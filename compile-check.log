Unity Editor version:    2022.3.62f1 (4af31df58517)
Branch:                  2022.3/staging
Build type:              Release
Batch mode:              YES
macOS version:           Version 15.5 (Build 24F74)
Darwin version:          24.5.0
Architecture:            arm64
Running under Rosetta:   NO
Available memory:        16384 MB
[Licensing::Module] Trying to connect to existing licensing client channel...
[Licensing::IpcConnector] Successfully connected to: "LicenseClient-cafe" at "2025-06-25T14:26:00.837075Z"
[Licensing::Client] Code 10 while verifying Licensing Client signature (process Id: 47956, path: "/Applications/Unity Hub.app/Contents/Frameworks/UnityLicensingClient_V1.app/Contents/MacOS/Unity.Licensing.Client")
[Licensing::Module] LicensingClient has failed validation; ignoring
[Licensing::Client] Handshaking with LicensingClient:
  Version:                 1.17.0+aa6cfba
  Session Id:              1dc2184fa8294d21a397fbb26b46c812
  Correlation Id:          add1fd4bf48fcb70563aa0051c34e7a0
  External correlation Id: 1397084632265391553
  Machine Id:              SaO/+vEl19cOZCJ9iXNm5zgvkFY=
[Licensing::Module] Successfully connected to LicensingClient on channel: "LicenseClient-cafe" (connect: 0.00s, validation: 0.02s, handshake: 0.34s)
[Licensing::IpcConnector] Successfully connected to: "LicenseClient-cafe-notifications" at "2025-06-25T14:26:01.19296Z"
[Licensing::Module] Error: Access token is unavailable; failed to update
[Licensing::Client] Successfully updated license
[Licensing::Client] Successfully resolved entitlement details
[Licensing::Module] Serial number assigned to: "F4-2R2W-WK36-W2FA-CS3P-XXXX"
[Licensing::Module] Serial number assigned to: "1375833638986-UnityPersXXXX"
Pro License: NO
[Licensing::Client] Successfully resolved entitlement details
[Licensing::Module] Serial number assigned to: "F4-2R2W-WK36-W2FA-CS3P-XXXX"
[Licensing::Module] Serial number assigned to: "1375833638986-UnityPersXXXX"
Launching external process: /Applications/Unity/Hub/Editor/2022.3.62f1/Unity.app/Contents/Resources/PackageManager/Server/UnityPackageManager

COMMAND LINE ARGUMENTS:
/Applications/Unity/Hub/Editor/2022.3.62f1/Unity.app/Contents/MacOS/Unity
-batchmode
-quit
-projectPath
/Users/<USER>/Documents/GitHub/PerfumerProject
-logFile
/Users/<USER>/Documents/GitHub/PerfumerProject/compile-check.log
-executeMethod
UnityEditor.EditorApplication.Exit
Successfully changed project path to: /Users/<USER>/Documents/GitHub/PerfumerProject
/Users/<USER>/Documents/GitHub/PerfumerProject
It looks like another Unity instance is running with this project open.

Multiple Unity instances cannot open the same project.

Project: /Users/<USER>/Documents/GitHub/PerfumerProject
Fatal Error! It looks like another Unity instance is running with this project open.

Multiple Unity instances cannot open the same project.

Project: /Users/<USER>/Documents/GitHub/PerfumerProject
[Package Manager] Server::Kill -- Server was shutdown
