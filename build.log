Unity Editor version:    2022.3.62f1 (4af31df58517)
Branch:                  2022.3/staging
Build type:              Release
Batch mode:              YES
macOS version:           Version 15.5 (Build 24F74)
Darwin version:          24.5.0
Architecture:            arm64
Running under Rosetta:   NO
Available memory:        16384 MB
[Licensing::Module] Trying to connect to existing licensing client channel...
[Licensing::IpcConnector] Successfully connected to: "LicenseClient-cafe" at "2025-06-25T08:46:25.921319Z"
[Licensing::Client] Code 10 while verifying Licensing Client signature (process Id: 47956, path: "/Applications/Unity Hub.app/Contents/Frameworks/UnityLicensingClient_V1.app/Contents/MacOS/Unity.Licensing.Client")
[Licensing::Module] LicensingClient has failed validation; ignoring
[Licensing::Client] Handshaking with LicensingClient:
  Version:                 1.17.0+aa6cfba
  Session Id:              ec8fe8d5a0664771b9137da2ead81308
  Correlation Id:          add1fd4bf48fcb70563aa0051c34e7a0
  External correlation Id: 2709437120080564086
  Machine Id:              SaO/+vEl19cOZCJ9iXNm5zgvkFY=
[Licensing::Module] Successfully connected to LicensingClient on channel: "LicenseClient-cafe" (connect: 0.00s, validation: 0.01s, handshake: 0.23s)
[Licensing::IpcConnector] Successfully connected to: "LicenseClient-cafe-notifications" at "2025-06-25T08:46:26.166082Z"
[Licensing::Module] Error: Access token is unavailable; failed to update
[Licensing::Client] Successfully updated license
[Licensing::Client] Successfully resolved entitlement details
[Licensing::Module] Serial number assigned to: "F4-2R2W-WK36-W2FA-CS3P-XXXX"
[Licensing::Module] Serial number assigned to: "1375833638986-UnityPersXXXX"
Pro License: NO
[Licensing::Client] Successfully resolved entitlement details
[Licensing::Module] Serial number assigned to: "F4-2R2W-WK36-W2FA-CS3P-XXXX"
[Licensing::Module] Serial number assigned to: "1375833638986-UnityPersXXXX"
Launching external process: /Applications/Unity/Hub/Editor/2022.3.62f1/Unity.app/Contents/Resources/PackageManager/Server/UnityPackageManager

COMMAND LINE ARGUMENTS:
/Applications/Unity/Hub/Editor/2022.3.62f1/Unity.app/Contents/MacOS/Unity
-batchmode
-quit
-projectPath
/Users/<USER>/Documents/GitHub/PerfumerProject
-logFile
/Users/<USER>/Documents/GitHub/PerfumerProject/build.log
-executeMethod
BuildTools.CommandLineBuild.BuildLinux
Successfully changed project path to: /Users/<USER>/Documents/GitHub/PerfumerProject
/Users/<USER>/Documents/GitHub/PerfumerProject
[UnityMemory] Configuration Parameters - Can be set up in boot.config
    "memorysetup-bucket-allocator-granularity=16"
    "memorysetup-bucket-allocator-bucket-count=8"
    "memorysetup-bucket-allocator-block-size=33554432"
    "memorysetup-bucket-allocator-block-count=8"
    "memorysetup-main-allocator-block-size=16777216"
    "memorysetup-thread-allocator-block-size=16777216"
    "memorysetup-gfx-main-allocator-block-size=16777216"
    "memorysetup-gfx-thread-allocator-block-size=16777216"
    "memorysetup-cache-allocator-block-size=4194304"
    "memorysetup-typetree-allocator-block-size=2097152"
    "memorysetup-profiler-bucket-allocator-granularity=16"
    "memorysetup-profiler-bucket-allocator-bucket-count=8"
    "memorysetup-profiler-bucket-allocator-block-size=33554432"
    "memorysetup-profiler-bucket-allocator-block-count=8"
    "memorysetup-profiler-allocator-block-size=16777216"
    "memorysetup-profiler-editor-allocator-block-size=1048576"
    "memorysetup-temp-allocator-size-main=16777216"
    "memorysetup-job-temp-allocator-block-size=2097152"
    "memorysetup-job-temp-allocator-block-size-background=1048576"
    "memorysetup-job-temp-allocator-reduction-small-platforms=262144"
    "memorysetup-allocator-temp-initial-block-size-main=262144"
    "memorysetup-allocator-temp-initial-block-size-worker=262144"
    "memorysetup-temp-allocator-size-background-worker=32768"
    "memorysetup-temp-allocator-size-job-worker=262144"
    "memorysetup-temp-allocator-size-preload-manager=33554432"
    "memorysetup-temp-allocator-size-nav-mesh-worker=65536"
    "memorysetup-temp-allocator-size-audio-worker=65536"
    "memorysetup-temp-allocator-size-cloud-worker=32768"
    "memorysetup-temp-allocator-size-gi-baking-worker=262144"
    "memorysetup-temp-allocator-size-gfx=262144"
Player connection [8499977984]  Target information:

Player connection [8499977984]  * "[IP] ************** [Port] 55504 [Flags] 2 [Guid] 3907185869 [EditorId] 3907185869 [Version] 1048832 [Id] OSXEditor(0,cafedeMBP) [Debug] 1 [PackageName] OSXEditor [ProjectName] Editor" 

Player connection [8499977984] Host joined multi-casting on [***********:54997]...
Player connection [8499977984] Host joined alternative multi-casting on [***********:34997]...
Input System module state changed to: Initialized.
[PhysX] Initialized MultithreadedTaskDispatcher with 8 workers.
[Package Manager] UpmClient::Connect -- Connected to IPC stream "Upm-54870" after 0.4 seconds.
[Package Manager] Restoring resolved packages state from cache
[Licensing::Client] Successfully resolved entitlement details
[Package Manager] Registered 53 packages:
  Packages from [https://packages.unity.com]:
    com.unity.nuget.newtonsoft-json@3.2.1 (location: /Users/<USER>/Documents/GitHub/PerfumerProject/Library/PackageCache/com.unity.nuget.newtonsoft-json@3.2.1)
    com.unity.collab-proxy@2.7.1 (location: /Users/<USER>/Documents/GitHub/PerfumerProject/Library/PackageCache/com.unity.collab-proxy@2.7.1)
    com.unity.inputsystem@1.14.0 (location: /Users/<USER>/Documents/GitHub/PerfumerProject/Library/PackageCache/com.unity.inputsystem@1.14.0)
    com.unity.textmeshpro@3.0.7 (location: /Users/<USER>/Documents/GitHub/PerfumerProject/Library/PackageCache/com.unity.textmeshpro@3.0.7)
    com.unity.timeline@1.7.7 (location: /Users/<USER>/Documents/GitHub/PerfumerProject/Library/PackageCache/com.unity.timeline@1.7.7)
    com.unity.toolchain.macos-arm64-linux-x86_64@2.0.4 (location: /Users/<USER>/Documents/GitHub/PerfumerProject/Library/PackageCache/com.unity.toolchain.macos-arm64-linux-x86_64@2.0.4)
    com.unity.visualscripting@1.9.4 (location: /Users/<USER>/Documents/GitHub/PerfumerProject/Library/PackageCache/com.unity.visualscripting@1.9.4)
    com.unity.sysroot@2.0.10 (location: /Users/<USER>/Documents/GitHub/PerfumerProject/Library/PackageCache/com.unity.sysroot@2.0.10)
    com.unity.sysroot.linux-x86_64@2.0.9 (location: /Users/<USER>/Documents/GitHub/PerfumerProject/Library/PackageCache/com.unity.sysroot.linux-x86_64@2.0.9)
    com.unity.ide.visualstudio@2.0.22 (location: /Users/<USER>/Documents/GitHub/PerfumerProject/Library/PackageCache/com.unity.ide.visualstudio@2.0.22)
    com.unity.ide.rider@3.0.36 (location: /Users/<USER>/Documents/GitHub/PerfumerProject/Library/PackageCache/com.unity.ide.rider@3.0.36)
    com.unity.ide.vscode@1.2.5 (location: /Users/<USER>/Documents/GitHub/PerfumerProject/Library/PackageCache/com.unity.ide.vscode@1.2.5)
    com.unity.editorcoroutines@1.0.0 (location: /Users/<USER>/Documents/GitHub/PerfumerProject/Library/PackageCache/com.unity.editorcoroutines@1.0.0)
    com.unity.performance.profile-analyzer@1.2.3 (location: /Users/<USER>/Documents/GitHub/PerfumerProject/Library/PackageCache/com.unity.performance.profile-analyzer@1.2.3)
    com.unity.test-framework@1.1.33 (location: /Users/<USER>/Documents/GitHub/PerfumerProject/Library/PackageCache/com.unity.test-framework@1.1.33)
    com.unity.testtools.codecoverage@1.2.6 (location: /Users/<USER>/Documents/GitHub/PerfumerProject/Library/PackageCache/com.unity.testtools.codecoverage@1.2.6)
    com.unity.settings-manager@2.1.0 (location: /Users/<USER>/Documents/GitHub/PerfumerProject/Library/PackageCache/com.unity.settings-manager@2.1.0)
    com.unity.ext.nunit@1.0.6 (location: /Users/<USER>/Documents/GitHub/PerfumerProject/Library/PackageCache/com.unity.ext.nunit@1.0.6)
  Built-in packages:
    com.unity.2d.sprite@1.0.0 (location: /Users/<USER>/Documents/GitHub/PerfumerProject/Library/PackageCache/com.unity.2d.sprite@1.0.0)
    com.unity.feature.development@1.0.1 (location: /Users/<USER>/Documents/GitHub/PerfumerProject/Library/PackageCache/com.unity.feature.development@1.0.1)
    com.unity.ugui@1.0.0 (location: /Users/<USER>/Documents/GitHub/PerfumerProject/Library/PackageCache/com.unity.ugui@1.0.0)
    com.unity.modules.ai@1.0.0 (location: /Users/<USER>/Documents/GitHub/PerfumerProject/Library/PackageCache/com.unity.modules.ai@1.0.0)
    com.unity.modules.androidjni@1.0.0 (location: /Users/<USER>/Documents/GitHub/PerfumerProject/Library/PackageCache/com.unity.modules.androidjni@1.0.0)
    com.unity.modules.animation@1.0.0 (location: /Users/<USER>/Documents/GitHub/PerfumerProject/Library/PackageCache/com.unity.modules.animation@1.0.0)
    com.unity.modules.assetbundle@1.0.0 (location: /Users/<USER>/Documents/GitHub/PerfumerProject/Library/PackageCache/com.unity.modules.assetbundle@1.0.0)
    com.unity.modules.audio@1.0.0 (location: /Users/<USER>/Documents/GitHub/PerfumerProject/Library/PackageCache/com.unity.modules.audio@1.0.0)
    com.unity.modules.cloth@1.0.0 (location: /Users/<USER>/Documents/GitHub/PerfumerProject/Library/PackageCache/com.unity.modules.cloth@1.0.0)
    com.unity.modules.director@1.0.0 (location: /Users/<USER>/Documents/GitHub/PerfumerProject/Library/PackageCache/com.unity.modules.director@1.0.0)
    com.unity.modules.imageconversion@1.0.0 (location: /Users/<USER>/Documents/GitHub/PerfumerProject/Library/PackageCache/com.unity.modules.imageconversion@1.0.0)
    com.unity.modules.imgui@1.0.0 (location: /Users/<USER>/Documents/GitHub/PerfumerProject/Library/PackageCache/com.unity.modules.imgui@1.0.0)
    com.unity.modules.jsonserialize@1.0.0 (location: /Users/<USER>/Documents/GitHub/PerfumerProject/Library/PackageCache/com.unity.modules.jsonserialize@1.0.0)
    com.unity.modules.particlesystem@1.0.0 (location: /Users/<USER>/Documents/GitHub/PerfumerProject/Library/PackageCache/com.unity.modules.particlesystem@1.0.0)
    com.unity.modules.physics@1.0.0 (location: /Users/<USER>/Documents/GitHub/PerfumerProject/Library/PackageCache/com.unity.modules.physics@1.0.0)
    com.unity.modules.physics2d@1.0.0 (location: /Users/<USER>/Documents/GitHub/PerfumerProject/Library/PackageCache/com.unity.modules.physics2d@1.0.0)
    com.unity.modules.screencapture@1.0.0 (location: /Users/<USER>/Documents/GitHub/PerfumerProject/Library/PackageCache/com.unity.modules.screencapture@1.0.0)
    com.unity.modules.terrain@1.0.0 (location: /Users/<USER>/Documents/GitHub/PerfumerProject/Library/PackageCache/com.unity.modules.terrain@1.0.0)
    com.unity.modules.terrainphysics@1.0.0 (location: /Users/<USER>/Documents/GitHub/PerfumerProject/Library/PackageCache/com.unity.modules.terrainphysics@1.0.0)
    com.unity.modules.tilemap@1.0.0 (location: /Users/<USER>/Documents/GitHub/PerfumerProject/Library/PackageCache/com.unity.modules.tilemap@1.0.0)
    com.unity.modules.ui@1.0.0 (location: /Users/<USER>/Documents/GitHub/PerfumerProject/Library/PackageCache/com.unity.modules.ui@1.0.0)
    com.unity.modules.uielements@1.0.0 (location: /Users/<USER>/Documents/GitHub/PerfumerProject/Library/PackageCache/com.unity.modules.uielements@1.0.0)
    com.unity.modules.umbra@1.0.0 (location: /Users/<USER>/Documents/GitHub/PerfumerProject/Library/PackageCache/com.unity.modules.umbra@1.0.0)
    com.unity.modules.unityanalytics@1.0.0 (location: /Users/<USER>/Documents/GitHub/PerfumerProject/Library/PackageCache/com.unity.modules.unityanalytics@1.0.0)
    com.unity.modules.unitywebrequest@1.0.0 (location: /Users/<USER>/Documents/GitHub/PerfumerProject/Library/PackageCache/com.unity.modules.unitywebrequest@1.0.0)
    com.unity.modules.unitywebrequestassetbundle@1.0.0 (location: /Users/<USER>/Documents/GitHub/PerfumerProject/Library/PackageCache/com.unity.modules.unitywebrequestassetbundle@1.0.0)
    com.unity.modules.unitywebrequestaudio@1.0.0 (location: /Users/<USER>/Documents/GitHub/PerfumerProject/Library/PackageCache/com.unity.modules.unitywebrequestaudio@1.0.0)
    com.unity.modules.unitywebrequesttexture@1.0.0 (location: /Users/<USER>/Documents/GitHub/PerfumerProject/Library/PackageCache/com.unity.modules.unitywebrequesttexture@1.0.0)
    com.unity.modules.unitywebrequestwww@1.0.0 (location: /Users/<USER>/Documents/GitHub/PerfumerProject/Library/PackageCache/com.unity.modules.unitywebrequestwww@1.0.0)
    com.unity.modules.vehicles@1.0.0 (location: /Users/<USER>/Documents/GitHub/PerfumerProject/Library/PackageCache/com.unity.modules.vehicles@1.0.0)
    com.unity.modules.video@1.0.0 (location: /Users/<USER>/Documents/GitHub/PerfumerProject/Library/PackageCache/com.unity.modules.video@1.0.0)
    com.unity.modules.vr@1.0.0 (location: /Users/<USER>/Documents/GitHub/PerfumerProject/Library/PackageCache/com.unity.modules.vr@1.0.0)
    com.unity.modules.wind@1.0.0 (location: /Users/<USER>/Documents/GitHub/PerfumerProject/Library/PackageCache/com.unity.modules.wind@1.0.0)
    com.unity.modules.xr@1.0.0 (location: /Users/<USER>/Documents/GitHub/PerfumerProject/Library/PackageCache/com.unity.modules.xr@1.0.0)
    com.unity.modules.subsystems@1.0.0 (location: /Users/<USER>/Documents/GitHub/PerfumerProject/Library/PackageCache/com.unity.modules.subsystems@1.0.0)
[Subsystems] No new subsystems found in resolved package list.
Package Manager log level set to [2]
[Package Manager] Done registering packages in 0.11 seconds
Refreshing native plugins compatible for Editor in 4.94 ms, found 2 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Initialize engine version: 2022.3.62f1 (4af31df58517)
[Subsystems] Discovering subsystems at path /Applications/Unity/Hub/Editor/2022.3.62f1/Unity.app/Contents/Resources/UnitySubsystems
[Subsystems] Discovering subsystems at path /Users/<USER>/Documents/GitHub/PerfumerProject/Assets
GfxDevice: creating device client; threaded=0; jobified=0
 preferred device: Apple M1 (high power)
Metal devices available: 1
0: Apple M1 (high power)
Using device Apple M1 (high power)
Initializing Metal device caps: Apple M1
[Licensing::Client] Successfully resolved entitlement details
[Licensing::Module] Serial number assigned to: "F4-2R2W-WK36-W2FA-CS3P-XXXX"
[Licensing::Module] Serial number assigned to: "1375833638986-UnityPersXXXX"
Initialize mono
Mono path[0] = '/Applications/Unity/Hub/Editor/2022.3.62f1/Unity.app/Contents/Managed'
Mono path[1] = '/Applications/Unity/Hub/Editor/2022.3.62f1/Unity.app/Contents/MonoBleedingEdge/lib/mono/unityjit-macos'
Mono config path = '/Applications/Unity/Hub/Editor/2022.3.62f1/Unity.app/Contents/MonoBleedingEdge/etc'
Using monoOptions --debugger-agent=transport=dt_socket,embedding=1,server=y,suspend=n,address=127.0.0.1:56870
Using cacheserver namespaces - metadata:defaultmetadata, artifacts:defaultartifacts
Using cacheserver namespaces - metadata:defaultmetadata, artifacts:defaultartifacts
ImportWorker Server TCP listen port: 0
Begin MonoManager ReloadAssembly
Registering precompiled unity dll's ...
Register platform support module: /Applications/Unity/Hub/Editor/2022.3.62f1/Unity.app/Contents/PlaybackEngines/MacStandaloneSupport/UnityEditor.OSXStandalone.Extensions.dll
[Licensing::Client] Successfully resolved entitlement details
[Licensing::Module] Serial number assigned to: "F4-2R2W-WK36-W2FA-CS3P-XXXX"
[Licensing::Module] Serial number assigned to: "1375833638986-UnityPersXXXX"
Register platform support module: /Applications/Unity/Hub/Editor/2022.3.62f1/PlaybackEngines/AndroidPlayer/UnityEditor.Android.Extensions.dll
[Licensing::Client] Successfully resolved entitlement details
[Licensing::Module] Serial number assigned to: "F4-2R2W-WK36-W2FA-CS3P-XXXX"
[Licensing::Module] Serial number assigned to: "1375833638986-UnityPersXXXX"
Register platform support module: /Applications/Unity/Hub/Editor/2022.3.62f1/PlaybackEngines/iOSSupport/UnityEditor.iOS.Extensions.dll
Register platform support module: /Applications/Unity/Hub/Editor/2022.3.62f1/PlaybackEngines/LinuxStandaloneSupport/UnityEditor.LinuxStandalone.Extensions.dll
Registered in 0.027145 seconds.
- Loaded All Assemblies, in  0.220 seconds
Native extension for LinuxStandalone target not found
[usbmuxd] Start listen thread
[usbmuxd] Listen thread started
[usbmuxd] Send listen message
Native extension for iOS target not found
Native extension for Android target not found
Android Extension - Scanning For ADB Devices 158 ms
Native extension for OSXStandalone target not found
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  0.393 seconds
Domain Reload Profiling: 614ms
	BeginReloadAssembly (44ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (0ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (1ms)
	RebuildCommonClasses (21ms)
	RebuildNativeTypeToScriptingClass (8ms)
	initialDomainReloadingComplete (55ms)
	LoadAllAssembliesAndSetupDomain (92ms)
		LoadAssemblies (44ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (91ms)
			TypeCache.Refresh (90ms)
				TypeCache.ScanAssembly (82ms)
			ScanForSourceGeneratedMonoScriptInfo (0ms)
			ResolveRequiredComponents (0ms)
	FinalizeReload (393ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (365ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (269ms)
			SetLoadedEditorAssemblies (5ms)
			RefreshPlugins (0ms)
			BeforeProcessingInitializeOnLoad (2ms)
			ProcessInitializeOnLoadAttributes (68ms)
			ProcessInitializeOnLoadMethodAttributes (22ms)
			AfterProcessingInitializeOnLoad (0ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (0ms)
[Licensing::Client] Successfully resolved entitlement details
[Licensing::Module] Serial number assigned to: "F4-2R2W-WK36-W2FA-CS3P-XXXX"
[Licensing::Module] Serial number assigned to: "1375833638986-UnityPersXXXX"
Application.AssetDatabase Initial Refresh Start
The GUID inside 'Assets/FlexReader/Manual.pdf.meta' cannot be extracted by the YAML Parser. Attempting to extract it via string matching instead. Please verify the file does not contain unexpected data.
The GUID inside 'Assets/Resources/Prefabs/UI/Item_Sell.prefab.meta' cannot be extracted by the YAML Parser. Attempting to extract it via string matching instead. Please verify the file does not contain unexpected data.
The GUID inside 'Assets/Scenes/Sell.unity.meta' cannot be extracted by the YAML Parser. Attempting to extract it via string matching instead. Please verify the file does not contain unexpected data.
The GUID inside 'Assets/StreamingAssets/PerfumerData.xlsx.meta' cannot be extracted by the YAML Parser. Attempting to extract it via string matching instead. Please verify the file does not contain unexpected data.
[40m[32minfo[39m[22m[49m: Microsoft.Hosting.Lifetime[14]
      Now listening on: http://unix:/tmp/ilpp.sock-eae4890779ba5942efbfb0ce14813848
[40m[32minfo[39m[22m[49m: Microsoft.Hosting.Lifetime[0]
      Application started. Press Ctrl+C to shut down.
[40m[32minfo[39m[22m[49m: Microsoft.Hosting.Lifetime[0]
      Hosting environment: Production
[40m[32minfo[39m[22m[49m: Microsoft.Hosting.Lifetime[0]
      Content root path: /Users/<USER>/Documents/GitHub/PerfumerProject/
[40m[32minfo[39m[22m[49m: Microsoft.AspNetCore.Hosting.Diagnostics[1]
      Request starting HTTP/2 POST http://ilpp/UnityILPP.PostProcessing/Ping application/grpc -
[40m[32minfo[39m[22m[49m: Microsoft.AspNetCore.Routing.EndpointMiddleware[0]
      Executing endpoint 'gRPC - /UnityILPP.PostProcessing/Ping'
[40m[32minfo[39m[22m[49m: Microsoft.AspNetCore.Routing.EndpointMiddleware[1]
      Executed endpoint 'gRPC - /UnityILPP.PostProcessing/Ping'
[40m[32minfo[39m[22m[49m: Microsoft.AspNetCore.Hosting.Diagnostics[2]
      Request finished HTTP/2 POST http://ilpp/UnityILPP.PostProcessing/Ping application/grpc - - 200 - application/grpc 25.0223ms
Starting: /Applications/Unity/Hub/Editor/2022.3.62f1/Unity.app/Contents/bee_backend --ipc --defer-dag-verification --dagfile="Library/Bee/200b0aE.dag" --continue-on-failure --profile="Library/Bee/backend1.traceevents" ScriptAssemblies
WorkingDir: /Users/<USER>/Documents/GitHub/PerfumerProject
DisplayProgressbar: Compiling Scripts
ExitCode: 0 Duration: 0s83ms
*** Tundra build success (0.07 seconds), 0 items updated, 510 evaluated
Total cache size ********
Total cache size after purge ********
AssetDatabase: script compilation time: 0.531064s
Begin MonoManager ReloadAssembly
Symbol file LoadedFromMemory is not a mono symbol file
- Loaded All Assemblies, in  0.384 seconds
The GUID inside 'Assets/FlexReader/Manual.pdf.meta' cannot be extracted by the YAML Parser. Attempting to extract it via string matching instead. Please verify the file does not contain unexpected data.
The GUID inside 'Assets/Resources/Prefabs/UI/Item_Sell.prefab.meta' cannot be extracted by the YAML Parser. Attempting to extract it via string matching instead. Please verify the file does not contain unexpected data.
The GUID inside 'Assets/Scenes/Sell.unity.meta' cannot be extracted by the YAML Parser. Attempting to extract it via string matching instead. Please verify the file does not contain unexpected data.
The GUID inside 'Assets/StreamingAssets/PerfumerData.xlsx.meta' cannot be extracted by the YAML Parser. Attempting to extract it via string matching instead. Please verify the file does not contain unexpected data.
Refreshing native plugins compatible for Editor in 1.27 ms, found 2 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
The GUID inside 'Assets/FlexReader/Manual.pdf.meta' cannot be extracted by the YAML Parser. Attempting to extract it via string matching instead. Please verify the file does not contain unexpected data.
The GUID inside 'Assets/Resources/Prefabs/UI/Item_Sell.prefab.meta' cannot be extracted by the YAML Parser. Attempting to extract it via string matching instead. Please verify the file does not contain unexpected data.
The GUID inside 'Assets/Scenes/Sell.unity.meta' cannot be extracted by the YAML Parser. Attempting to extract it via string matching instead. Please verify the file does not contain unexpected data.
The GUID inside 'Assets/StreamingAssets/PerfumerData.xlsx.meta' cannot be extracted by the YAML Parser. Attempting to extract it via string matching instead. Please verify the file does not contain unexpected data.
Refreshing native plugins compatible for Editor in 1.26 ms, found 2 plugins.
Native extension for LinuxStandalone target not found
Native extension for iOS target not found
Native extension for Android target not found
Native extension for OSXStandalone target not found
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  0.429 seconds
Domain Reload Profiling: 813ms
	BeginReloadAssembly (75ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (3ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (21ms)
	RebuildCommonClasses (22ms)
	RebuildNativeTypeToScriptingClass (8ms)
	initialDomainReloadingComplete (15ms)
	LoadAllAssembliesAndSetupDomain (265ms)
		LoadAssemblies (153ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (145ms)
			TypeCache.Refresh (126ms)
				TypeCache.ScanAssembly (113ms)
			ScanForSourceGeneratedMonoScriptInfo (10ms)
			ResolveRequiredComponents (9ms)
	FinalizeReload (429ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (293ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (33ms)
			SetLoadedEditorAssemblies (2ms)
			RefreshPlugins (0ms)
			BeforeProcessingInitializeOnLoad (39ms)
			ProcessInitializeOnLoadAttributes (206ms)
			ProcessInitializeOnLoadMethodAttributes (12ms)
			AfterProcessingInitializeOnLoad (1ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (2ms)
The GUID inside 'Assets/FlexReader/Manual.pdf.meta' cannot be extracted by the YAML Parser. Attempting to extract it via string matching instead. Please verify the file does not contain unexpected data.
The GUID inside 'Assets/Resources/Prefabs/UI/Item_Sell.prefab.meta' cannot be extracted by the YAML Parser. Attempting to extract it via string matching instead. Please verify the file does not contain unexpected data.
The GUID inside 'Assets/Scenes/Sell.unity.meta' cannot be extracted by the YAML Parser. Attempting to extract it via string matching instead. Please verify the file does not contain unexpected data.
The GUID inside 'Assets/StreamingAssets/PerfumerData.xlsx.meta' cannot be extracted by the YAML Parser. Attempting to extract it via string matching instead. Please verify the file does not contain unexpected data.
Asset Pipeline Refresh (id=183c8d52d61374d4281f9de9daa50c11): Total: 2.666 seconds - Initiated by InitialRefreshV2(ForceSynchronousImport)
	Summary:
		Imports: total=0 (actual=0, local cache=0, cache server=0)
		Asset DB Process Time: managed=0 ms, native=1262 ms
		Asset DB Callback time: managed=8 ms, native=0 ms
		Scripting: domain reloads=1, domain reload time=840 ms, compile time=532 ms, other=21 ms
		Project Asset Count: scripts=5485, non-scripts=1510
		Asset File Changes: new=0, changed=0, moved=0, deleted=0
		Scan Filter Count: 1
	InvokeBeforeRefreshCallbacks: 0.134ms
	ApplyChangesToAssetFolders: 0.044ms
	Scan: 209.460ms
	OnSourceAssetsModified: 0.001ms
	GetAllGuidsForCategorization: 0.446ms
	CategorizeAssets: 94.676ms
	ImportOutOfDateAssets: 449.910ms (-96.271ms without children)
		CompileScripts: 532.297ms
		ReloadNativeAssets: 0.004ms
		UnloadImportedAssets: 11.567ms
		EnsureUptoDateAssetsAreRegisteredWithGuidPM: 1.646ms
		InitializingProgressBar: 0.000ms
		PostProcessAllAssetNotificationsAddChangedAssets: 0.000ms
		OnDemandSchedulerStart: 0.667ms
	PostProcessAllAssets: 8.691ms
	GatherAllCurrentPrimaryArtifactRevisions: 0.003ms
	UnloadStreamsBegin: 0.336ms
	PersistCurrentRevisions: 0.188ms
	UnloadStreamsEnd: 0.016ms
	GenerateScriptTypeHashes: 1.839ms
	Untracked: 1901.981ms
Application.AssetDatabase Initial Refresh End
Launching external process: /Applications/Unity/Hub/Editor/2022.3.62f1/Unity.app/Contents/Tools/UnityShaderCompiler
Launched and connected shader compiler UnityShaderCompiler after 0.03 seconds
Scanning for USB devices : 0.028ms
Initializing Unity extensions:
[Licensing::Client] Successfully resolved entitlement details
[Licensing::Module] Serial number assigned to: "F4-2R2W-WK36-W2FA-CS3P-XXXX"
[Licensing::Module] Serial number assigned to: "1375833638986-UnityPersXXXX"
[MODES] ModeService[none].Initialize
[MODES] ModeService[none].LoadModes
[MODES] Loading mode Default (0) for mode-current-id-香域
2025-06-25 16:46:36.045 Unity[54870:759090] NSEventModifierFlagFunction specified to -setKeyEquivalentModifierMask: for item <NSMenuItem: 0x31dd6f050 Font Asset, ke='Command-F12'>, but is only supported for system-provided menu items; will not be used
[PhysX] Initialized MultithreadedTaskDispatcher with 8 workers.
ProgressiveSceneManager::Cancel()
-- Listing OpenCL platforms(s) --
 * OpenCL platform 0
	PROFILE = FULL_PROFILE
	VERSION = OpenCL 1.2 (Apr 18 2025 21:46:03)
	NAME = Apple
	VENDOR = Apple
-- Listing OpenCL device(s) --
 * OpenCL platform 0, device 0 
	DEVICE_TYPE = 4
	DEVICE_NAME = Apple M1
	DEVICE_VENDOR = Apple
	DEVICE_VERSION = OpenCL 1.2 
	DRIVER_VERSION = 1.2 1.0
	DEVICE_MAX_COMPUTE_UNITS = 8
	DEVICE_MAX_CLOCK_FREQUENCY = 1000
	CL_DEVICE_MAX_CONSTANT_BUFFER_SIZE = 1073741824
	CL_DEVICE_HOST_UNIFIED_MEMORY = true
	CL_DEVICE_MAX_MEM_ALLOC_SIZE = 2147483648
	DEVICE_GLOBAL_MEM_SIZE = 11453251584
-- GPU Progressive lightmapper will use OpenCL device 'Apple M1' from 'Apple'--
   use -OpenCL-PlatformAndDeviceIndices <platformIdx> <deviceIdx> as command line arguments if you want to select a specific adapter for OpenCL.
Unloading 5 Unused Serialized files (Serialized files now loaded: 0)
Unloading 69 unused Assets / (481.2 KB). Loaded Objects now: 5068.
Memory consumption went from 134.9 MB to 134.4 MB.
Total: 3.051542 ms (FindLiveObjects: 0.139042 ms CreateObjectMapping: 0.057375 ms MarkObjects: 2.482625 ms  DeleteObjects: 0.372125 ms)

Building for linux...
UnityEngine.StackTraceUtility:ExtractStackTrace () (at /Users/<USER>/build/output/unity/unity/Runtime/Export/Scripting/StackTrace.cs:37)
UnityEngine.DebugLogHandler:LogFormat (UnityEngine.LogType,UnityEngine.Object,string,object[])
UnityEngine.Logger:Log (UnityEngine.LogType,object)
UnityEngine.Debug:Log (object)
BuildTools.CommandLineBuild:BuildForTarget (UnityEditor.BuildTarget,string,bool,bool,string) (at Assets/Editor/CommandLineBuild.cs:212)
BuildTools.CommandLineBuild:BuildSinglePlatform (string,bool,bool,string) (at Assets/Editor/CommandLineBuild.cs:200)
BuildTools.CommandLineBuild:BuildPlatformFromCommandLine (string) (at Assets/Editor/CommandLineBuild.cs:116)
BuildTools.CommandLineBuild:BuildLinux () (at Assets/Editor/CommandLineBuild.cs:99)

(Filename: Assets/Editor/CommandLineBuild.cs Line: 212)

Updated player settings - Version: 1.0.0, Build: 1
UnityEngine.StackTraceUtility:ExtractStackTrace () (at /Users/<USER>/build/output/unity/unity/Runtime/Export/Scripting/StackTrace.cs:37)
UnityEngine.DebugLogHandler:LogFormat (UnityEngine.LogType,UnityEngine.Object,string,object[])
UnityEngine.Logger:Log (UnityEngine.LogType,object)
UnityEngine.Debug:Log (object)
BuildTools.CommandLineBuild:UpdatePlayerSettingsForBuild () (at Assets/Editor/CommandLineBuild.cs:313)
BuildTools.CommandLineBuild:BuildForTarget (UnityEditor.BuildTarget,string,bool,bool,string) (at Assets/Editor/CommandLineBuild.cs:253)
BuildTools.CommandLineBuild:BuildSinglePlatform (string,bool,bool,string) (at Assets/Editor/CommandLineBuild.cs:200)
BuildTools.CommandLineBuild:BuildPlatformFromCommandLine (string) (at Assets/Editor/CommandLineBuild.cs:116)
BuildTools.CommandLineBuild:BuildLinux () (at Assets/Editor/CommandLineBuild.cs:99)

(Filename: Assets/Editor/CommandLineBuild.cs Line: 313)

Start importing ProjectSettings/ProjectSettings.asset using Guid(00000000000000004000000000000000) Importer(-1,00000000000000000000000000000000) [PhysX] Initialized MultithreadedTaskDispatcher with 8 workers.
 -> (artifact id: 'e04c716c502ca73e530019d1be9fe778') in 0.003268 seconds
Refreshing native plugins compatible for Editor in 1.29 ms, found 2 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Asset Pipeline Refresh (id=0cad05099bd29464dbb1864e31cc8651): Total: 0.048 seconds - Initiated by StopAssetImportingV2(NoUpdateAssetOptions)
Error building Player: Currently selected scripting backend (Mono) is not installed.
Unloading 27 Unused Serialized files (Serialized files now loaded: 0)
Unloading 89 unused Assets / (20.9 MB). Loaded Objects now: 5077.
Memory consumption went from 196.3 MB to 175.4 MB.
Total: 5.043916 ms (FindLiveObjects: 0.144375 ms CreateObjectMapping: 0.059542 ms MarkObjects: 4.074583 ms  DeleteObjects: 0.765166 ms)

Build Finished, Result: Failure.
##utp:{"type":"PlayerBuildInfo","version":2,"phase":"Immediate","time":1750841196608,"processId":54870,"steps":[{"description":"Preprocess Player","duration":425},{"description":"Prepare For Build","duration":2}],"duration":427}
[PhysX] Initialized MultithreadedTaskDispatcher with 8 workers.
Unloading 0 Unused Serialized files (Serialized files now loaded: 0)
Loaded scene 'Temp/__Backupscenes/0.backup'
	Deserialize:            0.327 ms
	Integration:            4.867 ms
	Integration of assets:  0.001 ms
	Thread Wait Time:       0.040 ms
	Total Operation Time:   5.235 ms
Unloading 0 unused Assets / (0 B). Loaded Objects now: 5077.
Memory consumption went from 149.8 MB to 149.8 MB.
Total: 3.922834 ms (FindLiveObjects: 0.128416 ms CreateObjectMapping: 0.053375 ms MarkObjects: 3.735917 ms  DeleteObjects: 0.004875 ms)

Build failed for linux: linux build failed with result: Failed
UnityEngine.StackTraceUtility:ExtractStackTrace () (at /Users/<USER>/build/output/unity/unity/Runtime/Export/Scripting/StackTrace.cs:37)
UnityEngine.DebugLogHandler:LogFormat (UnityEngine.LogType,UnityEngine.Object,string,object[])
UnityEngine.Logger:Log (UnityEngine.LogType,object)
UnityEngine.Debug:LogError (object)
BuildTools.CommandLineBuild:BuildForTarget (UnityEditor.BuildTarget,string,bool,bool,string) (at Assets/Editor/CommandLineBuild.cs:271)
BuildTools.CommandLineBuild:BuildSinglePlatform (string,bool,bool,string) (at Assets/Editor/CommandLineBuild.cs:200)
BuildTools.CommandLineBuild:BuildPlatformFromCommandLine (string) (at Assets/Editor/CommandLineBuild.cs:116)
BuildTools.CommandLineBuild:BuildLinux () (at Assets/Editor/CommandLineBuild.cs:99)

(Filename: Assets/Editor/CommandLineBuild.cs Line: 271)

linux Build Failed: linux build failed with result: Failed
UnityEngine.StackTraceUtility:ExtractStackTrace () (at /Users/<USER>/build/output/unity/unity/Runtime/Export/Scripting/StackTrace.cs:37)
UnityEngine.DebugLogHandler:LogFormat (UnityEngine.LogType,UnityEngine.Object,string,object[])
UnityEngine.Logger:Log (UnityEngine.LogType,object)
UnityEngine.Debug:LogError (object)
BuildTools.CommandLineBuild:BuildPlatformFromCommandLine (string) (at Assets/Editor/CommandLineBuild.cs:121)
BuildTools.CommandLineBuild:BuildLinux () (at Assets/Editor/CommandLineBuild.cs:99)

(Filename: Assets/Editor/CommandLineBuild.cs Line: 121)

Curl error 42: Callback aborted
Killing ADB server in 0.033507 seconds.
Thread 0x3223d3000 may have been prematurely finalized
[PhysX] Initialized MultithreadedTaskDispatcher with 8 workers.
Input System module state changed to: ShutdownInProgress.
Input System polling thread exited.
Input System module state changed to: Shutdown.
[Licensing::IpcConnector] LicenseClient-cafe-notifications channel disconnected successfully.
[Licensing::IpcConnector] LicenseClient-cafe channel disconnected successfully.
Thread 0x16bd83000 may have been prematurely finalized
[40m[32minfo[39m[22m[49m: Microsoft.Hosting.Lifetime[0]
      Application is shutting down...
Cleanup mono
debugger-agent: Unable to listen on 48
[usbmuxd] Stop listen thread
[usbmuxd] Error: 
[usbmuxd] Listen thread exiting
[Performance] Application.InitializeProject                                                                                  :        1 samples, Peak.  4.37 s (1.0x), Avg.  4.37 s, Total. 4.365 s (40.0%)
[Performance] Application.PackageManager.StartServer                                                                         :        1 samples, Peak.  1.94 ms (1.0x), Avg.  1.94 ms, Total. 1.943 ms (0.0%)
[Performance] Application.AcquireProjectLock                                                                                 :        1 samples, Peak.  1.35 ms (1.0x), Avg.  1.35 ms, Total. 1.347 ms (0.0%)
[Performance] Application.InitializeEngineNoGraphics                                                                         :        1 samples, Peak.  29.4 ms (1.0x), Avg.  29.4 ms, Total. 29.36 ms (0.3%)
[Performance] Application.PackageManager.Initialize                                                                          :        1 samples, Peak.   430 ms (1.0x), Avg.   430 ms, Total. 430.1 ms (3.9%)
[Performance] Connecting to Package Manager                                                                                  :       44 samples, Peak.  52.0 us (31.0x), Avg.  1.67 us, Total. 73.66 us (0.0%)
[Performance] Application.EngineGraphics.Initialize                                                                          :        1 samples, Peak.  29.5 ms (1.0x), Avg.  29.5 ms, Total. 29.48 ms (0.3%)
[Performance] Application.GI.Initialize                                                                                      :        1 samples, Peak.   488 us (1.0x), Avg.   488 us, Total. 488.4 us (0.0%)
[Performance] Application.LoadAllDefaultResourcesFromEditor                                                                  :        1 samples, Peak.  1.03 ms (1.0x), Avg.  1.03 ms, Total. 1.030 ms (0.0%)
[Performance] Application.LoadMonoAssemblies                                                                                 :        1 samples, Peak.   620 ms (1.0x), Avg.   620 ms, Total. 620.4 ms (5.7%)
[Performance] RestoreManagedReferences                                                                                       :        2 samples, Peak.  36.5 ms (2.0x), Avg.  18.3 ms, Total. 36.53 ms (0.3%)
[Performance] InitializeOnLoad DrivenRectTransformUndo                                                                       :        2 samples, Peak.   395 us (1.2x), Avg.   321 us, Total. 641.5 us (0.0%)
[Performance] InitializeOnLoad SceneSearch                                                                                   :        2 samples, Peak.  93.7 us (1.1x), Avg.  82.6 us, Total. 165.3 us (0.0%)
[Performance] InitializeOnLoad CloudBuild                                                                                    :        2 samples, Peak.  3.12 us (1.1x), Avg.  2.79 us, Total. 5.584 us (0.0%)
[Performance] InitializeOnLoad NativeFormatImporterUtility                                                                   :        2 samples, Peak.  2.00 ms (1.4x), Avg.  1.47 ms, Total. 2.950 ms (0.0%)
[Performance] InitializeOnLoad SearchService                                                                                 :        2 samples, Peak.  85.5 us (1.1x), Avg.  78.2 us, Total. 156.4 us (0.0%)
[Performance] InitializeOnLoad SettingsService                                                                               :        2 samples, Peak.  7.12 us (1.0x), Avg.  6.87 us, Total. 13.75 us (0.0%)
[Performance] InitializeOnLoad WindowLayout                                                                                  :        2 samples, Peak.   180 us (1.2x), Avg.   155 us, Total. 310.0 us (0.0%)
[Performance] InitializeOnLoad AssetStoreContext                                                                             :        2 samples, Peak.  12.4 ms (1.7x), Avg.  7.43 ms, Total. 14.87 ms (0.1%)
[Performance] InitializeOnLoad PoolManager                                                                                   :        2 samples, Peak.   445 us (1.1x), Avg.   398 us, Total. 795.3 us (0.0%)
[Performance] InitializeOnLoad MixerEffectDefinitionReloader                                                                 :        2 samples, Peak.  1.83 ms (1.3x), Avg.  1.42 ms, Total. 2.840 ms (0.0%)
[Performance] InitializeOnLoad ProjectSearch                                                                                 :        2 samples, Peak.  38.7 us (1.1x), Avg.  33.8 us, Total. 67.50 us (0.0%)
[Performance] InitializeOnLoad UISystemProfilerRenderService                                                                 :        2 samples, Peak.  7.54 us (1.1x), Avg.  6.79 us, Total. 13.58 us (0.0%)
[Performance] InitializeOnLoad EditMode                                                                                      :        2 samples, Peak.   306 us (1.2x), Avg.   250 us, Total. 499.0 us (0.0%)
[Performance] InitializeOnLoad UnityConnect                                                                                  :        2 samples, Peak.  37.6 us (1.2x), Avg.  31.9 us, Total. 63.79 us (0.0%)
[Performance] InitializeOnLoad ManagedDebugger                                                                               :        2 samples, Peak.   486 us (1.2x), Avg.   410 us, Total. 819.3 us (0.0%)
[Performance] InitializeOnLoad ShortcutIntegration                                                                           :        2 samples, Peak.  46.7 us (1.2x), Avg.  39.0 us, Total. 77.92 us (0.0%)
[Performance] InitializeOnLoad AddComponentWindow                                                                            :        2 samples, Peak.  89.4 us (1.1x), Avg.  78.5 us, Total. 157.0 us (0.0%)
[Performance] InitializeOnLoad ManagedDebuggerWindow                                                                         :        2 samples, Peak.  96.3 us (1.2x), Avg.  82.2 us, Total. 164.4 us (0.0%)
[Performance] InitializeOnLoad CacheServerWindow                                                                             :        2 samples, Peak.  7.17 us (1.0x), Avg.  7.15 us, Total. 14.29 us (0.0%)
[Performance] InitializeOnLoad PlayModeDownload                                                                              :        2 samples, Peak.  2.48 ms (2.0x), Avg.  1.27 ms, Total. 2.546 ms (0.0%)
[Performance] InitializeOnLoad MenuItems                                                                                     :        2 samples, Peak.  69.4 us (1.2x), Avg.  56.7 us, Total. 113.3 us (0.0%)
[Performance] InitializeOnLoad PrefabInstanceChangedListener                                                                 :        2 samples, Peak.  98.2 us (1.1x), Avg.  91.8 us, Total. 183.6 us (0.0%)
[Performance] InitializeOnLoad ObjectSelectorSearch                                                                          :        2 samples, Peak.  22.3 us (1.0x), Avg.  22.2 us, Total. 44.33 us (0.0%)
[Performance] InitializeOnLoad EditorMonitor                                                                                 :        2 samples, Peak.   433 us (1.1x), Avg.   396 us, Total. 792.3 us (0.0%)
[Performance] InitializeOnLoad RetainedMode                                                                                  :        2 samples, Peak.  1.84 ms (1.2x), Avg.  1.59 ms, Total. 3.177 ms (0.0%)
[Performance] InitializeOnLoad EditorShaderLoader                                                                            :        2 samples, Peak.   543 us (1.2x), Avg.   435 us, Total. 869.0 us (0.0%)
[Performance] InitializeOnLoad EditorDelegateRegistration                                                                    :        2 samples, Peak.   364 us (1.0x), Avg.   350 us, Total. 700.0 us (0.0%)
[Performance] InitializeOnLoad UIDocumentHierarchyWatcher                                                                    :        2 samples, Peak.  57.7 us (1.2x), Avg.  47.2 us, Total. 94.46 us (0.0%)
[Performance] InitializeOnLoad LiveReloadTrackerCreator                                                                      :        2 samples, Peak.   207 us (1.2x), Avg.   177 us, Total. 354.4 us (0.0%)
[Performance] InitializeOnLoad UxmlObjectEditorFactories                                                                     :        2 samples, Peak.   352 us (1.0x), Avg.   342 us, Total. 683.7 us (0.0%)
[Performance] InitializeOnLoad UXMLEditorFactories                                                                           :        2 samples, Peak.  34.5 ms (1.0x), Avg.  33.7 ms, Total. 67.46 ms (0.6%)
[Performance] InitializeOnLoad PurchasingService                                                                             :        2 samples, Peak.  3.91 ms (1.1x), Avg.  3.71 ms, Total. 7.422 ms (0.1%)
[Performance] InitializeOnLoad CloudBuildPoller                                                                              :        2 samples, Peak.  57.7 us (1.0x), Avg.  56.1 us, Total. 112.2 us (0.0%)
[Performance] InitializeOnLoad EditorGameServicesAnalytics                                                                   :        2 samples, Peak.   656 us (1.2x), Avg.   548 us, Total. 1.096 ms (0.0%)
[Performance] InitializeOnLoad AnalyticsService                                                                              :        2 samples, Peak.  98.4 us (1.0x), Avg.  96.6 us, Total. 193.2 us (0.0%)
[Performance] InitializeOnLoad BuildService                                                                                  :        2 samples, Peak.  84.4 us (1.0x), Avg.  82.9 us, Total. 165.8 us (0.0%)
[Performance] InitializeOnLoad AdsService                                                                                    :        2 samples, Peak.   224 us (1.0x), Avg.   222 us, Total. 444.2 us (0.0%)
[Performance] InitializeOnLoad UDPService                                                                                    :        2 samples, Peak.  80.7 us (1.0x), Avg.  80.6 us, Total. 161.3 us (0.0%)
[Performance] InitializeOnLoad CrashService                                                                                  :        2 samples, Peak.  83.9 us (1.0x), Avg.  82.3 us, Total. 164.6 us (0.0%)
[Performance] InitializeOnLoad ServicesRepository                                                                            :        2 samples, Peak.   209 ns (1.1x), Avg.   188 ns, Total. 376.0 ns (0.0%)
[Performance] InitializeOnLoad SearchWindow                                                                                  :        2 samples, Peak.  78.9 us (1.1x), Avg.  70.5 us, Total. 141.0 us (0.0%)
[Performance] InitializeOnLoad CustomIndexers                                                                                :        2 samples, Peak.  8.50 ms (1.0x), Avg.  8.30 ms, Total. 16.59 ms (0.2%)
[Performance] InitializeOnLoad SearchMonitor                                                                                 :        2 samples, Peak.   584 us (1.1x), Avg.   516 us, Total. 1.033 ms (0.0%)
[Performance] InitializeOnLoad ParameterControllerEditor                                                                     :        2 samples, Peak.  17.7 us (1.1x), Avg.  15.5 us, Total. 31.00 us (0.0%)
[Performance] InitializeOnLoad LayerSettingsWindow                                                                           :        2 samples, Peak.  16.4 us (1.6x), Avg.  10.1 us, Total. 20.21 us (0.0%)
[Performance] InitializeOnLoad PackageManagerHookGUIDConverter.RegisterPackagesEventHandler                                  :        2 samples, Peak.   462 us (1.7x), Avg.   267 us, Total. 533.1 us (0.0%)
[Performance] InitializeOnLoad EditorDragAndDrop.RegisterEditorClient                                                        :        2 samples, Peak.   140 us (1.1x), Avg.   131 us, Total. 262.2 us (0.0%)
[Performance] InitializeOnLoad EditorEventCallbacks.InitializeFontAssetResourceChangeCallBacks                               :        2 samples, Peak.   716 us (1.0x), Avg.   703 us, Total. 1.406 ms (0.0%)
[Performance] InitializeOnLoad SceneTemplateService.Init                                                                     :        2 samples, Peak.   684 us (1.0x), Avg.   667 us, Total. 1.335 ms (0.0%)
[Performance] InitializeOnLoad DiagnosticSwitchesConsoleMessage.Init                                                         :        2 samples, Peak.  1.85 ms (1.6x), Avg.  1.13 ms, Total. 2.264 ms (0.0%)
[Performance] InitializeOnLoad PackageManagerWindow.EditorInitializedInSafeMode                                              :        2 samples, Peak.  13.6 ms (2.0x), Avg.  6.86 ms, Total. 13.72 ms (0.1%)
[Performance] InitializeOnLoad ToolShortcutContext.Init                                                                      :        2 samples, Peak.  79.9 us (1.0x), Avg.  79.3 us, Total. 158.6 us (0.0%)
[Performance] InitializeOnLoad MemoryProfilerCompilationGuard.InjectCompileGuard                                             :        2 samples, Peak.  88.9 us (1.0x), Avg.  88.6 us, Total. 177.1 us (0.0%)
[Performance] InitializeOnLoad AssetPostprocessingInternal.RefreshCustomDependencies                                         :        2 samples, Peak.  1.77 ms (1.5x), Avg.  1.16 ms, Total. 2.327 ms (0.0%)
[Performance] InitializeOnLoad SysrootManager.Initialize                                                                     :        2 samples, Peak.  5.54 us (1.2x), Avg.  4.67 us, Total. 9.333 us (0.0%)
[Performance] InitializeOnLoad ScopedRegistryAddedPopup.SubscribeToRegistriesAdded                                           :        2 samples, Peak.  4.63 ms (2.0x), Avg.  2.34 ms, Total. 4.684 ms (0.0%)
[Performance] InitializeOnLoad SceneVisibilityManager.Initialize                                                             :        2 samples, Peak.  1.70 ms (1.6x), Avg.  1.09 ms, Total. 2.175 ms (0.0%)
[Performance] InitializeOnLoad EditorWindow.Initialize                                                                       :        2 samples, Peak.   136 us (1.1x), Avg.   123 us, Total. 246.2 us (0.0%)
[Performance] AssemblyReloadEvents.afterAssemblyReload: UnityEditor.SceneTemplate.SceneTemplateService.AfterAssemblyReload   :        2 samples, Peak.  1.96 ms (1.9x), Avg.  1.06 ms, Total. 2.113 ms (0.0%)
[Performance] ProcessService.EditorAfterLoadAllAssemblies                                                                    :        2 samples, Peak.   197 us (1.1x), Avg.   175 us, Total. 350.3 us (0.0%)
[Performance] Application.ReadLicenseInfo                                                                                    :        1 samples, Peak.  14.1 ms (1.0x), Avg.  14.1 ms, Total. 14.07 ms (0.1%)
[Performance] Application.InitialRefresh                                                                                     :        1 samples, Peak.  2.67 s (1.0x), Avg.  2.67 s, Total. 2.667 s (24.4%)
[Performance] Compiling Scripts                                                                                              :        1 samples, Peak.   531 ms (1.0x), Avg.   531 ms, Total. 531.1 ms (4.9%)
[Performance] AssemblyReloadEvents.beforeAssemblyReload: UnityEditor.SceneTemplate.SceneTemplateService.BeforeAssemblyReload :        1 samples, Peak.   277 us (1.0x), Avg.   277 us, Total. 277.2 us (0.0%)
[Performance] InitializeOnLoad TouchSimulation                                                                               :        1 samples, Peak.   133 us (1.0x), Avg.   133 us, Total. 132.8 us (0.0%)
[Performance] InitializeOnLoad EnableUITKEditor                                                                              :        1 samples, Peak.  11.3 us (1.0x), Avg.  11.3 us, Total. 11.25 us (0.0%)
[Performance] InitializeOnLoad InputSystem                                                                                   :        1 samples, Peak.  28.3 ms (1.0x), Avg.  28.3 ms, Total. 28.31 ms (0.3%)
[Performance] InitializeOnLoad InputSystemUIInputModuleEditor                                                                :        1 samples, Peak.  90.4 us (1.0x), Avg.  90.4 us, Total. 90.42 us (0.0%)
[Performance] InitializeOnLoad DeEditorNotification                                                                          :        1 samples, Peak.   356 us (1.0x), Avg.   356 us, Total. 356.3 us (0.0%)
[Performance] InitializeOnLoad DeScriptExecutionOrderManager                                                                 :        1 samples, Peak.   154 us (1.0x), Avg.   154 us, Total. 154.2 us (0.0%)
[Performance] InitializeOnLoad DOTweenProEditorManager                                                                       :        1 samples, Peak.   307 us (1.0x), Avg.   307 us, Total. 306.9 us (0.0%)
[Performance] InitializeOnLoad Initializer                                                                                   :        2 samples, Peak.   107 us (1.1x), Avg.  93.6 us, Total. 187.3 us (0.0%)
[Performance] InitializeOnLoad ReadmeEditor                                                                                  :        1 samples, Peak.  85.2 us (1.0x), Avg.  85.2 us, Total. 85.17 us (0.0%)
[Performance] InitializeOnLoad VSUsageUtility                                                                                :        1 samples, Peak.  18.8 ms (1.0x), Avg.  18.8 ms, Total. 18.84 ms (0.2%)
[Performance] InitializeOnLoad BackgroundWatcher                                                                             :        1 samples, Peak.  86.0 us (1.0x), Avg.  86.0 us, Total. 85.96 us (0.0%)
[Performance] InitializeOnLoad UnityTestProtocolStarter                                                                      :        1 samples, Peak.   166 us (1.0x), Avg.   166 us, Total. 166.4 us (0.0%)
[Performance] InitializeOnLoad TestStarter                                                                                   :        1 samples, Peak.   368 us (1.0x), Avg.   368 us, Total. 367.8 us (0.0%)
[Performance] InitializeOnLoad RerunCallbackInitializer                                                                      :        1 samples, Peak.  1.86 ms (1.0x), Avg.  1.86 ms, Total. 1.861 ms (0.0%)
[Performance] InitializeOnLoad VisualStudioEditor                                                                            :        1 samples, Peak.  7.47 ms (1.0x), Avg.  7.47 ms, Total. 7.475 ms (0.1%)
[Performance] InitializeOnLoad TestRunnerApiListener                                                                         :        1 samples, Peak.   370 us (1.0x), Avg.   370 us, Total. 369.6 us (0.0%)
[Performance] InitializeOnLoad VisualStudioIntegration                                                                       :        1 samples, Peak.   983 us (1.0x), Avg.   983 us, Total. 982.5 us (0.0%)
[Performance] InitializeOnLoad PrefabLayoutRebuilder                                                                         :        1 samples, Peak.   205 us (1.0x), Avg.   205 us, Total. 205.3 us (0.0%)
[Performance] InitializeOnLoad RiderScriptEditor                                                                             :        1 samples, Peak.  5.15 ms (1.0x), Avg.  5.15 ms, Total. 5.147 ms (0.0%)
[Performance] InitializeOnLoad CallbackInitializer                                                                           :        1 samples, Peak.   676 us (1.0x), Avg.   676 us, Total. 676.2 us (0.0%)
[Performance] InitializeOnLoad SpriteEditorWindow                                                                            :        1 samples, Peak.   236 us (1.0x), Avg.   236 us, Total. 236.0 us (0.0%)
[Performance] InitializeOnLoad CoverageReporterStarter                                                                       :        1 samples, Peak.  2.77 ms (1.0x), Avg.  2.77 ms, Total. 2.773 ms (0.0%)
[Performance] InitializeOnLoad PlasticPlugin                                                                                 :        1 samples, Peak.  35.2 ms (1.0x), Avg.  35.2 ms, Total. 35.16 ms (0.3%)
[Performance] InitializeOnLoad FungusEditorPreferences                                                                       :        1 samples, Peak.   136 us (1.0x), Avg.   136 us, Total. 136.1 us (0.0%)
[Performance] InitializeOnLoad HierarchyIcons                                                                                :        1 samples, Peak.  86.3 us (1.0x), Avg.  86.3 us, Total. 86.29 us (0.0%)
[Performance] InitializeOnLoad ToolbarBootstrap                                                                              :        1 samples, Peak.  29.7 ms (1.0x), Avg.  29.7 ms, Total. 29.68 ms (0.3%)
[Performance] InitializeOnLoad VSCodeScriptEditor                                                                            :        1 samples, Peak.  1.47 ms (1.0x), Avg.  1.47 ms, Total. 1.468 ms (0.0%)
[Performance] InitializeOnLoad SysrootPackage.IssueWarningIfLinuxIL2CPPNotPresent                                            :        1 samples, Peak.   112 us (1.0x), Avg.   112 us, Total. 111.9 us (0.0%)
[Performance] InitializeOnLoad InputSystemPackageControl.SubscribePackageManagerEvent                                        :        1 samples, Peak.  40.4 us (1.0x), Avg.  40.4 us, Total. 40.37 us (0.0%)
[Performance] InitializeOnLoad InputSystemPluginControl.CheckForExtension                                                    :        1 samples, Peak.   897 us (1.0x), Avg.   897 us, Total. 896.7 us (0.0%)
[Performance] InitializeOnLoad TestJobDataHolder.ResumeRunningJobs                                                           :        1 samples, Peak.   555 us (1.0x), Avg.   555 us, Total. 555.0 us (0.0%)
[Performance] InitializeOnLoad AnalyticsReporter.RegisterCallbacks                                                           :        1 samples, Peak.   147 us (1.0x), Avg.   147 us, Total. 147.4 us (0.0%)
[Performance] InitializeOnLoad VisualStudioEditor.LegacyVisualStudioCodePackageDisabler                                      :        1 samples, Peak.  1.77 ms (1.0x), Avg.  1.77 ms, Total. 1.771 ms (0.0%)
[Performance] DidReloadScriptsNavMeshExtension.OnScriptReload                                                                :        1 samples, Peak.   120 us (1.0x), Avg.   120 us, Total. 119.7 us (0.0%)
[Performance] DidReloadScriptsFungusEditorResources.OnDidReloadScripts                                                       :        1 samples, Peak.  45.5 ms (1.0x), Avg.  45.5 ms, Total. 45.48 ms (0.4%)
[Performance] DidReloadScriptsEventSelectorPopupWindowContent.OnScriptsReloaded                                              :        1 samples, Peak.   541 us (1.0x), Avg.   541 us, Total. 540.9 us (0.0%)
[Performance] DidReloadScriptsCommandSelectorPopupWindowContent.OnScriptsReloaded                                            :        1 samples, Peak.   250 us (1.0x), Avg.   250 us, Total. 250.2 us (0.0%)
[Performance] DidReloadScriptsVariableSelectPopupWindowContent.OnScriptsReloaded                                             :        1 samples, Peak.   120 us (1.0x), Avg.   120 us, Total. 119.8 us (0.0%)
[Performance] DidReloadScriptsAdvancedDropdownWindow.OnScriptReload                                                          :        2 samples, Peak.   155 us (1.2x), Avg.   133 us, Total. 266.4 us (0.0%)
[Performance] DidReloadScriptsTestRunnerWindow.CompilationCallback                                                           :        1 samples, Peak.   286 us (1.0x), Avg.   286 us, Total. 285.5 us (0.0%)
[Performance] DidReloadScriptsTestListCache.ScriptReloaded                                                                   :        1 samples, Peak.   784 us (1.0x), Avg.   784 us, Total. 783.9 us (0.0%)
[Performance] DidReloadScriptsLuaBindingsEditor.DidReloadScripts                                                             :        1 samples, Peak.   251 us (1.0x), Avg.   251 us, Total. 251.0 us (0.0%)
[Performance] InputActionAssetPostprocessor.OnPostprocessAllAssets                                                           :        4 samples, Peak.   252 us (2.1x), Avg.   121 us, Total. 483.9 us (0.0%)
[Performance] InputActionJsonNameModifierAssetProcessor.OnPostprocessAllAssets                                               :        4 samples, Peak.  28.1 us (1.7x), Avg.  16.3 us, Total. 65.21 us (0.0%)
[Performance] ProjectSettingsPostprocessor.OnPostprocessAllAssets                                                            :        4 samples, Peak.   115 us (1.9x), Avg.  59.6 us, Total. 238.3 us (0.0%)
[Performance] SyncVS.PostprocessSyncProject                                                                                  :        2 samples, Peak.  1.69 ms (1.8x), Avg.   917 us, Total. 1.833 ms (0.0%)
[Performance] Application.ImportPackagesAndSetTemplateWhenCreatingProject                                                    :        1 samples, Peak.  11.1 ms (1.0x), Avg.  11.1 ms, Total. 11.11 ms (0.1%)
[Performance] Application.SyncCurrentColorSpace                                                                              :        1 samples, Peak.  51.4 ms (1.0x), Avg.  51.4 ms, Total. 51.36 ms (0.5%)
[Performance] Application.OnUsbDevicesChanged                                                                                :        1 samples, Peak.  56.0 us (1.0x), Avg.  56.0 us, Total. 55.96 us (0.0%)
[Performance] Application.AssetInstanceCacheUpdate                                                                           :        1 samples, Peak.   208 ns (1.0x), Avg.   208 ns, Total. 208.0 ns (0.0%)
[Performance] Application.UnityExtensions.Initialize                                                                         :        1 samples, Peak.  3.81 ms (1.0x), Avg.  3.81 ms, Total. 3.812 ms (0.0%)
[Performance] CodeEditorProjectSync.SyncEditorProject                                                                        :        1 samples, Peak.   135 ms (1.0x), Avg.   135 ms, Total. 134.6 ms (1.2%)
[Performance] Application.ExecuteStartups                                                                                    :        1 samples, Peak.  34.8 ms (1.0x), Avg.  34.8 ms, Total. 34.84 ms (0.3%)
[Performance] Menu.RegisterMenuInterface                                                                                     :       26 samples, Peak.   500 ns (4.3x), Avg.   115 ns, Total. 3.002 us (0.0%)
[Performance] Gizmo.RebuildRenderers                                                                                         :        2 samples, Peak.  34.0 ms (1.5x), Avg.  22.8 ms, Total. 45.53 ms (0.4%)
[Performance] Gizmo.AddGizmoRenderers                                                                                        :      184 samples, Peak.   903 us (72.8x), Avg.  12.4 us, Total. 2.282 ms (0.0%)
[Performance] Application.editorInitializingProject                                                                          :        1 samples, Peak.  13.3 ms (1.0x), Avg.  13.3 ms, Total. 13.28 ms (0.1%)
[Performance] Application.InitializeMenu                                                                                     :        1 samples, Peak.   275 ms (1.0x), Avg.   275 ms, Total. 275.3 ms (2.5%)
[Performance] Menu.RebuildAll                                                                                                :        1 samples, Peak.   275 ms (1.0x), Avg.   275 ms, Total. 275.3 ms (2.5%)
[Performance] Menu.BuildRegisteredMenuInterfaces                                                                             :        1 samples, Peak.   272 ms (1.0x), Avg.   272 ms, Total. 272.1 ms (2.5%)
[Performance] Menu.FilterMenuItem                                                                                            :      796 samples, Peak.  19.9 ms (788.5x), Avg.  25.3 us, Total. 20.10 ms (0.2%)
[Performance] UpdateAllMenus                                                                                                 :        1 samples, Peak.   350 us (1.0x), Avg.   350 us, Total. 349.5 us (0.0%)
[Performance] EditorSceneManager.sceneClosing: UnityEditor.SceneVisibilityManager.EditorSceneManagerOnSceneClosing           :        2 samples, Peak.   163 us (2.0x), Avg.  83.6 us, Total. 167.2 us (0.0%)
[Performance] GUIView.RepaintAll.PlayerLoopController                                                                        :        2 samples, Peak.   625 ns (1.1x), Avg.   563 ns, Total. 1.125 us (0.0%)
[Performance] EditorSceneManager.newSceneCreated: UnityEditor.SceneTemplate.SceneTemplateService.OnNewSceneCreated           :        1 samples, Peak.   151 us (1.0x), Avg.   151 us, Total. 151.2 us (0.0%)
[Performance] EditorSceneManager.newSceneCreated: UnityEditor.SceneVisibilityManager.EditorSceneManagerOnNewSceneCreated     :        1 samples, Peak.  96.0 us (1.0x), Avg.  96.0 us, Total. 96.00 us (0.0%)
[Performance] EditorSceneManager.newSceneCreated: UnityEditor.SceneManagement.StageNavigationManager.OnNewSceneCreated       :        1 samples, Peak.  32.7 us (1.0x), Avg.  32.7 us, Total. 32.71 us (0.0%)
[Performance] Application.InvokeFinishedLoadingProject                                                                       :        1 samples, Peak.  8.19 ms (1.0x), Avg.  8.19 ms, Total. 8.185 ms (0.1%)
[Performance] ProcessService.OnProjectLoaded                                                                                 :        1 samples, Peak.  34.0 us (1.0x), Avg.  34.0 us, Total. 34.00 us (0.0%)
[Performance] VersionControl.Task.Wait                                                                                       :        1 samples, Peak.  4.96 us (1.0x), Avg.  4.96 us, Total. 4.958 us (0.0%)
[Performance] Inspector.InitOrRebuild                                                                                        :        2 samples, Peak.   324 us (1.5x), Avg.   223 us, Total. 446.5 us (0.0%)
[Performance] StateMacroSavedEvent.OnWillSaveAssets                                                                          :        2 samples, Peak.   143 us (1.9x), Avg.  74.5 us, Total. 149.0 us (0.0%)
[Performance] AssetModProcessor.OnWillSaveAssets                                                                             :        2 samples, Peak.   132 us (2.0x), Avg.  66.5 us, Total. 132.9 us (0.0%)
[Performance] AssetModificationProcessor.OnWillSaveAssets                                                                    :        2 samples, Peak.   138 us (2.0x), Avg.  69.2 us, Total. 138.5 us (0.0%)
[Performance] UnityCloudProjectLinkMonitor.OnWillSaveAssets                                                                  :        2 samples, Peak.   160 us (2.0x), Avg.  80.6 us, Total. 161.2 us (0.0%)
[Performance] FlowMacroSavedEvent.OnWillSaveAssets                                                                           :        2 samples, Peak.  75.1 us (2.0x), Avg.  37.9 us, Total. 75.83 us (0.0%)
[Performance] BuilderAssetModificationProcessor.OnWillSaveAssets                                                             :        2 samples, Peak.   234 us (2.0x), Avg.   118 us, Total. 236.2 us (0.0%)
[Performance] TerrainModificationProcessor.OnWillSaveAssets                                                                  :        2 samples, Peak.   352 us (2.0x), Avg.   179 us, Total. 358.3 us (0.0%)
[Performance] AssetDatabase.ImportAssets                                                                                     :        5 samples, Peak.  48.1 ms (5.0x), Avg.  9.63 ms, Total. 48.14 ms (0.4%)
[Performance] PresetManagerPostProcessor.OnPreprocessAsset                                                                   :        1 samples, Peak.   192 us (1.0x), Avg.   192 us, Total. 192.3 us (0.0%)
[Performance] CancelSplashScreenOnAssetChange.OnPreprocessAsset                                                              :        1 samples, Peak.  84.6 us (1.0x), Avg.  84.6 us, Total. 84.58 us (0.0%)
[Performance] UtilityWindowPostProcessor.OnPostprocessAllAssets                                                              :        2 samples, Peak.   501 us (1.0x), Avg.   497 us, Total. 994.7 us (0.0%)
[Performance] EditorResourcesPostProcessor.OnPostprocessAllAssets                                                            :        2 samples, Peak.   132 us (1.0x), Avg.   132 us, Total. 263.2 us (0.0%)
[Performance] TMPro_TexturePostProcessor.OnPostprocessAllAssets                                                              :        2 samples, Peak.   163 us (1.0x), Avg.   163 us, Total. 325.0 us (0.0%)
[Performance] AssetPostprocessor.OnPostprocessAllAssets                                                                      :        2 samples, Peak.   435 us (1.0x), Avg.   434 us, Total. 868.8 us (0.0%)
[Performance] BuilderAssetPostprocessor.OnPostprocessAllAssets                                                               :        2 samples, Peak.  1.97 ms (1.0x), Avg.  1.97 ms, Total. 3.935 ms (0.0%)
[Performance] ArtifactBrowserPostProcessor.OnPostprocessAllAssets                                                            :        2 samples, Peak.   370 us (1.0x), Avg.   369 us, Total. 739.0 us (0.0%)
[Performance] AssetEvents.OnPostprocessAllAssets                                                                             :        4 samples, Peak.   105 us (1.5x), Avg.  70.9 us, Total. 283.7 us (0.0%)
[Performance] AudioMixerPostprocessor.OnPostprocessAllAssets                                                                 :        2 samples, Peak.   210 us (1.0x), Avg.   209 us, Total. 417.9 us (0.0%)
[Performance] StyleCatalogPostProcessor.OnPostprocessAllAssets                                                               :        2 samples, Peak.  11.3 ms (1.0x), Avg.  11.3 ms, Total. 22.55 ms (0.2%)
[Performance] BuildCatalog                                                                                                   :        1 samples, Peak.  10.6 ms (1.0x), Avg.  10.6 ms, Total. 10.62 ms (0.1%)
[Performance] ModelImporterPostProcessor.OnPostprocessAllAssets                                                              :        2 samples, Peak.   570 us (1.0x), Avg.   569 us, Total. 1.139 ms (0.0%)
[Performance] RetainedModeAssetPostprocessor.OnPostprocessAllAssets                                                          :        2 samples, Peak.  1.31 ms (1.0x), Avg.  1.30 ms, Total. 2.607 ms (0.0%)
[Performance] AssetChangedListener.OnPostprocessAllAssets                                                                    :        2 samples, Peak.   729 us (1.0x), Avg.   728 us, Total. 1.457 ms (0.0%)
[Performance] SpeedTreePostProcessor.OnPostprocessAllAssets                                                                  :        2 samples, Peak.   103 us (1.0x), Avg.   103 us, Total. 205.1 us (0.0%)
[Performance] TextAssetPostProcessor.OnPostprocessAllAssets                                                                  :        2 samples, Peak.   213 us (1.0x), Avg.   212 us, Total. 424.7 us (0.0%)
[Performance] SpriteEditorTexturePostprocessor.OnPostprocessAllAssets                                                        :        2 samples, Peak.   159 us (1.0x), Avg.   158 us, Total. 315.9 us (0.0%)
[Performance] Application.Shutdown.SaveDefaultWindowPreferences                                                              :        1 samples, Peak.  44.8 us (1.0x), Avg.  44.8 us, Total. 44.83 us (0.0%)
[Performance] Application.Shutdown.SaveSceneManagerSetup                                                                     :        1 samples, Peak.   103 us (1.0x), Avg.   103 us, Total. 102.9 us (0.0%)
[Performance] Application.Shutdown.CleanupAllContainerWindows                                                                :        1 samples, Peak.   291 ns (1.0x), Avg.   291 ns, Total. 291.0 ns (0.0%)
[Performance] EditorApplication.quitting: UnityEditor.SettingsManagement.FileSettingsRepository.Save                         :        1 samples, Peak.   745 us (1.0x), Avg.   745 us, Total. 745.4 us (0.0%)
[Performance] EditorApplication.quitting: callback in UnityEditor.TextCore.Text.EditorEventCallbacks                         :        1 samples, Peak.  3.49 ms (1.0x), Avg.  3.49 ms, Total. 3.491 ms (0.0%)
[Performance] Killing ADB server                                                                                             :        1 samples, Peak.  35.0 ms (1.0x), Avg.  35.0 ms, Total. 34.98 ms (0.3%)
[Performance] Application.Shutdown.PauseProfilerSession                                                                      :        1 samples, Peak.  36.3 us (1.0x), Avg.  36.3 us, Total. 36.33 us (0.0%)
[Performance] Application.Shutdown.PauseAssetImportWorkers                                                                   :        1 samples, Peak.   223 us (1.0x), Avg.   223 us, Total. 222.8 us (0.0%)
[Performance] Application.Shutdown.SaveAssets                                                                                :        1 samples, Peak.  2.30 ms (1.0x), Avg.  2.30 ms, Total. 2.303 ms (0.0%)
[Performance] Application.Shutdown.CleanupRenderPipeline                                                                     :        1 samples, Peak.  6.92 us (1.0x), Avg.  6.92 us, Total. 6.917 us (0.0%)
[Performance] Application.Shutdown.StopPreloadManager                                                                        :        1 samples, Peak.  10.6 ms (1.0x), Avg.  10.6 ms, Total. 10.58 ms (0.1%)
[Performance] Application.Shutdown.DestroyWorld                                                                              :        1 samples, Peak.   543 us (1.0x), Avg.   543 us, Total. 542.7 us (0.0%)
[Performance] Application.Shutdown.CleanupAfterLoad                                                                          :        1 samples, Peak.  2.95 ms (1.0x), Avg.  2.95 ms, Total. 2.955 ms (0.0%)
[Performance] Application.Shutdown.Progress                                                                                  :        1 samples, Peak.  14.0 us (1.0x), Avg.  14.0 us, Total. 14.04 us (0.0%)
[Performance] Application.Shutdown.GICleanupManagers                                                                         :        1 samples, Peak.   515 us (1.0x), Avg.   515 us, Total. 514.9 us (0.0%)
[Performance] Application.Shutdown.MenuCleanupClass                                                                          :        1 samples, Peak.   152 us (1.0x), Avg.   152 us, Total. 152.1 us (0.0%)
[Performance] Application.Shutdown.ADBSaveStateBeforeShutdown                                                                :        1 samples, Peak.  9.33 us (1.0x), Avg.  9.33 us, Total. 9.334 us (0.0%)
[Performance] Application.Shutdown.RemoteShutdown                                                                            :        1 samples, Peak.   750 ns (1.0x), Avg.   750 ns, Total. 750.0 ns (0.0%)
[Performance] Application.Shutdown.CleanupVCProvider                                                                         :        1 samples, Peak.  10.0 us (1.0x), Avg.  10.0 us, Total. 10.04 us (0.0%)
[Performance] Application.Shutdown.InputShutdown                                                                             :        1 samples, Peak.   162 us (1.0x), Avg.   162 us, Total. 161.5 us (0.0%)
[Performance] Application.Shutdown.GizmoManagerDestroy                                                                       :        1 samples, Peak.   331 us (1.0x), Avg.   331 us, Total. 331.0 us (0.0%)
[Performance] Application.Shutdown.ProfilerSession                                                                           :        1 samples, Peak.   125 us (1.0x), Avg.   125 us, Total. 124.7 us (0.0%)
[Performance] Application.Shutdown.ReleaseGfxWindowOnAllGUIViews                                                             :        1 samples, Peak.  1.33 us (1.0x), Avg.  1.33 us, Total. 1.333 us (0.0%)
[Performance] Application.Shutdown.CleanupEngine                                                                             :        1 samples, Peak.  92.6 ms (1.0x), Avg.  92.6 ms, Total. 92.58 ms (0.8%)
[Performance] Application.Shutdown.CleanupAssetDatabase                                                                      :        1 samples, Peak.  4.30 ms (1.0x), Avg.  4.30 ms, Total. 4.299 ms (0.0%)
[Performance] Application.Shutdown.ScriptCompilationCleanUp                                                                  :        1 samples, Peak.  20.8 us (1.0x), Avg.  20.8 us, Total. 20.75 us (0.0%)
[Performance] Application.Shutdown.DestroyJobSystem                                                                          :        1 samples, Peak.   181 us (1.0x), Avg.   181 us, Total. 180.9 us (0.0%)
[Performance] Application.Shutdown.CleanupPersistentManager                                                                  :        1 samples, Peak.  1.97 ms (1.0x), Avg.  1.97 ms, Total. 1.971 ms (0.0%)
[Performance] Application.Shutdown.CleanupAsyncReadManager                                                                   :        1 samples, Peak.   107 us (1.0x), Avg.   107 us, Total. 107.3 us (0.0%)
[Performance] Application.Shutdown.CleanupMono                                                                               :        1 samples, Peak.  54.1 ms (1.0x), Avg.  54.1 ms, Total. 54.05 ms (0.5%)
[Performance] Application.Shutdown.CleanupStdConverters                                                                      :        1 samples, Peak.  8.33 us (1.0x), Avg.  8.33 us, Total. 8.334 us (0.0%)
[Performance] Application.Shutdown.UnloadAllPlatformSupportModuleNativeDlls                                                  :        1 samples, Peak.   416 ns (1.0x), Avg.   416 ns, Total. 416.0 ns (0.0%)
[Performance] Application.Shutdown.UnloadAllPlatformSupportNativeLibraries                                                   :        1 samples, Peak.  48.8 us (1.0x), Avg.  48.8 us, Total. 48.79 us (0.0%)
[Performance] Application.Shutdown.CleanupAutoDocumentation                                                                  :        1 samples, Peak.   198 us (1.0x), Avg.   198 us, Total. 197.6 us (0.0%)
[Performance] Application.Shutdown.ShaderNameManagerDestroy                                                                  :        1 samples, Peak.  28.2 us (1.0x), Avg.  28.2 us, Total. 28.25 us (0.0%)
[Performance] Application.Shutdown.CleanupCacheServer                                                                        :        1 samples, Peak.   125 ns (1.0x), Avg.   125 ns, Total. 125.0 ns (0.0%)
[Performance] Application.Shutdown.Virtualization_Shutdown                                                                   :        1 samples, Peak.   250 ns (1.0x), Avg.   250 ns, Total. 250.0 ns (0.0%)
[Performance] Application.Shutdown.DevConnections                                                                            :        1 samples, Peak.  52.0 us (1.0x), Avg.  52.0 us, Total. 52.04 us (0.0%)
[Package Manager] Server::Kill -- Server was shutdown
Checking for leaked weakptr:
  Found no leaked weakptrs.
Memory Statistics:
[ALLOC_TEMP_TLS] TLS Allocator
  StackAllocators : 
    [ALLOC_TEMP_CurlRequest]
      Initial Block Size 64.0 KB
      Current Block Size 64.0 KB
      Peak Allocated Bytes 0 B
      Overflow Count 0
    [ALLOC_TEMP_MAIN]
      Initial Block Size 16.0 MB
      Current Block Size 74.7 MB
      Peak Allocated Bytes 74.7 MB
      Overflow Count 0
    [ALLOC_TEMP_Background Job.Worker 8]
      Initial Block Size 32.0 KB
      Current Block Size 32.0 KB
      Peak Allocated Bytes 0 B
      Overflow Count 0
    [ALLOC_TEMP_Job.Worker 4]
      Initial Block Size 256.0 KB
      Current Block Size 256.0 KB
      Peak Allocated Bytes 9.2 KB
      Overflow Count 1
    [ALLOC_TEMP_BakingJobs.Worker 5]
      Initial Block Size 256.0 KB
      Current Block Size 256.0 KB
      Peak Allocated Bytes 0 B
      Overflow Count 0
    [ALLOC_TEMP_Background Job.Worker 11]
      Initial Block Size 32.0 KB
      Current Block Size 32.0 KB
      Peak Allocated Bytes 0 B
      Overflow Count 0
    [ALLOC_TEMP_Background Job.Worker 3]
      Initial Block Size 32.0 KB
      Current Block Size 32.0 KB
      Peak Allocated Bytes 0 B
      Overflow Count 0
    [ALLOC_TEMP_BatchDeleteObjects]
      Initial Block Size 64.0 KB
      Current Block Size 64.0 KB
      Peak Allocated Bytes 0 B
      Overflow Count 0
    [ALLOC_TEMP_CoreBusinessMetricsCache]
      Initial Block Size 64.0 KB
      Current Block Size 64.0 KB
      Peak Allocated Bytes 7.9 KB
      Overflow Count 0
    [ALLOC_TEMP_REST Message Handler]
      Initial Block Size 64.0 KB
      Current Block Size 64.0 KB
      Peak Allocated Bytes 0 B
      Overflow Count 0
    [ALLOC_TEMP_AssetDatabase.IOService]
      Initial Block Size 64.0 KB
      Current Block Size 64.0 KB
      Peak Allocated Bytes 0 B
      Overflow Count 0
    [ALLOC_TEMP_Background Job.Worker 2]
      Initial Block Size 32.0 KB
      Current Block Size 32.0 KB
      Peak Allocated Bytes 66 B
      Overflow Count 0
    [ALLOC_TEMP_Loading.PreloadManager]
      Initial Block Size 32.0 MB
      Current Block Size 32.0 MB
      Peak Allocated Bytes 2.0 MB
      Overflow Count 0
    [ALLOC_TEMP_Profiler.Dispatcher]
      Initial Block Size 64.0 KB
      Current Block Size 64.0 KB
      Peak Allocated Bytes 0 B
      Overflow Count 0
    [ALLOC_TEMP_Job.Worker 3]
      Initial Block Size 256.0 KB
      Current Block Size 256.0 KB
      Peak Allocated Bytes 8.0 KB
      Overflow Count 1
    [ALLOC_TEMP_BakingJobs.Worker 7]
      Initial Block Size 256.0 KB
      Current Block Size 256.0 KB
      Peak Allocated Bytes 0 B
      Overflow Count 0
    [ALLOC_TEMP_Background Job.Worker 5]
      Initial Block Size 32.0 KB
      Current Block Size 32.0 KB
      Peak Allocated Bytes 0 B
      Overflow Count 0
    [ALLOC_TEMP_Background Job.Worker 13]
      Initial Block Size 32.0 KB
      Current Block Size 32.0 KB
      Peak Allocated Bytes 0 B
      Overflow Count 0
    [ALLOC_TEMP_Job.Worker 2]
      Initial Block Size 256.0 KB
      Current Block Size 256.0 KB
      Peak Allocated Bytes 9.2 KB
      Overflow Count 1
    [ALLOC_TEMP_BakingJobs.Worker 6]
      Initial Block Size 256.0 KB
      Current Block Size 256.0 KB
      Peak Allocated Bytes 0 B
      Overflow Count 0
    [ALLOC_TEMP_Job.Worker 0]
      Initial Block Size 256.0 KB
      Current Block Size 256.0 KB
      Peak Allocated Bytes 15.7 KB
      Overflow Count 0
    [ALLOC_TEMP_Job.Worker 5]
      Initial Block Size 256.0 KB
      Current Block Size 256.0 KB
      Peak Allocated Bytes 24.1 KB
      Overflow Count 1
    [ALLOC_TEMP_Job.Worker 6]
      Initial Block Size 256.0 KB
      Current Block Size 256.0 KB
      Peak Allocated Bytes 9.5 KB
      Overflow Count 0
    [ALLOC_TEMP_EnlightenWorker] x 4
      Initial Block Size 64.0 KB
      Current Block Size 64.0 KB
      Peak Allocated Bytes 0 B
      Overflow Count 0
    [ALLOC_TEMP_Background Job.Worker 9]
      Initial Block Size 32.0 KB
      Current Block Size 32.0 KB
      Peak Allocated Bytes 0 B
      Overflow Count 0
    [ALLOC_TEMP_BakingJobs.Worker 1]
      Initial Block Size 256.0 KB
      Current Block Size 256.0 KB
      Peak Allocated Bytes 0 B
      Overflow Count 0
    [ALLOC_TEMP_Loading.AsyncRead]
      Initial Block Size 64.0 KB
      Current Block Size 64.0 KB
      Peak Allocated Bytes 128 B
      Overflow Count 0
    [ALLOC_TEMP_Background Job.Worker 10]
      Initial Block Size 32.0 KB
      Current Block Size 32.0 KB
      Peak Allocated Bytes 0 B
      Overflow Count 0
    [ALLOC_TEMP_Background Job.Worker 6]
      Initial Block Size 32.0 KB
      Current Block Size 32.0 KB
      Peak Allocated Bytes 0 B
      Overflow Count 0
    [ALLOC_TEMP_Background Job.Worker 1]
      Initial Block Size 32.0 KB
      Current Block Size 160.0 KB
      Peak Allocated Bytes 115.2 KB
      Overflow Count 0
    [ALLOC_TEMP_OSX HID Input]
      Initial Block Size 64.0 KB
      Current Block Size 64.0 KB
      Peak Allocated Bytes 0 B
      Overflow Count 0
    [ALLOC_TEMP_AUDIO_FMOD stream thread]
      Initial Block Size 64.0 KB
      Current Block Size 64.0 KB
      Peak Allocated Bytes 0 B
      Overflow Count 0
    [ALLOC_TEMP_BakingJobs.Worker 2]
      Initial Block Size 256.0 KB
      Current Block Size 256.0 KB
      Peak Allocated Bytes 0 B
      Overflow Count 0
    [ALLOC_TEMP_Background Job.Worker 7]
      Initial Block Size 32.0 KB
      Current Block Size 32.0 KB
      Peak Allocated Bytes 0 B
      Overflow Count 0
    [ALLOC_TEMP_Background Job.Worker 15]
      Initial Block Size 32.0 KB
      Current Block Size 32.0 KB
      Peak Allocated Bytes 0 B
      Overflow Count 0
    [ALLOC_TEMP_Job.Worker 1]
      Initial Block Size 256.0 KB
      Current Block Size 256.0 KB
      Peak Allocated Bytes 10.4 KB
      Overflow Count 1
    [ALLOC_TEMP_BakingJobs.Worker 0]
      Initial Block Size 256.0 KB
      Current Block Size 256.0 KB
      Peak Allocated Bytes 0 B
      Overflow Count 0
    [ALLOC_TEMP_BakingJobs.Worker 3]
      Initial Block Size 256.0 KB
      Current Block Size 256.0 KB
      Peak Allocated Bytes 0 B
      Overflow Count 0
    [ALLOC_TEMP_EditorTaskManager]
      Initial Block Size 64.0 KB
      Current Block Size 80.0 KB
      Peak Allocated Bytes 64.0 KB
      Overflow Count 0
    [ALLOC_TEMP_BakingJobs.Worker 4]
      Initial Block Size 256.0 KB
      Current Block Size 256.0 KB
      Peak Allocated Bytes 0 B
      Overflow Count 0
    [ALLOC_TEMP_Background Job.Worker 12]
      Initial Block Size 32.0 KB
      Current Block Size 32.0 KB
      Peak Allocated Bytes 0 B
      Overflow Count 0
    [ALLOC_TEMP_HTTP REST Server]
      Initial Block Size 64.0 KB
      Current Block Size 64.0 KB
      Peak Allocated Bytes 65 B
      Overflow Count 0
    [ALLOC_TEMP_AssetGarbageCollectorHelper] x 7
      Initial Block Size 64.0 KB
      Current Block Size 64.0 KB
      Peak Allocated Bytes 0 B
      Overflow Count 0
    [ALLOC_TEMP_Background Job.Worker 0]
      Initial Block Size 32.0 KB
      Current Block Size 32.0 KB
      Peak Allocated Bytes 0 B
      Overflow Count 0
    [ALLOC_TEMP_AUDIO_Audio Mixer Thread]
      Initial Block Size 64.0 KB
      Current Block Size 64.0 KB
      Peak Allocated Bytes 0 B
      Overflow Count 0
    [ALLOC_TEMP_Background Job.Worker 4]
      Initial Block Size 32.0 KB
      Current Block Size 32.0 KB
      Peak Allocated Bytes 0 B
      Overflow Count 0
    [ALLOC_TEMP_Background Job.Worker 14]
      Initial Block Size 32.0 KB
      Current Block Size 32.0 KB
      Peak Allocated Bytes 0 B
      Overflow Count 0
    [ALLOC_TEMP_CloudJob.Worker 0]
      Initial Block Size 32.0 KB
      Current Block Size 32.0 KB
      Peak Allocated Bytes 3.9 KB
      Overflow Count 0
[ALLOC_MEMORYPROFILER]
  Requested Block Size 1.0 MB
  Peak Block count 2
  Peak Allocated memory 0.8 MB
  Peak Large allocation bytes 0 B
[ALLOC_DEFAULT] Dual Thread Allocator
  Peak main deferred allocation count 44
    [ALLOC_BUCKET]
      Large Block size 32.0 MB
      Used Block count 1
      Peak Allocated bytes 8.6 MB
    [ALLOC_DEFAULT_MAIN]
      Requested Block Size 16.0 MB
      Peak Block count 6
      Peak Allocated memory 88.6 MB
      Peak Large allocation bytes 0 B
    [ALLOC_DEFAULT_THREAD]
      Requested Block Size 16.0 MB
      Peak Block count 1
      Peak Allocated memory 17.9 MB
      Peak Large allocation bytes 8.8 MB
[ALLOC_TEMP_JOB_1_FRAME]
  Initial Block Size 2.0 MB
  Used Block Count 0
  Overflow Count (too large) 0
  Overflow Count (full) 0
[ALLOC_TEMP_JOB_2_FRAMES]
  Initial Block Size 2.0 MB
  Used Block Count 0
  Overflow Count (too large) 0
  Overflow Count (full) 0
[ALLOC_TEMP_JOB_4_FRAMES (JobTemp)]
  Initial Block Size 2.0 MB
  Used Block Count 1
  Overflow Count (too large) 0
  Overflow Count (full) 0
[ALLOC_TEMP_JOB_ASYNC (Background)]
  Initial Block Size 1.0 MB
  Used Block Count 1
  Overflow Count (too large) 2
  Overflow Count (full) 0
[ALLOC_GFX] Dual Thread Allocator
  Peak main deferred allocation count 41
    [ALLOC_BUCKET]
      Large Block size 32.0 MB
      Used Block count 1
      Peak Allocated bytes 8.6 MB
    [ALLOC_GFX_MAIN]
      Requested Block Size 16.0 MB
      Peak Block count 2
      Peak Allocated memory 35.4 MB
      Peak Large allocation bytes 16.0 MB
    [ALLOC_GFX_THREAD]
      Requested Block Size 16.0 MB
      Peak Block count 0
      Peak Allocated memory 0 B
      Peak Large allocation bytes 0 B
[ALLOC_CACHEOBJECTS] Dual Thread Allocator
  Peak main deferred allocation count 251
    [ALLOC_BUCKET]
      Large Block size 32.0 MB
      Used Block count 1
      Peak Allocated bytes 8.6 MB
    [ALLOC_CACHEOBJECTS_MAIN]
      Requested Block Size 4.0 MB
      Peak Block count 16
      Peak Allocated memory 53.5 MB
      Peak Large allocation bytes 40.8 MB
    [ALLOC_CACHEOBJECTS_THREAD]
      Requested Block Size 4.0 MB
      Peak Block count 1
      Peak Allocated memory 33.1 KB
      Peak Large allocation bytes 0 B
[ALLOC_TYPETREE] Dual Thread Allocator
  Peak main deferred allocation count 515
    [ALLOC_BUCKET]
      Large Block size 32.0 MB
      Used Block count 1
      Peak Allocated bytes 8.6 MB
    [ALLOC_TYPETREE_MAIN]
      Requested Block Size 2.0 MB
      Peak Block count 12
      Peak Allocated memory 21.3 MB
      Peak Large allocation bytes 0 B
    [ALLOC_TYPETREE_THREAD]
      Requested Block Size 2.0 MB
      Peak Block count 1
      Peak Allocated memory 21.8 KB
      Peak Large allocation bytes 0 B
[ALLOC_PROFILER]
  Requested Block Size 16.0 MB
  Peak Block count 1
  Peak Allocated memory 455.2 KB
  Peak Large allocation bytes 0 B
    [ALLOC_PROFILER_BUCKET]
      Large Block size 32.0 MB
      Used Block count 1
      Peak Allocated bytes 4.1 KB
[ALLOC_PROFILER_EDITOR]
  Requested Block Size 1.0 MB
  Peak Block count 0
  Peak Allocated memory 0 B
  Peak Large allocation bytes 0 B
    [ALLOC_PROFILER_BUCKET]
      Large Block size 32.0 MB
      Used Block count 1
      Peak Allocated bytes 4.1 KB
##utp:{"type":"MemoryLeaks","version":2,"phase":"Immediate","time":1750841196861,"processId":54870,"allocatedMemory":17510708,"memoryLabels":[{"Default":19935},{"Permanent":15536},{"Thread":1083636},{"Manager":29825},{"VertexData":12},{"Geometry":280},{"Texture":328},{"Shader":68410},{"Material":24},{"GfxDevice":38160},{"Animation":312},{"Audio":3904},{"FontEngine":288},{"Physics":513},{"Serialization":676},{"Input":9248},{"JobScheduler":33424},{"Mono":40},{"ScriptingNativeRuntime":192016},{"BaseObject":1627212},{"Resource":952},{"Renderer":2312},{"Transform":24},{"File":732},{"WebCam":40},{"Culling":40},{"Terrain":1025},{"Wind":24},{"String":29419},{"DynamicArray":91784},{"HashMap":113415},{"Utility":2659758},{"Curl":1056},{"PoolAlloc":1496},{"AI":72},{"TypeTree":5061},{"ScriptManager":640},{"RuntimeInitializeOnLoadManager":80},{"SpriteAtlas":128},{"GI":3496},{"Director":7880},{"CloudService":4552},{"WebRequest":816},{"VR":45705},{"SceneManager":576},{"Video":1096},{"LazyScriptCache":40},{"NativeArray":12},{"Camera":25},{"Secure":9865},{"SerializationCache":1576},{"APIUpdating":11856},{"Subsystems":392},{"VirtualTexturing":57512},{"StaticSafetyDebugInfo":327680},{"Baselib":16},{"EditorGui":80},{"EditorUtility":6252},{"VersionControl":4},{"Undo":720},{"AssetDatabase":10947603},{"RestService":1310},{"EditorGi":376},{"ProgressiveLightmapper":2728},{"License":3584},{"UnityConnect":25496},{"Collab":641},{"Upm":1872},{"DrivenProperties":96},{"LocalIPC":212},{"ProfilerEditor":10975},{"CoreBusinessMetrics":3747},{"AssetReference":40},{"IPCStream":40}]}
