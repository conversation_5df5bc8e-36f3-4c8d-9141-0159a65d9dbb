Unity Editor version:    2022.3.62f1 (4af31df58517)
Branch:                  2022.3/staging
Build type:              Release
Batch mode:              YES
macOS version:           Version 15.5 (Build 24F74)
Darwin version:          24.5.0
Architecture:            arm64
Running under Rosetta:   NO
Available memory:        16384 MB
[Licensing::Module] Trying to connect to existing licensing client channel...
[Licensing::IpcConnector] Successfully connected to: "LicenseClient-cafe" at "2025-06-25T14:38:01.312024Z"
[Licensing::Client] Code 10 while verifying Licensing Client signature (process Id: 47956, path: "/Applications/Unity Hub.app/Contents/Frameworks/UnityLicensingClient_V1.app/Contents/MacOS/Unity.Licensing.Client")
[Licensing::Module] LicensingClient has failed validation; ignoring
[Licensing::Client] Handshaking with LicensingClient:
  Version:                 1.17.0+aa6cfba
  Session Id:              9790a042f3c54f5dad2ffa0a069548c9
  Correlation Id:          add1fd4bf48fcb70563aa0051c34e7a0
  External correlation Id: 7010641796705376637
  Machine Id:              SaO/+vEl19cOZCJ9iXNm5zgvkFY=
[Licensing::Module] Successfully connected to LicensingClient on channel: "LicenseClient-cafe" (connect: 0.00s, validation: 0.01s, handshake: 0.22s)
[Licensing::IpcConnector] Successfully connected to: "LicenseClient-cafe-notifications" at "2025-06-25T14:38:01.550226Z"
[Licensing::Module] Error: Access token is unavailable; failed to update
[Licensing::Client] Successfully updated license
[Licensing::Client] Successfully resolved entitlement details
[Licensing::Module] Serial number assigned to: "F4-2R2W-WK36-W2FA-CS3P-XXXX"
[Licensing::Module] Serial number assigned to: "1375833638986-UnityPersXXXX"
Pro License: NO
[Licensing::Client] Successfully resolved entitlement details
[Licensing::Module] Serial number assigned to: "F4-2R2W-WK36-W2FA-CS3P-XXXX"
[Licensing::Module] Serial number assigned to: "1375833638986-UnityPersXXXX"
Launching external process: /Applications/Unity/Hub/Editor/2022.3.62f1/Unity.app/Contents/Resources/PackageManager/Server/UnityPackageManager

COMMAND LINE ARGUMENTS:
/Applications/Unity/Hub/Editor/2022.3.62f1/Unity.app/Contents/MacOS/Unity
-batchmode
-quit
-projectPath
/Users/<USER>/Documents/GitHub/PerfumerProject
-buildTarget
Android
-logFile
/Users/<USER>/Documents/GitHub/PerfumerProject/unity-android-build.log
Successfully changed project path to: /Users/<USER>/Documents/GitHub/PerfumerProject
/Users/<USER>/Documents/GitHub/PerfumerProject
[UnityMemory] Configuration Parameters - Can be set up in boot.config
    "memorysetup-bucket-allocator-granularity=16"
    "memorysetup-bucket-allocator-bucket-count=8"
    "memorysetup-bucket-allocator-block-size=33554432"
    "memorysetup-bucket-allocator-block-count=8"
    "memorysetup-main-allocator-block-size=16777216"
    "memorysetup-thread-allocator-block-size=16777216"
    "memorysetup-gfx-main-allocator-block-size=16777216"
    "memorysetup-gfx-thread-allocator-block-size=16777216"
    "memorysetup-cache-allocator-block-size=4194304"
    "memorysetup-typetree-allocator-block-size=2097152"
    "memorysetup-profiler-bucket-allocator-granularity=16"
    "memorysetup-profiler-bucket-allocator-bucket-count=8"
    "memorysetup-profiler-bucket-allocator-block-size=33554432"
    "memorysetup-profiler-bucket-allocator-block-count=8"
    "memorysetup-profiler-allocator-block-size=16777216"
    "memorysetup-profiler-editor-allocator-block-size=1048576"
    "memorysetup-temp-allocator-size-main=16777216"
    "memorysetup-job-temp-allocator-block-size=2097152"
    "memorysetup-job-temp-allocator-block-size-background=1048576"
    "memorysetup-job-temp-allocator-reduction-small-platforms=262144"
    "memorysetup-allocator-temp-initial-block-size-main=262144"
    "memorysetup-allocator-temp-initial-block-size-worker=262144"
    "memorysetup-temp-allocator-size-background-worker=32768"
    "memorysetup-temp-allocator-size-job-worker=262144"
    "memorysetup-temp-allocator-size-preload-manager=33554432"
    "memorysetup-temp-allocator-size-nav-mesh-worker=65536"
    "memorysetup-temp-allocator-size-audio-worker=65536"
    "memorysetup-temp-allocator-size-cloud-worker=32768"
    "memorysetup-temp-allocator-size-gi-baking-worker=262144"
    "memorysetup-temp-allocator-size-gfx=262144"
Player connection [8499977984]  Target information:

Player connection [8499977984]  * "[IP] ************** [Port] 55504 [Flags] 2 [Guid] 354136457 [EditorId] 354136457 [Version] 1048832 [Id] OSXEditor(0,MacBookPro) [Debug] 1 [PackageName] OSXEditor [ProjectName] Editor" 

Player connection [8499977984] Host joined multi-casting on [***********:54997]...
Player connection [8499977984] Host joined alternative multi-casting on [***********:34997]...
Input System module state changed to: Initialized.
[PhysX] Initialized MultithreadedTaskDispatcher with 8 workers.
[Package Manager] UpmClient::Connect -- Connected to IPC stream "Upm-71935" after 1.9 seconds.
[Package Manager] Restoring resolved packages state from cache
[Licensing::Client] Successfully resolved entitlement details
[Package Manager] Registered 53 packages:
  Packages from [https://packages.unity.com]:
    com.unity.nuget.newtonsoft-json@3.2.1 (location: /Users/<USER>/Documents/GitHub/PerfumerProject/Library/PackageCache/com.unity.nuget.newtonsoft-json@3.2.1)
    com.unity.collab-proxy@2.7.1 (location: /Users/<USER>/Documents/GitHub/PerfumerProject/Library/PackageCache/com.unity.collab-proxy@2.7.1)
    com.unity.inputsystem@1.14.0 (location: /Users/<USER>/Documents/GitHub/PerfumerProject/Library/PackageCache/com.unity.inputsystem@1.14.0)
    com.unity.textmeshpro@3.0.7 (location: /Users/<USER>/Documents/GitHub/PerfumerProject/Library/PackageCache/com.unity.textmeshpro@3.0.7)
    com.unity.timeline@1.7.7 (location: /Users/<USER>/Documents/GitHub/PerfumerProject/Library/PackageCache/com.unity.timeline@1.7.7)
    com.unity.toolchain.macos-arm64-linux-x86_64@2.0.4 (location: /Users/<USER>/Documents/GitHub/PerfumerProject/Library/PackageCache/com.unity.toolchain.macos-arm64-linux-x86_64@2.0.4)
    com.unity.visualscripting@1.9.4 (location: /Users/<USER>/Documents/GitHub/PerfumerProject/Library/PackageCache/com.unity.visualscripting@1.9.4)
    com.unity.sysroot@2.0.10 (location: /Users/<USER>/Documents/GitHub/PerfumerProject/Library/PackageCache/com.unity.sysroot@2.0.10)
    com.unity.sysroot.linux-x86_64@2.0.9 (location: /Users/<USER>/Documents/GitHub/PerfumerProject/Library/PackageCache/com.unity.sysroot.linux-x86_64@2.0.9)
    com.unity.ide.visualstudio@2.0.22 (location: /Users/<USER>/Documents/GitHub/PerfumerProject/Library/PackageCache/com.unity.ide.visualstudio@2.0.22)
    com.unity.ide.rider@3.0.36 (location: /Users/<USER>/Documents/GitHub/PerfumerProject/Library/PackageCache/com.unity.ide.rider@3.0.36)
    com.unity.ide.vscode@1.2.5 (location: /Users/<USER>/Documents/GitHub/PerfumerProject/Library/PackageCache/com.unity.ide.vscode@1.2.5)
    com.unity.editorcoroutines@1.0.0 (location: /Users/<USER>/Documents/GitHub/PerfumerProject/Library/PackageCache/com.unity.editorcoroutines@1.0.0)
    com.unity.performance.profile-analyzer@1.2.3 (location: /Users/<USER>/Documents/GitHub/PerfumerProject/Library/PackageCache/com.unity.performance.profile-analyzer@1.2.3)
    com.unity.test-framework@1.1.33 (location: /Users/<USER>/Documents/GitHub/PerfumerProject/Library/PackageCache/com.unity.test-framework@1.1.33)
    com.unity.testtools.codecoverage@1.2.6 (location: /Users/<USER>/Documents/GitHub/PerfumerProject/Library/PackageCache/com.unity.testtools.codecoverage@1.2.6)
    com.unity.settings-manager@2.1.0 (location: /Users/<USER>/Documents/GitHub/PerfumerProject/Library/PackageCache/com.unity.settings-manager@2.1.0)
    com.unity.ext.nunit@1.0.6 (location: /Users/<USER>/Documents/GitHub/PerfumerProject/Library/PackageCache/com.unity.ext.nunit@1.0.6)
  Built-in packages:
    com.unity.2d.sprite@1.0.0 (location: /Users/<USER>/Documents/GitHub/PerfumerProject/Library/PackageCache/com.unity.2d.sprite@1.0.0)
    com.unity.feature.development@1.0.1 (location: /Users/<USER>/Documents/GitHub/PerfumerProject/Library/PackageCache/com.unity.feature.development@1.0.1)
    com.unity.ugui@1.0.0 (location: /Users/<USER>/Documents/GitHub/PerfumerProject/Library/PackageCache/com.unity.ugui@1.0.0)
    com.unity.modules.ai@1.0.0 (location: /Users/<USER>/Documents/GitHub/PerfumerProject/Library/PackageCache/com.unity.modules.ai@1.0.0)
    com.unity.modules.androidjni@1.0.0 (location: /Users/<USER>/Documents/GitHub/PerfumerProject/Library/PackageCache/com.unity.modules.androidjni@1.0.0)
    com.unity.modules.animation@1.0.0 (location: /Users/<USER>/Documents/GitHub/PerfumerProject/Library/PackageCache/com.unity.modules.animation@1.0.0)
    com.unity.modules.assetbundle@1.0.0 (location: /Users/<USER>/Documents/GitHub/PerfumerProject/Library/PackageCache/com.unity.modules.assetbundle@1.0.0)
    com.unity.modules.audio@1.0.0 (location: /Users/<USER>/Documents/GitHub/PerfumerProject/Library/PackageCache/com.unity.modules.audio@1.0.0)
    com.unity.modules.cloth@1.0.0 (location: /Users/<USER>/Documents/GitHub/PerfumerProject/Library/PackageCache/com.unity.modules.cloth@1.0.0)
    com.unity.modules.director@1.0.0 (location: /Users/<USER>/Documents/GitHub/PerfumerProject/Library/PackageCache/com.unity.modules.director@1.0.0)
    com.unity.modules.imageconversion@1.0.0 (location: /Users/<USER>/Documents/GitHub/PerfumerProject/Library/PackageCache/com.unity.modules.imageconversion@1.0.0)
    com.unity.modules.imgui@1.0.0 (location: /Users/<USER>/Documents/GitHub/PerfumerProject/Library/PackageCache/com.unity.modules.imgui@1.0.0)
    com.unity.modules.jsonserialize@1.0.0 (location: /Users/<USER>/Documents/GitHub/PerfumerProject/Library/PackageCache/com.unity.modules.jsonserialize@1.0.0)
    com.unity.modules.particlesystem@1.0.0 (location: /Users/<USER>/Documents/GitHub/PerfumerProject/Library/PackageCache/com.unity.modules.particlesystem@1.0.0)
    com.unity.modules.physics@1.0.0 (location: /Users/<USER>/Documents/GitHub/PerfumerProject/Library/PackageCache/com.unity.modules.physics@1.0.0)
    com.unity.modules.physics2d@1.0.0 (location: /Users/<USER>/Documents/GitHub/PerfumerProject/Library/PackageCache/com.unity.modules.physics2d@1.0.0)
    com.unity.modules.screencapture@1.0.0 (location: /Users/<USER>/Documents/GitHub/PerfumerProject/Library/PackageCache/com.unity.modules.screencapture@1.0.0)
    com.unity.modules.terrain@1.0.0 (location: /Users/<USER>/Documents/GitHub/PerfumerProject/Library/PackageCache/com.unity.modules.terrain@1.0.0)
    com.unity.modules.terrainphysics@1.0.0 (location: /Users/<USER>/Documents/GitHub/PerfumerProject/Library/PackageCache/com.unity.modules.terrainphysics@1.0.0)
    com.unity.modules.tilemap@1.0.0 (location: /Users/<USER>/Documents/GitHub/PerfumerProject/Library/PackageCache/com.unity.modules.tilemap@1.0.0)
    com.unity.modules.ui@1.0.0 (location: /Users/<USER>/Documents/GitHub/PerfumerProject/Library/PackageCache/com.unity.modules.ui@1.0.0)
    com.unity.modules.uielements@1.0.0 (location: /Users/<USER>/Documents/GitHub/PerfumerProject/Library/PackageCache/com.unity.modules.uielements@1.0.0)
    com.unity.modules.umbra@1.0.0 (location: /Users/<USER>/Documents/GitHub/PerfumerProject/Library/PackageCache/com.unity.modules.umbra@1.0.0)
    com.unity.modules.unityanalytics@1.0.0 (location: /Users/<USER>/Documents/GitHub/PerfumerProject/Library/PackageCache/com.unity.modules.unityanalytics@1.0.0)
    com.unity.modules.unitywebrequest@1.0.0 (location: /Users/<USER>/Documents/GitHub/PerfumerProject/Library/PackageCache/com.unity.modules.unitywebrequest@1.0.0)
    com.unity.modules.unitywebrequestassetbundle@1.0.0 (location: /Users/<USER>/Documents/GitHub/PerfumerProject/Library/PackageCache/com.unity.modules.unitywebrequestassetbundle@1.0.0)
    com.unity.modules.unitywebrequestaudio@1.0.0 (location: /Users/<USER>/Documents/GitHub/PerfumerProject/Library/PackageCache/com.unity.modules.unitywebrequestaudio@1.0.0)
    com.unity.modules.unitywebrequesttexture@1.0.0 (location: /Users/<USER>/Documents/GitHub/PerfumerProject/Library/PackageCache/com.unity.modules.unitywebrequesttexture@1.0.0)
    com.unity.modules.unitywebrequestwww@1.0.0 (location: /Users/<USER>/Documents/GitHub/PerfumerProject/Library/PackageCache/com.unity.modules.unitywebrequestwww@1.0.0)
    com.unity.modules.vehicles@1.0.0 (location: /Users/<USER>/Documents/GitHub/PerfumerProject/Library/PackageCache/com.unity.modules.vehicles@1.0.0)
    com.unity.modules.video@1.0.0 (location: /Users/<USER>/Documents/GitHub/PerfumerProject/Library/PackageCache/com.unity.modules.video@1.0.0)
    com.unity.modules.vr@1.0.0 (location: /Users/<USER>/Documents/GitHub/PerfumerProject/Library/PackageCache/com.unity.modules.vr@1.0.0)
    com.unity.modules.wind@1.0.0 (location: /Users/<USER>/Documents/GitHub/PerfumerProject/Library/PackageCache/com.unity.modules.wind@1.0.0)
    com.unity.modules.xr@1.0.0 (location: /Users/<USER>/Documents/GitHub/PerfumerProject/Library/PackageCache/com.unity.modules.xr@1.0.0)
    com.unity.modules.subsystems@1.0.0 (location: /Users/<USER>/Documents/GitHub/PerfumerProject/Library/PackageCache/com.unity.modules.subsystems@1.0.0)
[Subsystems] No new subsystems found in resolved package list.
Package Manager log level set to [2]
[Package Manager] Done registering packages in 0.18 seconds
[Licensing::Client] Successfully resolved entitlement details
[Licensing::Module] Serial number assigned to: "F4-2R2W-WK36-W2FA-CS3P-XXXX"
[Licensing::Module] Serial number assigned to: "1375833638986-UnityPersXXXX"
Targeting platform: Android
Refreshing native plugins compatible for Editor in 18.55 ms, found 2 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Initialize engine version: 2022.3.62f1 (4af31df58517)
[Subsystems] Discovering subsystems at path /Applications/Unity/Hub/Editor/2022.3.62f1/Unity.app/Contents/Resources/UnitySubsystems
[Subsystems] Discovering subsystems at path /Users/<USER>/Documents/GitHub/PerfumerProject/Assets
GfxDevice: creating device client; threaded=0; jobified=0
 preferred device: Apple M1 (high power)
Metal devices available: 1
0: Apple M1 (high power)
Using device Apple M1 (high power)
Initializing Metal device caps: Apple M1
[Licensing::Client] Successfully resolved entitlement details
[Licensing::Module] Serial number assigned to: "F4-2R2W-WK36-W2FA-CS3P-XXXX"
[Licensing::Module] Serial number assigned to: "1375833638986-UnityPersXXXX"
Initialize mono
Mono path[0] = '/Applications/Unity/Hub/Editor/2022.3.62f1/Unity.app/Contents/Managed'
Mono path[1] = '/Applications/Unity/Hub/Editor/2022.3.62f1/Unity.app/Contents/MonoBleedingEdge/lib/mono/unityjit-macos'
Mono config path = '/Applications/Unity/Hub/Editor/2022.3.62f1/Unity.app/Contents/MonoBleedingEdge/etc'
Using monoOptions --debugger-agent=transport=dt_socket,embedding=1,server=y,suspend=n,address=127.0.0.1:56935
Using cacheserver namespaces - metadata:defaultmetadata, artifacts:defaultartifacts
Using cacheserver namespaces - metadata:defaultmetadata, artifacts:defaultartifacts
ImportWorker Server TCP listen port: 0
Begin MonoManager ReloadAssembly
Registering precompiled unity dll's ...
Register platform support module: /Applications/Unity/Hub/Editor/2022.3.62f1/PlaybackEngines/WebGLSupport/UnityEditor.WebGL.Extensions.dll
Register platform support module: /Applications/Unity/Hub/Editor/2022.3.62f1/Unity.app/Contents/PlaybackEngines/MacStandaloneSupport/UnityEditor.OSXStandalone.Extensions.dll
Register platform support module: /Applications/Unity/Hub/Editor/2022.3.62f1/PlaybackEngines/AndroidPlayer/UnityEditor.Android.Extensions.dll
[Licensing::Client] Successfully resolved entitlement details
[Licensing::Module] Serial number assigned to: "F4-2R2W-WK36-W2FA-CS3P-XXXX"
[Licensing::Module] Serial number assigned to: "1375833638986-UnityPersXXXX"
Register platform support module: /Applications/Unity/Hub/Editor/2022.3.62f1/PlaybackEngines/iOSSupport/UnityEditor.iOS.Extensions.dll
Register platform support module: /Applications/Unity/Hub/Editor/2022.3.62f1/PlaybackEngines/VisionOSPlayer/UnityEditor.VisionOS.Extensions.dll
Register platform support module: /Applications/Unity/Hub/Editor/2022.3.62f1/PlaybackEngines/AppleTVSupport/UnityEditor.AppleTV.Extensions.dll
Register platform support module: /Applications/Unity/Hub/Editor/2022.3.62f1/PlaybackEngines/WindowsStandaloneSupport/UnityEditor.WindowsStandalone.Extensions.dll
Register platform support module: /Applications/Unity/Hub/Editor/2022.3.62f1/PlaybackEngines/LinuxStandaloneSupport/UnityEditor.LinuxStandalone.Extensions.dll
Registered in 0.021682 seconds.
- Loaded All Assemblies, in  0.297 seconds
Native extension for LinuxStandalone target not found
Native extension for WindowsStandalone target not found
[usbmuxd] Start listen thread
[usbmuxd] Listen thread started
[usbmuxd] Send listen message
Native extension for AppleTV target not found
objc[71935]: Class XcodeScriptingDelegate is implemented in both /Applications/Unity/Hub/Editor/2022.3.62f1/PlaybackEngines/AppleTVSupport/arm64/UnityEditor.iOS.Native.dylib (0x13f6845c8) and /Applications/Unity/Hub/Editor/2022.3.62f1/PlaybackEngines/VisionOSPlayer/arm64/UnityEditor.VisionOS.Native.dylib (0x13f8185c8). This may cause spurious casting failures and mysterious crashes. One of the duplicates must be removed or renamed.
[usbmuxd] Start listen thread
[usbmuxd] Listen thread started
[usbmuxd] Send listen message
Native extension for VisionOS target not found
Native extension for iOS target not found
Native extension for Android target not found
Android Extension - Scanning For ADB Devices 179 ms
Native extension for OSXStandalone target not found
Native extension for WebGL target not found
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  0.455 seconds
Domain Reload Profiling: 752ms
	BeginReloadAssembly (91ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (0ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (1ms)
	RebuildCommonClasses (27ms)
	RebuildNativeTypeToScriptingClass (8ms)
	initialDomainReloadingComplete (55ms)
	LoadAllAssembliesAndSetupDomain (117ms)
		LoadAssemblies (95ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (109ms)
			TypeCache.Refresh (107ms)
				TypeCache.ScanAssembly (94ms)
			ScanForSourceGeneratedMonoScriptInfo (0ms)
			ResolveRequiredComponents (1ms)
	FinalizeReload (455ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (426ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (325ms)
			SetLoadedEditorAssemblies (5ms)
			RefreshPlugins (0ms)
			BeforeProcessingInitializeOnLoad (2ms)
			ProcessInitializeOnLoadAttributes (69ms)
			ProcessInitializeOnLoadMethodAttributes (24ms)
			AfterProcessingInitializeOnLoad (0ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (0ms)
[Licensing::Client] Successfully resolved entitlement details
[Licensing::Module] Serial number assigned to: "F4-2R2W-WK36-W2FA-CS3P-XXXX"
[Licensing::Module] Serial number assigned to: "1375833638986-UnityPersXXXX"
[Licensing::Client] Successfully resolved entitlement details
[Licensing::Module] Serial number assigned to: "F4-2R2W-WK36-W2FA-CS3P-XXXX"
[Licensing::Module] Serial number assigned to: "1375833638986-UnityPersXXXX"
Application.AssetDatabase Initial Refresh Start
The GUID inside 'Assets/FlexReader/Manual.pdf.meta' cannot be extracted by the YAML Parser. Attempting to extract it via string matching instead. Please verify the file does not contain unexpected data.
The GUID inside 'Assets/Resources/Prefabs/UI/Item_Sell.prefab.meta' cannot be extracted by the YAML Parser. Attempting to extract it via string matching instead. Please verify the file does not contain unexpected data.
The GUID inside 'Assets/Scenes/Sell.unity.meta' cannot be extracted by the YAML Parser. Attempting to extract it via string matching instead. Please verify the file does not contain unexpected data.
The GUID inside 'Assets/StreamingAssets/PerfumerData.xlsx.meta' cannot be extracted by the YAML Parser. Attempting to extract it via string matching instead. Please verify the file does not contain unexpected data.
[40m[32minfo[39m[22m[49m: Microsoft.Hosting.Lifetime[14]
      Now listening on: http://unix:/tmp/ilpp.sock-eae4890779ba5942efbfb0ce14813848
[40m[32minfo[39m[22m[49m: Microsoft.Hosting.Lifetime[0]
      Application started. Press Ctrl+C to shut down.
[40m[32minfo[39m[22m[49m: Microsoft.Hosting.Lifetime[0]
      Hosting environment: Production
[40m[32minfo[39m[22m[49m: Microsoft.Hosting.Lifetime[0]
      Content root path: /Users/<USER>/Documents/GitHub/PerfumerProject/
[40m[32minfo[39m[22m[49m: Microsoft.AspNetCore.Hosting.Diagnostics[1]
      Request starting HTTP/2 POST http://ilpp/UnityILPP.PostProcessing/Ping application/grpc -
[40m[32minfo[39m[22m[49m: Microsoft.AspNetCore.Routing.EndpointMiddleware[0]
      Executing endpoint 'gRPC - /UnityILPP.PostProcessing/Ping'
[40m[32minfo[39m[22m[49m: Microsoft.AspNetCore.Routing.EndpointMiddleware[1]
      Executed endpoint 'gRPC - /UnityILPP.PostProcessing/Ping'
[40m[32minfo[39m[22m[49m: Microsoft.AspNetCore.Hosting.Diagnostics[2]
      Request finished HTTP/2 POST http://ilpp/UnityILPP.PostProcessing/Ping application/grpc - - 200 - application/grpc 39.7530ms
Starting: /Applications/Unity/Hub/Editor/2022.3.62f1/Unity.app/Contents/bee_backend --ipc --defer-dag-verification --dagfile="Library/Bee/1300b0aE.dag" --continue-on-failure --profile="Library/Bee/backend1.traceevents" ScriptAssemblies
WorkingDir: /Users/<USER>/Documents/GitHub/PerfumerProject
DisplayProgressbar: Compiling Scripts
ExitCode: 0 Duration: 0s92ms
*** Tundra build success (0.08 seconds), 0 items updated, 517 evaluated
AssetDatabase: script compilation time: 0.676956s
Total cache size ********
Total cache size after purge ********
Begin MonoManager ReloadAssembly
Symbol file LoadedFromMemory is not a mono symbol file
- Loaded All Assemblies, in  0.484 seconds
The GUID inside 'Assets/FlexReader/Manual.pdf.meta' cannot be extracted by the YAML Parser. Attempting to extract it via string matching instead. Please verify the file does not contain unexpected data.
The GUID inside 'Assets/Resources/Prefabs/UI/Item_Sell.prefab.meta' cannot be extracted by the YAML Parser. Attempting to extract it via string matching instead. Please verify the file does not contain unexpected data.
The GUID inside 'Assets/Scenes/Sell.unity.meta' cannot be extracted by the YAML Parser. Attempting to extract it via string matching instead. Please verify the file does not contain unexpected data.
The GUID inside 'Assets/StreamingAssets/PerfumerData.xlsx.meta' cannot be extracted by the YAML Parser. Attempting to extract it via string matching instead. Please verify the file does not contain unexpected data.
Refreshing native plugins compatible for Editor in 4.25 ms, found 2 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
The GUID inside 'Assets/FlexReader/Manual.pdf.meta' cannot be extracted by the YAML Parser. Attempting to extract it via string matching instead. Please verify the file does not contain unexpected data.
The GUID inside 'Assets/Resources/Prefabs/UI/Item_Sell.prefab.meta' cannot be extracted by the YAML Parser. Attempting to extract it via string matching instead. Please verify the file does not contain unexpected data.
The GUID inside 'Assets/Scenes/Sell.unity.meta' cannot be extracted by the YAML Parser. Attempting to extract it via string matching instead. Please verify the file does not contain unexpected data.
The GUID inside 'Assets/StreamingAssets/PerfumerData.xlsx.meta' cannot be extracted by the YAML Parser. Attempting to extract it via string matching instead. Please verify the file does not contain unexpected data.
Refreshing native plugins compatible for Editor in 1.28 ms, found 2 plugins.
Native extension for LinuxStandalone target not found
Native extension for WindowsStandalone target not found
Native extension for AppleTV target not found
Native extension for VisionOS target not found
Native extension for iOS target not found
Native extension for Android target not found
Native extension for OSXStandalone target not found
Native extension for WebGL target not found
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  0.432 seconds
Domain Reload Profiling: 916ms
	BeginReloadAssembly (95ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (3ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (28ms)
	RebuildCommonClasses (21ms)
	RebuildNativeTypeToScriptingClass (8ms)
	initialDomainReloadingComplete (15ms)
	LoadAllAssembliesAndSetupDomain (346ms)
		LoadAssemblies (234ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (154ms)
			TypeCache.Refresh (134ms)
				TypeCache.ScanAssembly (118ms)
			ScanForSourceGeneratedMonoScriptInfo (10ms)
			ResolveRequiredComponents (9ms)
	FinalizeReload (432ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (301ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (35ms)
			SetLoadedEditorAssemblies (2ms)
			RefreshPlugins (0ms)
			BeforeProcessingInitializeOnLoad (38ms)
			ProcessInitializeOnLoadAttributes (214ms)
			ProcessInitializeOnLoadMethodAttributes (11ms)
			AfterProcessingInitializeOnLoad (1ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (2ms)
The GUID inside 'Assets/FlexReader/Manual.pdf.meta' cannot be extracted by the YAML Parser. Attempting to extract it via string matching instead. Please verify the file does not contain unexpected data.
The GUID inside 'Assets/Resources/Prefabs/UI/Item_Sell.prefab.meta' cannot be extracted by the YAML Parser. Attempting to extract it via string matching instead. Please verify the file does not contain unexpected data.
The GUID inside 'Assets/Scenes/Sell.unity.meta' cannot be extracted by the YAML Parser. Attempting to extract it via string matching instead. Please verify the file does not contain unexpected data.
The GUID inside 'Assets/StreamingAssets/PerfumerData.xlsx.meta' cannot be extracted by the YAML Parser. Attempting to extract it via string matching instead. Please verify the file does not contain unexpected data.
Asset Pipeline Refresh (id=79abc4cd421cd46fb9016459897a4e90): Total: 2.986 seconds - Initiated by InitialRefreshV2(ForceSynchronousImport)
	Summary:
		Imports: total=0 (actual=0, local cache=0, cache server=0)
		Asset DB Process Time: managed=0 ms, native=1326 ms
		Asset DB Callback time: managed=8 ms, native=0 ms
		Scripting: domain reloads=1, domain reload time=949 ms, compile time=678 ms, other=21 ms
		Project Asset Count: scripts=5485, non-scripts=1510
		Asset File Changes: new=0, changed=0, moved=0, deleted=0
		Scan Filter Count: 1
	InvokeBeforeRefreshCallbacks: 0.133ms
	ApplyChangesToAssetFolders: 0.059ms
	Scan: 223.551ms
	OnSourceAssetsModified: 0.001ms
	GetAllGuidsForCategorization: 0.456ms
	CategorizeAssets: 107.693ms
	ImportOutOfDateAssets: 456.173ms (-237.121ms without children)
		CompileScripts: 678.198ms
		ReloadNativeAssets: 0.004ms
		UnloadImportedAssets: 12.484ms
		EnsureUptoDateAssetsAreRegisteredWithGuidPM: 1.631ms
		InitializingProgressBar: 0.000ms
		OnDemandSchedulerStart: 0.977ms
	PostProcessAllAssets: 8.719ms
	GatherAllCurrentPrimaryArtifactRevisions: 0.000ms
	UnloadStreamsBegin: 0.380ms
	PersistCurrentRevisions: 0.180ms
	UnloadStreamsEnd: 0.013ms
	GenerateScriptTypeHashes: 1.831ms
	Untracked: 2188.307ms
Application.AssetDatabase Initial Refresh End
Launching external process: /Applications/Unity/Hub/Editor/2022.3.62f1/Unity.app/Contents/Tools/UnityShaderCompiler
Launched and connected shader compiler UnityShaderCompiler after 0.05 seconds
Scanning for USB devices : 0.025ms
Initializing Unity extensions:
[Licensing::Client] Successfully resolved entitlement details
[Licensing::Module] Serial number assigned to: "F4-2R2W-WK36-W2FA-CS3P-XXXX"
[Licensing::Module] Serial number assigned to: "1375833638986-UnityPersXXXX"
[MODES] ModeService[none].Initialize
[MODES] ModeService[none].LoadModes
[MODES] Loading mode Default (0) for mode-current-id-香域
2025-06-25 22:38:14.011 Unity[71935:963260] NSEventModifierFlagFunction specified to -setKeyEquivalentModifierMask: for item <NSMenuItem: 0x31d2ccc10 Font Asset, ke='Command-F12'>, but is only supported for system-provided menu items; will not be used
[PhysX] Initialized MultithreadedTaskDispatcher with 8 workers.
ProgressiveSceneManager::Cancel()
-- Listing OpenCL platforms(s) --
 * OpenCL platform 0
	PROFILE = FULL_PROFILE
	VERSION = OpenCL 1.2 (Apr 18 2025 21:46:03)
	NAME = Apple
	VENDOR = Apple
-- Listing OpenCL device(s) --
 * OpenCL platform 0, device 0 
	DEVICE_TYPE = 4
	DEVICE_NAME = Apple M1
	DEVICE_VENDOR = Apple
	DEVICE_VERSION = OpenCL 1.2 
	DRIVER_VERSION = 1.2 1.0
	DEVICE_MAX_COMPUTE_UNITS = 8
	DEVICE_MAX_CLOCK_FREQUENCY = 1000
	CL_DEVICE_MAX_CONSTANT_BUFFER_SIZE = 1073741824
	CL_DEVICE_HOST_UNIFIED_MEMORY = true
	CL_DEVICE_MAX_MEM_ALLOC_SIZE = 2147483648
	DEVICE_GLOBAL_MEM_SIZE = 11453251584
-- GPU Progressive lightmapper will use OpenCL device 'Apple M1' from 'Apple'--
   use -OpenCL-PlatformAndDeviceIndices <platformIdx> <deviceIdx> as command line arguments if you want to select a specific adapter for OpenCL.
Unloading 5 Unused Serialized files (Serialized files now loaded: 0)
Unloading 69 unused Assets / (467.3 KB). Loaded Objects now: 5068.
Memory consumption went from 135.0 MB to 134.6 MB.
Total: 3.203041 ms (FindLiveObjects: 0.133375 ms CreateObjectMapping: 0.058083 ms MarkObjects: 2.749500 ms  DeleteObjects: 0.261708 ms)

Batchmode quit successfully invoked - shutting down!
Curl error 42: Callback aborted
Killing ADB server in 0.032946 seconds.
Thread 0x31e7c7000 may have been prematurely finalized
[PhysX] Initialized MultithreadedTaskDispatcher with 8 workers.
Input System module state changed to: ShutdownInProgress.
Input System polling thread exited.
Input System module state changed to: Shutdown.
[Licensing::IpcConnector] LicenseClient-cafe-notifications channel disconnected successfully.
[Licensing::IpcConnector] LicenseClient-cafe channel disconnected successfully.
Thread 0x16de5b000 may have been prematurely finalized
[40m[32minfo[39m[22m[49m: Microsoft.Hosting.Lifetime[0]
      Application is shutting down...
Cleanup mono
debugger-agent: Unable to listen on 48
[usbmuxd] Stop listen thread
[usbmuxd] Error: 
[usbmuxd] Listen thread exiting
[usbmuxd] Stop listen thread
[usbmuxd] Error: 
[usbmuxd] Listen thread exiting
[Performance] Application.InitializeProject                                                                                  :        1 samples, Peak.  6.71 s (1.0x), Avg.  6.71 s, Total. 6.712 s (51.0%)
[Performance] Application.PackageManager.StartServer                                                                         :        1 samples, Peak.  3.65 ms (1.0x), Avg.  3.65 ms, Total. 3.647 ms (0.0%)
[Performance] Application.AcquireProjectLock                                                                                 :        1 samples, Peak.  1.98 ms (1.0x), Avg.  1.98 ms, Total. 1.984 ms (0.0%)
[Performance] Application.InitializeEngineNoGraphics                                                                         :        1 samples, Peak.  72.7 ms (1.0x), Avg.  72.7 ms, Total. 72.74 ms (0.6%)
[Performance] Application.PackageManager.Initialize                                                                          :        1 samples, Peak.  1.91 s (1.0x), Avg.  1.91 s, Total. 1.911 s (14.5%)
[Performance] Connecting to Package Manager                                                                                  :      192 samples, Peak.  42.6 us (26.4x), Avg.  1.62 us, Total. 310.5 us (0.0%)
[Performance] Application.EngineGraphics.Initialize                                                                          :        1 samples, Peak.  51.4 ms (1.0x), Avg.  51.4 ms, Total. 51.40 ms (0.4%)
[Performance] Application.GI.Initialize                                                                                      :        1 samples, Peak.  2.60 ms (1.0x), Avg.  2.60 ms, Total. 2.602 ms (0.0%)
[Performance] Application.LoadAllDefaultResourcesFromEditor                                                                  :        1 samples, Peak.  1.35 ms (1.0x), Avg.  1.35 ms, Total. 1.351 ms (0.0%)
[Performance] Application.LoadMonoAssemblies                                                                                 :        1 samples, Peak.   760 ms (1.0x), Avg.   760 ms, Total. 760.0 ms (5.8%)
[Performance] RestoreManagedReferences                                                                                       :        2 samples, Peak.  33.8 ms (2.0x), Avg.  16.9 ms, Total. 33.84 ms (0.3%)
[Performance] InitializeOnLoad DrivenRectTransformUndo                                                                       :        2 samples, Peak.   355 us (1.2x), Avg.   303 us, Total. 605.4 us (0.0%)
[Performance] InitializeOnLoad SceneSearch                                                                                   :        2 samples, Peak.  76.7 us (1.0x), Avg.  75.5 us, Total. 151.0 us (0.0%)
[Performance] InitializeOnLoad CloudBuild                                                                                    :        2 samples, Peak.  3.50 us (1.1x), Avg.  3.31 us, Total. 6.625 us (0.0%)
[Performance] InitializeOnLoad NativeFormatImporterUtility                                                                   :        2 samples, Peak.  1.75 ms (1.3x), Avg.  1.35 ms, Total. 2.696 ms (0.0%)
[Performance] InitializeOnLoad SearchService                                                                                 :        2 samples, Peak.  85.2 us (1.1x), Avg.  77.0 us, Total. 153.9 us (0.0%)
[Performance] InitializeOnLoad SettingsService                                                                               :        2 samples, Peak.  6.96 us (1.1x), Avg.  6.58 us, Total. 13.17 us (0.0%)
[Performance] InitializeOnLoad WindowLayout                                                                                  :        2 samples, Peak.   175 us (1.1x), Avg.   153 us, Total. 305.7 us (0.0%)
[Performance] InitializeOnLoad AssetStoreContext                                                                             :        2 samples, Peak.  12.5 ms (1.7x), Avg.  7.55 ms, Total. 15.09 ms (0.1%)
[Performance] InitializeOnLoad PoolManager                                                                                   :        2 samples, Peak.   449 us (1.1x), Avg.   392 us, Total. 784.5 us (0.0%)
[Performance] InitializeOnLoad MixerEffectDefinitionReloader                                                                 :        2 samples, Peak.  2.55 ms (1.4x), Avg.  1.79 ms, Total. 3.575 ms (0.0%)
[Performance] InitializeOnLoad ProjectSearch                                                                                 :        2 samples, Peak.  37.6 us (1.1x), Avg.  35.2 us, Total. 70.46 us (0.0%)
[Performance] InitializeOnLoad UISystemProfilerRenderService                                                                 :        2 samples, Peak.  8.00 us (1.2x), Avg.  6.85 us, Total. 13.71 us (0.0%)
[Performance] InitializeOnLoad EditMode                                                                                      :        2 samples, Peak.   321 us (1.2x), Avg.   258 us, Total. 515.5 us (0.0%)
[Performance] InitializeOnLoad UnityConnect                                                                                  :        2 samples, Peak.  40.3 us (1.2x), Avg.  34.3 us, Total. 68.54 us (0.0%)
[Performance] InitializeOnLoad ManagedDebugger                                                                               :        2 samples, Peak.   448 us (1.1x), Avg.   401 us, Total. 801.4 us (0.0%)
[Performance] InitializeOnLoad ShortcutIntegration                                                                           :        2 samples, Peak.  47.1 us (1.2x), Avg.  40.4 us, Total. 80.88 us (0.0%)
[Performance] InitializeOnLoad AddComponentWindow                                                                            :        2 samples, Peak.  85.5 us (1.1x), Avg.  80.7 us, Total. 161.4 us (0.0%)
[Performance] InitializeOnLoad ManagedDebuggerWindow                                                                         :        2 samples, Peak.  92.1 us (1.1x), Avg.  85.4 us, Total. 170.8 us (0.0%)
[Performance] InitializeOnLoad CacheServerWindow                                                                             :        2 samples, Peak.  7.75 us (1.1x), Avg.  7.29 us, Total. 14.58 us (0.0%)
[Performance] InitializeOnLoad PlayModeDownload                                                                              :        2 samples, Peak.  2.18 ms (1.9x), Avg.  1.13 ms, Total. 2.258 ms (0.0%)
[Performance] InitializeOnLoad MenuItems                                                                                     :        2 samples, Peak.  87.0 us (1.2x), Avg.  71.7 us, Total. 143.3 us (0.0%)
[Performance] InitializeOnLoad PrefabInstanceChangedListener                                                                 :        2 samples, Peak.   132 us (1.2x), Avg.   113 us, Total. 225.0 us (0.0%)
[Performance] InitializeOnLoad ObjectSelectorSearch                                                                          :        2 samples, Peak.  24.3 us (1.1x), Avg.  22.9 us, Total. 45.71 us (0.0%)
[Performance] InitializeOnLoad EditorMonitor                                                                                 :        2 samples, Peak.   537 us (1.2x), Avg.   444 us, Total. 888.1 us (0.0%)
[Performance] InitializeOnLoad RetainedMode                                                                                  :        2 samples, Peak.  1.77 ms (1.0x), Avg.  1.69 ms, Total. 3.385 ms (0.0%)
[Performance] InitializeOnLoad EditorShaderLoader                                                                            :        2 samples, Peak.   699 us (1.3x), Avg.   546 us, Total. 1.093 ms (0.0%)
[Performance] InitializeOnLoad EditorDelegateRegistration                                                                    :        2 samples, Peak.   423 us (1.1x), Avg.   379 us, Total. 758.8 us (0.0%)
[Performance] InitializeOnLoad UIDocumentHierarchyWatcher                                                                    :        2 samples, Peak.  48.9 us (1.0x), Avg.  48.8 us, Total. 97.63 us (0.0%)
[Performance] InitializeOnLoad LiveReloadTrackerCreator                                                                      :        2 samples, Peak.   204 us (1.3x), Avg.   153 us, Total. 305.3 us (0.0%)
[Performance] InitializeOnLoad UxmlObjectEditorFactories                                                                     :        2 samples, Peak.   405 us (1.1x), Avg.   374 us, Total. 748.0 us (0.0%)
[Performance] InitializeOnLoad UXMLEditorFactories                                                                           :        2 samples, Peak.  38.6 ms (1.1x), Avg.  35.8 ms, Total. 71.64 ms (0.5%)
[Performance] InitializeOnLoad PurchasingService                                                                             :        2 samples, Peak.  3.87 ms (1.0x), Avg.  3.71 ms, Total. 7.428 ms (0.1%)
[Performance] InitializeOnLoad CloudBuildPoller                                                                              :        2 samples, Peak.  61.1 us (1.1x), Avg.  57.9 us, Total. 115.8 us (0.0%)
[Performance] InitializeOnLoad EditorGameServicesAnalytics                                                                   :        2 samples, Peak.   866 us (1.4x), Avg.   634 us, Total. 1.269 ms (0.0%)
[Performance] InitializeOnLoad AnalyticsService                                                                              :        2 samples, Peak.   116 us (1.1x), Avg.   108 us, Total. 215.7 us (0.0%)
[Performance] InitializeOnLoad BuildService                                                                                  :        2 samples, Peak.  88.0 us (1.0x), Avg.  86.9 us, Total. 173.9 us (0.0%)
[Performance] InitializeOnLoad AdsService                                                                                    :        2 samples, Peak.   244 us (1.1x), Avg.   230 us, Total. 459.8 us (0.0%)
[Performance] InitializeOnLoad UDPService                                                                                    :        2 samples, Peak.  83.8 us (1.0x), Avg.  83.8 us, Total. 167.6 us (0.0%)
[Performance] InitializeOnLoad CrashService                                                                                  :        2 samples, Peak.  82.4 us (1.0x), Avg.  80.4 us, Total. 160.8 us (0.0%)
[Performance] InitializeOnLoad ServicesRepository                                                                            :        2 samples, Peak.   208 ns (1.0x), Avg.   208 ns, Total. 416.0 ns (0.0%)
[Performance] InitializeOnLoad SearchWindow                                                                                  :        2 samples, Peak.  71.6 us (1.1x), Avg.  66.7 us, Total. 133.4 us (0.0%)
[Performance] InitializeOnLoad CustomIndexers                                                                                :        2 samples, Peak.  8.79 ms (1.1x), Avg.  8.32 ms, Total. 16.64 ms (0.1%)
[Performance] InitializeOnLoad SearchMonitor                                                                                 :        2 samples, Peak.  1.73 ms (1.5x), Avg.  1.17 ms, Total. 2.347 ms (0.0%)
[Performance] InitializeOnLoad ParameterControllerEditor                                                                     :        2 samples, Peak.  17.8 us (1.1x), Avg.  15.9 us, Total. 31.83 us (0.0%)
[Performance] InitializeOnLoad LayerSettingsWindow                                                                           :        2 samples, Peak.  15.8 us (1.6x), Avg.  9.71 us, Total. 19.42 us (0.0%)
[Performance] InitializeOnLoad PackageManagerHookGUIDConverter.RegisterPackagesEventHandler                                  :        2 samples, Peak.   421 us (1.7x), Avg.   245 us, Total. 489.0 us (0.0%)
[Performance] InitializeOnLoad EditorDragAndDrop.RegisterEditorClient                                                        :        2 samples, Peak.   139 us (1.2x), Avg.   121 us, Total. 242.1 us (0.0%)
[Performance] InitializeOnLoad EditorEventCallbacks.InitializeFontAssetResourceChangeCallBacks                               :        2 samples, Peak.   650 us (1.0x), Avg.   649 us, Total. 1.299 ms (0.0%)
[Performance] InitializeOnLoad SceneTemplateService.Init                                                                     :        2 samples, Peak.   640 us (1.0x), Avg.   634 us, Total. 1.268 ms (0.0%)
[Performance] InitializeOnLoad DiagnosticSwitchesConsoleMessage.Init                                                         :        2 samples, Peak.  1.84 ms (1.7x), Avg.  1.11 ms, Total. 2.221 ms (0.0%)
[Performance] InitializeOnLoad PackageManagerWindow.EditorInitializedInSafeMode                                              :        2 samples, Peak.  15.6 ms (2.0x), Avg.  7.84 ms, Total. 15.67 ms (0.1%)
[Performance] InitializeOnLoad ToolShortcutContext.Init                                                                      :        2 samples, Peak.  75.5 us (1.0x), Avg.  74.5 us, Total. 149.0 us (0.0%)
[Performance] InitializeOnLoad MemoryProfilerCompilationGuard.InjectCompileGuard                                             :        2 samples, Peak.  84.5 us (1.1x), Avg.  79.7 us, Total. 159.5 us (0.0%)
[Performance] InitializeOnLoad AssetPostprocessingInternal.RefreshCustomDependencies                                         :        2 samples, Peak.  1.92 ms (1.6x), Avg.  1.22 ms, Total. 2.449 ms (0.0%)
[Performance] InitializeOnLoad SysrootManager.Initialize                                                                     :        2 samples, Peak.  4.79 us (1.1x), Avg.  4.56 us, Total. 9.124 us (0.0%)
[Performance] InitializeOnLoad ScopedRegistryAddedPopup.SubscribeToRegistriesAdded                                           :        2 samples, Peak.  4.23 ms (2.0x), Avg.  2.14 ms, Total. 4.284 ms (0.0%)
[Performance] InitializeOnLoad SceneVisibilityManager.Initialize                                                             :        2 samples, Peak.  1.84 ms (1.6x), Avg.  1.14 ms, Total. 2.280 ms (0.0%)
[Performance] InitializeOnLoad EditorWindow.Initialize                                                                       :        2 samples, Peak.   151 us (1.1x), Avg.   142 us, Total. 284.7 us (0.0%)
[Performance] AssemblyReloadEvents.afterAssemblyReload: UnityEditor.SceneTemplate.SceneTemplateService.AfterAssemblyReload   :        2 samples, Peak.  1.92 ms (1.9x), Avg.  1.04 ms, Total. 2.074 ms (0.0%)
[Performance] ProcessService.EditorAfterLoadAllAssemblies                                                                    :        2 samples, Peak.   333 us (1.2x), Avg.   271 us, Total. 541.6 us (0.0%)
[Performance] Application.ReadLicenseInfo                                                                                    :        1 samples, Peak.  26.0 ms (1.0x), Avg.  26.0 ms, Total. 26.00 ms (0.2%)
[Performance] Application.InitialRefresh                                                                                     :        1 samples, Peak.  2.99 s (1.0x), Avg.  2.99 s, Total. 2.987 s (22.7%)
[Performance] Compiling Scripts                                                                                              :        1 samples, Peak.   677 ms (1.0x), Avg.   677 ms, Total. 677.0 ms (5.1%)
[Performance] AssemblyReloadEvents.beforeAssemblyReload: UnityEditor.SceneTemplate.SceneTemplateService.BeforeAssemblyReload :        1 samples, Peak.   618 us (1.0x), Avg.   618 us, Total. 618.4 us (0.0%)
[Performance] InitializeOnLoad TouchSimulation                                                                               :        1 samples, Peak.   160 us (1.0x), Avg.   160 us, Total. 160.3 us (0.0%)
[Performance] InitializeOnLoad EnableUITKEditor                                                                              :        1 samples, Peak.  14.1 us (1.0x), Avg.  14.1 us, Total. 14.08 us (0.0%)
[Performance] InitializeOnLoad InputSystem                                                                                   :        1 samples, Peak.  28.1 ms (1.0x), Avg.  28.1 ms, Total. 28.12 ms (0.2%)
[Performance] InitializeOnLoad InputSystemUIInputModuleEditor                                                                :        1 samples, Peak.  81.3 us (1.0x), Avg.  81.3 us, Total. 81.25 us (0.0%)
[Performance] InitializeOnLoad DeEditorNotification                                                                          :        1 samples, Peak.   331 us (1.0x), Avg.   331 us, Total. 330.5 us (0.0%)
[Performance] InitializeOnLoad DeScriptExecutionOrderManager                                                                 :        1 samples, Peak.   144 us (1.0x), Avg.   144 us, Total. 144.4 us (0.0%)
[Performance] InitializeOnLoad DOTweenProEditorManager                                                                       :        1 samples, Peak.   282 us (1.0x), Avg.   282 us, Total. 282.1 us (0.0%)
[Performance] InitializeOnLoad Initializer                                                                                   :        2 samples, Peak.   108 us (1.2x), Avg.  93.2 us, Total. 186.5 us (0.0%)
[Performance] InitializeOnLoad ReadmeEditor                                                                                  :        1 samples, Peak.  77.8 us (1.0x), Avg.  77.8 us, Total. 77.75 us (0.0%)
[Performance] InitializeOnLoad VSUsageUtility                                                                                :        1 samples, Peak.  20.7 ms (1.0x), Avg.  20.7 ms, Total. 20.66 ms (0.2%)
[Performance] InitializeOnLoad BackgroundWatcher                                                                             :        1 samples, Peak.  86.6 us (1.0x), Avg.  86.6 us, Total. 86.58 us (0.0%)
[Performance] InitializeOnLoad UnityTestProtocolStarter                                                                      :        1 samples, Peak.   174 us (1.0x), Avg.   174 us, Total. 173.6 us (0.0%)
[Performance] InitializeOnLoad TestStarter                                                                                   :        1 samples, Peak.   353 us (1.0x), Avg.   353 us, Total. 353.3 us (0.0%)
[Performance] InitializeOnLoad RerunCallbackInitializer                                                                      :        1 samples, Peak.  1.84 ms (1.0x), Avg.  1.84 ms, Total. 1.837 ms (0.0%)
[Performance] InitializeOnLoad VisualStudioEditor                                                                            :        1 samples, Peak.  8.96 ms (1.0x), Avg.  8.96 ms, Total. 8.964 ms (0.1%)
[Performance] InitializeOnLoad TestRunnerApiListener                                                                         :        1 samples, Peak.   486 us (1.0x), Avg.   486 us, Total. 486.3 us (0.0%)
[Performance] InitializeOnLoad VisualStudioIntegration                                                                       :        1 samples, Peak.  1.07 ms (1.0x), Avg.  1.07 ms, Total. 1.070 ms (0.0%)
[Performance] InitializeOnLoad PrefabLayoutRebuilder                                                                         :        1 samples, Peak.   203 us (1.0x), Avg.   203 us, Total. 202.7 us (0.0%)
[Performance] InitializeOnLoad RiderScriptEditor                                                                             :        1 samples, Peak.  4.77 ms (1.0x), Avg.  4.77 ms, Total. 4.771 ms (0.0%)
[Performance] InitializeOnLoad CallbackInitializer                                                                           :        1 samples, Peak.  1.51 ms (1.0x), Avg.  1.51 ms, Total. 1.512 ms (0.0%)
[Performance] InitializeOnLoad SpriteEditorWindow                                                                            :        1 samples, Peak.   242 us (1.0x), Avg.   242 us, Total. 242.4 us (0.0%)
[Performance] InitializeOnLoad CoverageReporterStarter                                                                       :        1 samples, Peak.  3.23 ms (1.0x), Avg.  3.23 ms, Total. 3.231 ms (0.0%)
[Performance] InitializeOnLoad PlasticPlugin                                                                                 :        1 samples, Peak.  34.4 ms (1.0x), Avg.  34.4 ms, Total. 34.40 ms (0.3%)
[Performance] InitializeOnLoad FungusEditorPreferences                                                                       :        1 samples, Peak.   132 us (1.0x), Avg.   132 us, Total. 132.0 us (0.0%)
[Performance] InitializeOnLoad HierarchyIcons                                                                                :        1 samples, Peak.  85.7 us (1.0x), Avg.  85.7 us, Total. 85.67 us (0.0%)
[Performance] InitializeOnLoad ToolbarBootstrap                                                                              :        1 samples, Peak.  28.3 ms (1.0x), Avg.  28.3 ms, Total. 28.27 ms (0.2%)
[Performance] InitializeOnLoad VSCodeScriptEditor                                                                            :        1 samples, Peak.  1.35 ms (1.0x), Avg.  1.35 ms, Total. 1.346 ms (0.0%)
[Performance] InitializeOnLoad SysrootPackage.IssueWarningIfLinuxIL2CPPNotPresent                                            :        1 samples, Peak.   106 us (1.0x), Avg.   106 us, Total. 105.8 us (0.0%)
[Performance] InitializeOnLoad InputSystemPackageControl.SubscribePackageManagerEvent                                        :        1 samples, Peak.  38.6 us (1.0x), Avg.  38.6 us, Total. 38.58 us (0.0%)
[Performance] InitializeOnLoad InputSystemPluginControl.CheckForExtension                                                    :        1 samples, Peak.   849 us (1.0x), Avg.   849 us, Total. 849.5 us (0.0%)
[Performance] InitializeOnLoad TestJobDataHolder.ResumeRunningJobs                                                           :        1 samples, Peak.   551 us (1.0x), Avg.   551 us, Total. 551.2 us (0.0%)
[Performance] InitializeOnLoad AnalyticsReporter.RegisterCallbacks                                                           :        1 samples, Peak.   163 us (1.0x), Avg.   163 us, Total. 163.0 us (0.0%)
[Performance] InitializeOnLoad VisualStudioEditor.LegacyVisualStudioCodePackageDisabler                                      :        1 samples, Peak.  1.68 ms (1.0x), Avg.  1.68 ms, Total. 1.683 ms (0.0%)
[Performance] DidReloadScriptsNavMeshExtension.OnScriptReload                                                                :        1 samples, Peak.   115 us (1.0x), Avg.   115 us, Total. 114.6 us (0.0%)
[Performance] DidReloadScriptsFungusEditorResources.OnDidReloadScripts                                                       :        1 samples, Peak.  43.2 ms (1.0x), Avg.  43.2 ms, Total. 43.18 ms (0.3%)
[Performance] DidReloadScriptsEventSelectorPopupWindowContent.OnScriptsReloaded                                              :        1 samples, Peak.   490 us (1.0x), Avg.   490 us, Total. 489.6 us (0.0%)
[Performance] DidReloadScriptsCommandSelectorPopupWindowContent.OnScriptsReloaded                                            :        1 samples, Peak.   236 us (1.0x), Avg.   236 us, Total. 236.5 us (0.0%)
[Performance] DidReloadScriptsVariableSelectPopupWindowContent.OnScriptsReloaded                                             :        1 samples, Peak.   114 us (1.0x), Avg.   114 us, Total. 114.0 us (0.0%)
[Performance] DidReloadScriptsAdvancedDropdownWindow.OnScriptReload                                                          :        2 samples, Peak.   143 us (1.2x), Avg.   124 us, Total. 248.0 us (0.0%)
[Performance] DidReloadScriptsTestRunnerWindow.CompilationCallback                                                           :        1 samples, Peak.   276 us (1.0x), Avg.   276 us, Total. 276.3 us (0.0%)
[Performance] DidReloadScriptsTestListCache.ScriptReloaded                                                                   :        1 samples, Peak.   751 us (1.0x), Avg.   751 us, Total. 751.1 us (0.0%)
[Performance] DidReloadScriptsLuaBindingsEditor.DidReloadScripts                                                             :        1 samples, Peak.   229 us (1.0x), Avg.   229 us, Total. 228.7 us (0.0%)
[Performance] InputActionAssetPostprocessor.OnPostprocessAllAssets                                                           :        2 samples, Peak.   257 us (1.2x), Avg.   220 us, Total. 439.5 us (0.0%)
[Performance] InputActionJsonNameModifierAssetProcessor.OnPostprocessAllAssets                                               :        2 samples, Peak.  27.7 us (1.0x), Avg.  26.9 us, Total. 53.88 us (0.0%)
[Performance] ProjectSettingsPostprocessor.OnPostprocessAllAssets                                                            :        2 samples, Peak.   115 us (1.0x), Avg.   114 us, Total. 228.6 us (0.0%)
[Performance] SyncVS.PostprocessSyncProject                                                                                  :        1 samples, Peak.  1.70 ms (1.0x), Avg.  1.70 ms, Total. 1.701 ms (0.0%)
[Performance] Application.ImportPackagesAndSetTemplateWhenCreatingProject                                                    :        1 samples, Peak.  30.6 ms (1.0x), Avg.  30.6 ms, Total. 30.64 ms (0.2%)
[Performance] Application.SyncCurrentColorSpace                                                                              :        1 samples, Peak.  82.6 ms (1.0x), Avg.  82.6 ms, Total. 82.60 ms (0.6%)
[Performance] Application.OnUsbDevicesChanged                                                                                :        1 samples, Peak.  43.5 us (1.0x), Avg.  43.5 us, Total. 43.54 us (0.0%)
[Performance] Application.AssetInstanceCacheUpdate                                                                           :        1 samples, Peak.   167 ns (1.0x), Avg.   167 ns, Total. 167.0 ns (0.0%)
[Performance] Application.UnityExtensions.Initialize                                                                         :        1 samples, Peak.  4.39 ms (1.0x), Avg.  4.39 ms, Total. 4.389 ms (0.0%)
[Performance] CodeEditorProjectSync.SyncEditorProject                                                                        :        1 samples, Peak.   146 ms (1.0x), Avg.   146 ms, Total. 145.5 ms (1.1%)
[Performance] Application.ExecuteStartups                                                                                    :        1 samples, Peak.  51.7 ms (1.0x), Avg.  51.7 ms, Total. 51.67 ms (0.4%)
[Performance] Menu.RegisterMenuInterface                                                                                     :       26 samples, Peak.   833 ns (5.8x), Avg.   144 ns, Total. 3.751 us (0.0%)
[Performance] Gizmo.RebuildRenderers                                                                                         :        1 samples, Peak.  47.8 ms (1.0x), Avg.  47.8 ms, Total. 47.78 ms (0.4%)
[Performance] Gizmo.AddGizmoRenderers                                                                                        :       96 samples, Peak.   932 us (49.0x), Avg.  19.0 us, Total. 1.828 ms (0.0%)
[Performance] Application.editorInitializingProject                                                                          :        1 samples, Peak.  15.7 ms (1.0x), Avg.  15.7 ms, Total. 15.71 ms (0.1%)
[Performance] Application.InitializeMenu                                                                                     :        1 samples, Peak.   810 ms (1.0x), Avg.   810 ms, Total. 809.9 ms (6.2%)
[Performance] Menu.RebuildAll                                                                                                :        1 samples, Peak.   810 ms (1.0x), Avg.   810 ms, Total. 809.9 ms (6.2%)
[Performance] Menu.BuildRegisteredMenuInterfaces                                                                             :        1 samples, Peak.   805 ms (1.0x), Avg.   805 ms, Total. 804.9 ms (6.1%)
[Performance] Menu.FilterMenuItem                                                                                            :      796 samples, Peak.  20.5 ms (788.3x), Avg.  26.0 us, Total. 20.73 ms (0.2%)
[Performance] UpdateAllMenus                                                                                                 :        1 samples, Peak.   577 us (1.0x), Avg.   577 us, Total. 577.3 us (0.0%)
[Performance] EditorSceneManager.sceneClosing: UnityEditor.SceneVisibilityManager.EditorSceneManagerOnSceneClosing           :        1 samples, Peak.   163 us (1.0x), Avg.   163 us, Total. 163.3 us (0.0%)
[Performance] GUIView.RepaintAll.PlayerLoopController                                                                        :        1 samples, Peak.   417 ns (1.0x), Avg.   417 ns, Total. 417.0 ns (0.0%)
[Performance] EditorSceneManager.newSceneCreated: UnityEditor.SceneTemplate.SceneTemplateService.OnNewSceneCreated           :        1 samples, Peak.   155 us (1.0x), Avg.   155 us, Total. 154.8 us (0.0%)
[Performance] EditorSceneManager.newSceneCreated: UnityEditor.SceneVisibilityManager.EditorSceneManagerOnNewSceneCreated     :        1 samples, Peak.   108 us (1.0x), Avg.   108 us, Total. 107.8 us (0.0%)
[Performance] EditorSceneManager.newSceneCreated: UnityEditor.SceneManagement.StageNavigationManager.OnNewSceneCreated       :        1 samples, Peak.  32.1 us (1.0x), Avg.  32.1 us, Total. 32.08 us (0.0%)
[Performance] Application.InvokeFinishedLoadingProject                                                                       :        1 samples, Peak.  8.78 ms (1.0x), Avg.  8.78 ms, Total. 8.782 ms (0.1%)
[Performance] ProcessService.OnProjectLoaded                                                                                 :        1 samples, Peak.  32.0 us (1.0x), Avg.  32.0 us, Total. 32.04 us (0.0%)
[Performance] VersionControl.Task.Wait                                                                                       :        1 samples, Peak.  46.8 us (1.0x), Avg.  46.8 us, Total. 46.79 us (0.0%)
[Performance] EditorApplication.quitting: UnityEditor.SettingsManagement.FileSettingsRepository.Save                         :        1 samples, Peak.   791 us (1.0x), Avg.   791 us, Total. 791.1 us (0.0%)
[Performance] EditorApplication.quitting: callback in UnityEditor.TextCore.Text.EditorEventCallbacks                         :        1 samples, Peak.  99.8 ms (1.0x), Avg.  99.8 ms, Total. 99.82 ms (0.8%)
[Performance] Killing ADB server                                                                                             :        1 samples, Peak.  34.4 ms (1.0x), Avg.  34.4 ms, Total. 34.36 ms (0.3%)
[Performance] Application.Shutdown.PauseProfilerSession                                                                      :        1 samples, Peak.   456 us (1.0x), Avg.   456 us, Total. 455.5 us (0.0%)
[Performance] Application.Shutdown.PauseAssetImportWorkers                                                                   :        1 samples, Peak.   283 us (1.0x), Avg.   283 us, Total. 283.5 us (0.0%)
[Performance] Application.Shutdown.SaveAssets                                                                                :        1 samples, Peak.  6.19 ms (1.0x), Avg.  6.19 ms, Total. 6.186 ms (0.0%)
[Performance] StateMacroSavedEvent.OnWillSaveAssets                                                                          :        1 samples, Peak.   127 us (1.0x), Avg.   127 us, Total. 127.0 us (0.0%)
[Performance] AssetModProcessor.OnWillSaveAssets                                                                             :        1 samples, Peak.   170 us (1.0x), Avg.   170 us, Total. 170.3 us (0.0%)
[Performance] AssetModificationProcessor.OnWillSaveAssets                                                                    :        1 samples, Peak.   194 us (1.0x), Avg.   194 us, Total. 194.3 us (0.0%)
[Performance] UnityCloudProjectLinkMonitor.OnWillSaveAssets                                                                  :        1 samples, Peak.   211 us (1.0x), Avg.   211 us, Total. 211.1 us (0.0%)
[Performance] FlowMacroSavedEvent.OnWillSaveAssets                                                                           :        1 samples, Peak.  79.4 us (1.0x), Avg.  79.4 us, Total. 79.42 us (0.0%)
[Performance] BuilderAssetModificationProcessor.OnWillSaveAssets                                                             :        1 samples, Peak.   296 us (1.0x), Avg.   296 us, Total. 296.2 us (0.0%)
[Performance] TerrainModificationProcessor.OnWillSaveAssets                                                                  :        1 samples, Peak.   408 us (1.0x), Avg.   408 us, Total. 407.5 us (0.0%)
[Performance] AssetDatabase.ImportAssets                                                                                     :        2 samples, Peak.   333 ns (1.8x), Avg.   188 ns, Total. 375.0 ns (0.0%)
[Performance] Application.Shutdown.CleanupRenderPipeline                                                                     :        1 samples, Peak.  9.13 us (1.0x), Avg.  9.13 us, Total. 9.125 us (0.0%)
[Performance] Application.Shutdown.StopPreloadManager                                                                        :        1 samples, Peak.  11.0 ms (1.0x), Avg.  11.0 ms, Total. 11.04 ms (0.1%)
[Performance] Application.Shutdown.DestroyWorld                                                                              :        1 samples, Peak.   554 us (1.0x), Avg.   554 us, Total. 553.7 us (0.0%)
[Performance] Application.Shutdown.CleanupAfterLoad                                                                          :        1 samples, Peak.  2.84 ms (1.0x), Avg.  2.84 ms, Total. 2.845 ms (0.0%)
[Performance] Application.Shutdown.Progress                                                                                  :        1 samples, Peak.  20.3 us (1.0x), Avg.  20.3 us, Total. 20.33 us (0.0%)
[Performance] Application.Shutdown.GICleanupManagers                                                                         :        1 samples, Peak.  2.05 ms (1.0x), Avg.  2.05 ms, Total. 2.051 ms (0.0%)
[Performance] Application.Shutdown.MenuCleanupClass                                                                          :        1 samples, Peak.   143 us (1.0x), Avg.   143 us, Total. 143.3 us (0.0%)
[Performance] Application.Shutdown.ADBSaveStateBeforeShutdown                                                                :        1 samples, Peak.  10.0 us (1.0x), Avg.  10.0 us, Total. 10.04 us (0.0%)
[Performance] Application.Shutdown.RemoteShutdown                                                                            :        1 samples, Peak.   625 ns (1.0x), Avg.   625 ns, Total. 625.0 ns (0.0%)
[Performance] Application.Shutdown.CleanupVCProvider                                                                         :        1 samples, Peak.   173 us (1.0x), Avg.   173 us, Total. 173.3 us (0.0%)
[Performance] Application.Shutdown.InputShutdown                                                                             :        1 samples, Peak.   147 us (1.0x), Avg.   147 us, Total. 147.2 us (0.0%)
[Performance] Application.Shutdown.GizmoManagerDestroy                                                                       :        1 samples, Peak.   349 us (1.0x), Avg.   349 us, Total. 349.3 us (0.0%)
[Performance] Application.Shutdown.ProfilerSession                                                                           :        1 samples, Peak.   484 us (1.0x), Avg.   484 us, Total. 484.1 us (0.0%)
[Performance] Application.Shutdown.ReleaseGfxWindowOnAllGUIViews                                                             :        1 samples, Peak.  1.42 us (1.0x), Avg.  1.42 us, Total. 1.416 us (0.0%)
[Performance] Application.Shutdown.CleanupEngine                                                                             :        1 samples, Peak.   124 ms (1.0x), Avg.   124 ms, Total. 124.0 ms (0.9%)
[Performance] Application.Shutdown.CleanupAssetDatabase                                                                      :        1 samples, Peak.  4.32 ms (1.0x), Avg.  4.32 ms, Total. 4.317 ms (0.0%)
[Performance] Application.Shutdown.ScriptCompilationCleanUp                                                                  :        1 samples, Peak.  19.3 us (1.0x), Avg.  19.3 us, Total. 19.33 us (0.0%)
[Performance] Application.Shutdown.DestroyJobSystem                                                                          :        1 samples, Peak.   397 us (1.0x), Avg.   397 us, Total. 397.3 us (0.0%)
[Performance] Application.Shutdown.CleanupPersistentManager                                                                  :        1 samples, Peak.  2.16 ms (1.0x), Avg.  2.16 ms, Total. 2.157 ms (0.0%)
[Performance] Application.Shutdown.CleanupAsyncReadManager                                                                   :        1 samples, Peak.  97.2 us (1.0x), Avg.  97.2 us, Total. 97.17 us (0.0%)
[Performance] Application.Shutdown.CleanupMono                                                                               :        1 samples, Peak.  51.7 ms (1.0x), Avg.  51.7 ms, Total. 51.72 ms (0.4%)
[Performance] Application.Shutdown.CleanupStdConverters                                                                      :        1 samples, Peak.   156 us (1.0x), Avg.   156 us, Total. 155.9 us (0.0%)
[Performance] Application.Shutdown.UnloadAllPlatformSupportModuleNativeDlls                                                  :        1 samples, Peak.   417 ns (1.0x), Avg.   417 ns, Total. 417.0 ns (0.0%)
[Performance] Application.Shutdown.UnloadAllPlatformSupportNativeLibraries                                                   :        1 samples, Peak.  90.7 us (1.0x), Avg.  90.7 us, Total. 90.67 us (0.0%)
[Performance] Application.Shutdown.CleanupAutoDocumentation                                                                  :        1 samples, Peak.   330 us (1.0x), Avg.   330 us, Total. 330.5 us (0.0%)
[Performance] Application.Shutdown.ShaderNameManagerDestroy                                                                  :        1 samples, Peak.  41.4 us (1.0x), Avg.  41.4 us, Total. 41.42 us (0.0%)
[Performance] Application.Shutdown.CleanupCacheServer                                                                        :        1 samples, Peak.   209 ns (1.0x), Avg.   209 ns, Total. 209.0 ns (0.0%)
[Performance] Application.Shutdown.Virtualization_Shutdown                                                                   :        1 samples, Peak.   375 ns (1.0x), Avg.   375 ns, Total. 375.0 ns (0.0%)
[Performance] Application.Shutdown.DevConnections                                                                            :        1 samples, Peak.  62.1 us (1.0x), Avg.  62.1 us, Total. 62.12 us (0.0%)
Exiting batchmode successfully now!
[Package Manager] Server::Kill -- Server was shutdown
