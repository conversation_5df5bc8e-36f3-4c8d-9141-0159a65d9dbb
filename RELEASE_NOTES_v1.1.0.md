# 香域 (Perfumer Project) - Release v1.1.0

**发布日期**: 2025年6月25日  
**构建版本**: Build 2  
**Unity版本**: 2022.3.62f1

## 🎉 版本亮点

这是香域项目的重大更新版本，集成了完整的Unity多平台构建系统，大幅提升了开发效率和项目管理能力。

## 🚀 新功能特性

### 🛠️ Unity多平台构建系统
- ✅ **一键构建支持**: 支持iOS、Android、Windows、Mac、Linux多平台构建
- ✅ **命令行工具**: 完整的CLI构建接口，支持CI/CD集成
- ✅ **Unity编辑器集成**: 菜单快速访问和可视化构建窗口
- ✅ **版本管理**: 自动版本号管理和构建编号递增
- ✅ **Git集成**: 自动生成基于Git提交记录的版本说明

### 📋 构建配置管理
- ✅ **配置化构建**: 支持自定义构建参数和平台设置
- ✅ **构建日志**: 详细的构建过程记录和错误报告
- ✅ **输出管理**: 统一的构建输出目录结构

### 🔧 技术改进
- ✅ **依赖管理**: 添加Newtonsoft.Json包支持JSON序列化
- ✅ **编译优化**: 修复所有编译错误和警告
- ✅ **项目结构**: 优化项目组织和文件结构

## 📁 构建输出

### Mac版本 (已验证)
- **文件**: `Builds/mac/香域.app`
- **大小**: 329MB
- **状态**: ✅ 构建成功，完全可用

### 其他平台
- **Windows**: 需要安装Windows Build Support模块
- **Linux**: 需要安装Linux Build Support模块
- **iOS**: 需要安装iOS Build Support模块
- **Android**: 需要安装Android Build Support模块

## 🛠️ 使用方法

### 命令行构建
```bash
# 构建Mac版本
./build.sh -p mac

# 构建指定版本
./build.sh -p mac -v 1.1.0

# 开发版本构建
./build.sh -p mac --development

# 查看帮助
./build.sh --help
```

### Unity编辑器内构建
1. 打开Unity编辑器
2. 菜单: `Build Tools > Multi-Platform Builder`
3. 选择目标平台并点击构建

### 快速构建菜单
- `Build Tools > Build All Platforms` - 构建所有平台
- `Build Tools > Build Mac` - 仅构建Mac版本
- `Build Tools > Build Windows` - 仅构建Windows版本

## 📋 系统要求

### 开发环境
- **Unity**: 2022.3.62f1 或更高版本
- **操作系统**: macOS (当前测试环境)
- **Git**: 用于版本控制和自动changelog生成

### 运行环境
- **Mac**: macOS 10.14 或更高版本
- **Windows**: Windows 10 或更高版本 (需要构建支持)
- **Linux**: Ubuntu 18.04 或更高版本 (需要构建支持)

## 🔧 技术细节

### 新增文件
- `Assets/Editor/MultiPlatformBuilder.cs` - 主构建器窗口
- `Assets/Editor/BuildConfiguration.cs` - 构建配置管理
- `Assets/Editor/CommandLineBuild.cs` - 命令行构建支持
- `build.sh` - Mac/Linux构建脚本
- `build.bat` - Windows构建脚本
- `BUILD_SYSTEM_README.md` - 构建系统文档

### 依赖更新
- 添加 `com.unity.nuget.newtonsoft-json@3.2.1`

### 配置文件
- `version.json` - 版本信息管理
- `Packages/manifest.json` - 包依赖配置

## 🚨 已知问题

1. **跨平台构建限制**: 
   - 在Mac上构建Windows/Linux需要安装相应的Build Support模块
   - 可通过Unity Hub安装额外的平台支持

2. **Unity实例冲突**: 
   - 命令行构建时需要关闭Unity编辑器
   - 避免多个Unity实例同时打开同一项目

## 🔄 升级指南

从v1.0.0升级到v1.1.0:

1. **备份项目**: 建议先备份当前项目
2. **更新依赖**: 新版本会自动安装Newtonsoft.Json包
3. **测试构建**: 运行 `./build.sh -p mac` 测试构建系统
4. **安装平台支持**: 根据需要安装其他平台的Build Support模块

## 🤝 贡献与反馈

如有问题或建议，请通过以下方式联系：
- 项目仓库: `/Users/<USER>/Documents/GitHub/PerfumerProject`
- 构建日志: `build.log`

## 📄 许可证

本项目基于MIT许可证开源。

---

**构建信息**:
- 构建时间: 2025-06-25 16:50:00
- 构建编号: 2
- Unity版本: 2022.3.62f1
- 构建平台: macOS (Apple M1)
