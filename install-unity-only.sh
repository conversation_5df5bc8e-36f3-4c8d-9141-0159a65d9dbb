#!/bin/bash

# 仅安装Unity开发环境的轻量级脚本
# 使用curl下载和安装Unity Hub和Unity Editor

# 配置
UNITY_VERSION="2022.3.8f1"

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m'

echo -e "${BLUE}=========================================${NC}"
echo -e "${BLUE}  Unity开发环境快速安装 (仅Unity)${NC}"
echo -e "${BLUE}=========================================${NC}"

# 检查操作系统
if [[ "$OSTYPE" != "darwin"* ]]; then
    echo -e "${RED}此脚本仅支持macOS系统！${NC}"
    exit 1
fi

# 检查命令是否存在
command_exists() {
    command -v "$1" >/dev/null 2>&1
}

# 安装Unity Hub
install_unity_hub() {
    echo -e "${BLUE}正在安装Unity Hub...${NC}"
    
    if [ -d "/Applications/Unity Hub.app" ]; then
        echo -e "${GREEN}Unity Hub已经安装。${NC}"
        return 0
    fi
    
    # 下载Unity Hub
    local unity_hub_url="https://public-cdn.cloud.unity3d.com/hub/prod/UnityHub.dmg"
    local temp_dmg="/tmp/UnityHub.dmg"
    
    echo -e "${BLUE}正在通过curl下载Unity Hub...${NC}"
    if curl -L -o "$temp_dmg" "$unity_hub_url"; then
        echo -e "${GREEN}Unity Hub下载完成${NC}"
    else
        echo -e "${RED}Unity Hub下载失败${NC}"
        return 1
    fi
    
    # 挂载DMG文件
    echo -e "${BLUE}正在安装Unity Hub...${NC}"
    if hdiutil attach "$temp_dmg" -quiet; then
        # 复制到Applications目录
        if cp -R "/Volumes/Unity Hub/Unity Hub.app" "/Applications/"; then
            echo -e "${GREEN}Unity Hub安装成功！${NC}"
        else
            echo -e "${RED}Unity Hub安装失败${NC}"
            return 1
        fi
        
        # 卸载DMG
        hdiutil detach "/Volumes/Unity Hub" -quiet
    else
        echo -e "${RED}无法挂载Unity Hub安装文件${NC}"
        return 1
    fi
    
    # 清理临时文件
    rm -f "$temp_dmg"
    return 0
}

# 安装Unity Editor
install_unity_editor() {
    echo -e "${BLUE}正在安装Unity Editor $UNITY_VERSION...${NC}"
    
    # 检查Unity版本是否已安装
    local unity_path="/Applications/Unity/Hub/Editor/$UNITY_VERSION/Unity.app"
    if [ -d "$unity_path" ]; then
        echo -e "${GREEN}Unity $UNITY_VERSION 已经安装。${NC}"
        return 0
    fi
    
    # 使用Unity Hub CLI安装Unity Editor
    local unity_hub_cli="/Applications/Unity Hub.app/Contents/MacOS/Unity Hub"
    
    if [ -f "$unity_hub_cli" ]; then
        echo -e "${BLUE}正在通过Unity Hub CLI安装Unity $UNITY_VERSION...${NC}"
        echo -e "${YELLOW}这可能需要几分钟时间，请耐心等待...${NC}"
        
        # 安装Unity及常用模块
        if "$unity_hub_cli" -- --headless install --version "$UNITY_VERSION" --module android,ios,mac-il2cpp,windows-mono,linux-il2cpp,webgl; then
            echo -e "${GREEN}Unity $UNITY_VERSION 安装成功！${NC}"
        else
            echo -e "${YELLOW}Unity CLI安装失败，请手动通过Unity Hub安装${NC}"
            echo -e "${YELLOW}1. 打开Unity Hub${NC}"
            echo -e "${YELLOW}2. 转到'安装'标签页${NC}"
            echo -e "${YELLOW}3. 点击'安装编辑器'${NC}"
            echo -e "${YELLOW}4. 选择版本 $UNITY_VERSION${NC}"
            echo -e "${YELLOW}5. 添加模块：Android, iOS, Mac, Windows, Linux, WebGL${NC}"
        fi
    else
        echo -e "${RED}Unity Hub CLI未找到，请确保Unity Hub已正确安装${NC}"
        return 1
    fi
}

# 验证安装
verify_installation() {
    echo -e "${BLUE}正在验证安装...${NC}"
    
    local success=true
    
    # 检查Unity Hub
    if [ -d "/Applications/Unity Hub.app" ]; then
        echo -e "${GREEN}✓ Unity Hub${NC}"
    else
        echo -e "${RED}✗ Unity Hub${NC}"
        success=false
    fi
    
    # 检查Unity Editor
    if [ -d "/Applications/Unity/Hub/Editor/$UNITY_VERSION/Unity.app" ]; then
        echo -e "${GREEN}✓ Unity Editor $UNITY_VERSION${NC}"
    else
        echo -e "${YELLOW}⚠ Unity Editor $UNITY_VERSION (可能需要手动安装)${NC}"
    fi
    
    if [ "$success" = true ]; then
        echo -e "${GREEN}Unity开发环境安装完成！${NC}"
    else
        echo -e "${YELLOW}安装过程中遇到一些问题，请检查上述输出${NC}"
    fi
}

# 显示使用说明
show_usage_info() {
    echo -e "${BLUE}\n=========================================${NC}"
    echo -e "${BLUE}    使用说明${NC}"
    echo -e "${BLUE}=========================================${NC}"
    
    echo -e "${GREEN}下一步操作：${NC}"
    echo -e "${YELLOW}1. 打开Unity Hub${NC}"
    echo -e "${YELLOW}2. 登录您的Unity账户${NC}"
    echo -e "${YELLOW}3. 如果Unity Editor未自动安装，请手动安装版本 $UNITY_VERSION${NC}"
    echo -e "${YELLOW}4. 打开您的Unity项目${NC}"
    
    echo -e "${GREEN}\n项目相关：${NC}"
    echo -e "${YELLOW}• 项目路径：$(pwd)${NC}"
    echo -e "${YELLOW}• Unity版本：$UNITY_VERSION${NC}"
    echo -e "${YELLOW}• 支持平台：iOS, Android, Windows, Mac, Linux, WebGL${NC}"
    
    echo -e "${BLUE}\n=========================================${NC}"
}

# 主函数
main() {
    echo -e "${GREEN}开始安装Unity开发环境...${NC}"
    echo -e "${YELLOW}此脚本将安装Unity Hub和Unity Editor $UNITY_VERSION${NC}"
    
    # 确认安装
    read -p $'\n是否继续安装？(y/n): ' -n 1 -r
    echo
    if [[ ! $REPLY =~ ^[Yy]$ ]]; then
        echo -e "${YELLOW}安装已取消。${NC}"
        exit 0
    fi
    
    # 使脚本可执行
    chmod +x "$0"
    
    # 执行安装步骤
    if install_unity_hub; then
        install_unity_editor
        verify_installation
        show_usage_info
    else
        echo -e "${RED}Unity Hub安装失败，无法继续${NC}"
        exit 1
    fi
    
    echo -e "${GREEN}\n安装脚本执行完成！${NC}"
}

# 运行主函数
main "$@"
