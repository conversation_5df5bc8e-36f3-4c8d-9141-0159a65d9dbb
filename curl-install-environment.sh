#!/bin/bash

# Unity Development Environment Setup using curl
# This script uses curl for most installations where possible

# Configuration
UNITY_VERSION="2022.3.8f1"
NODE_VERSION="18.17.0"

# Colors
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

echo -e "${BLUE}=========================================${NC}"
echo -e "${BLUE}  Unity Environment Setup via curl${NC}"
echo -e "${BLUE}=========================================${NC}"

# Function to check if command exists
command_exists() {
    command -v "$1" >/dev/null 2>&1
}

# Install Homebrew using curl
install_homebrew_curl() {
    echo -e "${BLUE}Installing Homebrew via curl...${NC}"
    
    if command_exists brew; then
        echo -e "${GREEN}Homebrew is already installed.${NC}"
        return
    fi
    
    /bin/bash -c "$(curl -fsSL https://raw.githubusercontent.com/Homebrew/install/HEAD/install.sh)"
    
    # Add Homebrew to PATH for Apple Silicon Macs
    if [[ $(uname -m) == "arm64" ]]; then
        echo 'eval "$(/opt/homebrew/bin/brew shellenv)"' >> ~/.zprofile
        eval "$(/opt/homebrew/bin/brew shellenv)"
    fi
    
    echo -e "${GREEN}Homebrew installed successfully via curl!${NC}"
}

# Install Node.js using curl (via nvm)
install_nodejs_curl() {
    echo -e "${BLUE}Installing Node.js via curl (nvm)...${NC}"
    
    if command_exists node; then
        echo -e "${GREEN}Node.js is already installed.${NC}"
        return
    fi
    
    # Install nvm via curl
    curl -o- https://raw.githubusercontent.com/nvm-sh/nvm/v0.39.0/install.sh | bash
    
    # Source nvm
    export NVM_DIR="$HOME/.nvm"
    [ -s "$NVM_DIR/nvm.sh" ] && \. "$NVM_DIR/nvm.sh"
    [ -s "$NVM_DIR/bash_completion" ] && \. "$NVM_DIR/bash_completion"
    
    # Install specific Node.js version
    nvm install $NODE_VERSION
    nvm use $NODE_VERSION
    nvm alias default $NODE_VERSION
    
    echo -e "${GREEN}Node.js $NODE_VERSION installed successfully via curl!${NC}"
}

# Install Unity Hub using curl
install_unity_hub_curl() {
    echo -e "${BLUE}Installing Unity Hub via curl...${NC}"
    
    if [ -d "/Applications/Unity Hub.app" ]; then
        echo -e "${GREEN}Unity Hub is already installed.${NC}"
        return
    fi
    
    # Download Unity Hub
    UNITY_HUB_URL="https://public-cdn.cloud.unity3d.com/hub/prod/UnityHub.dmg"
    TEMP_DMG="/tmp/UnityHub.dmg"
    
    echo -e "${BLUE}Downloading Unity Hub via curl...${NC}"
    curl -L -o "$TEMP_DMG" "$UNITY_HUB_URL"
    
    # Mount and install
    echo -e "${BLUE}Installing Unity Hub...${NC}"
    hdiutil attach "$TEMP_DMG" -quiet
    cp -R "/Volumes/Unity Hub/Unity Hub.app" "/Applications/"
    hdiutil detach "/Volumes/Unity Hub" -quiet
    rm "$TEMP_DMG"
    
    echo -e "${GREEN}Unity Hub installed successfully via curl!${NC}"
}

# Install Rust using curl
install_rust_curl() {
    echo -e "${BLUE}Installing Rust via curl...${NC}"
    
    if command_exists rustc; then
        echo -e "${GREEN}Rust is already installed.${NC}"
        return
    fi
    
    curl --proto '=https' --tlsv1.2 -sSf https://sh.rustup.rs | sh -s -- -y
    source ~/.cargo/env
    
    echo -e "${GREEN}Rust installed successfully via curl!${NC}"
}

# Install Python using curl (via pyenv)
install_python_curl() {
    echo -e "${BLUE}Installing Python via curl (pyenv)...${NC}"
    
    if command_exists pyenv; then
        echo -e "${GREEN}pyenv is already installed.${NC}"
        return
    fi
    
    # Install pyenv via curl
    curl https://pyenv.run | bash
    
    # Add to shell profile
    echo 'export PYENV_ROOT="$HOME/.pyenv"' >> ~/.zshrc
    echo 'command -v pyenv >/dev/null || export PATH="$PYENV_ROOT/bin:$PATH"' >> ~/.zshrc
    echo 'eval "$(pyenv init -)"' >> ~/.zshrc
    
    # Source the profile
    export PYENV_ROOT="$HOME/.pyenv"
    export PATH="$PYENV_ROOT/bin:$PATH"
    eval "$(pyenv init -)"
    
    # Install latest Python
    pyenv install 3.11.5
    pyenv global 3.11.5
    
    echo -e "${GREEN}Python installed successfully via curl!${NC}"
}

# Install Docker using curl
install_docker_curl() {
    echo -e "${BLUE}Installing Docker via curl...${NC}"
    
    if command_exists docker; then
        echo -e "${GREEN}Docker is already installed.${NC}"
        return
    fi
    
    # Download Docker Desktop for Mac
    if [[ $(uname -m) == "arm64" ]]; then
        DOCKER_URL="https://desktop.docker.com/mac/main/arm64/Docker.dmg"
    else
        DOCKER_URL="https://desktop.docker.com/mac/main/amd64/Docker.dmg"
    fi
    
    TEMP_DMG="/tmp/Docker.dmg"
    
    echo -e "${BLUE}Downloading Docker Desktop via curl...${NC}"
    curl -L -o "$TEMP_DMG" "$DOCKER_URL"
    
    # Mount and install
    echo -e "${BLUE}Installing Docker Desktop...${NC}"
    hdiutil attach "$TEMP_DMG" -quiet
    cp -R "/Volumes/Docker/Docker.app" "/Applications/"
    hdiutil detach "/Volumes/Docker" -quiet
    rm "$TEMP_DMG"
    
    echo -e "${GREEN}Docker Desktop installed successfully via curl!${NC}"
    echo -e "${YELLOW}Please start Docker Desktop manually from Applications.${NC}"
}

# Install VS Code using curl
install_vscode_curl() {
    echo -e "${BLUE}Installing VS Code via curl...${NC}"
    
    if [ -d "/Applications/Visual Studio Code.app" ]; then
        echo -e "${GREEN}VS Code is already installed.${NC}"
        return
    fi
    
    # Download VS Code
    if [[ $(uname -m) == "arm64" ]]; then
        VSCODE_URL="https://code.visualstudio.com/sha/download?build=stable&os=darwin-arm64"
    else
        VSCODE_URL="https://code.visualstudio.com/sha/download?build=stable&os=darwin"
    fi
    
    TEMP_ZIP="/tmp/VSCode.zip"
    
    echo -e "${BLUE}Downloading VS Code via curl...${NC}"
    curl -L -o "$TEMP_ZIP" "$VSCODE_URL"
    
    # Extract and install
    echo -e "${BLUE}Installing VS Code...${NC}"
    unzip -q "$TEMP_ZIP" -d "/Applications/"
    rm "$TEMP_ZIP"
    
    echo -e "${GREEN}VS Code installed successfully via curl!${NC}"
}

# Install Oh My Zsh using curl
install_ohmyzsh_curl() {
    echo -e "${BLUE}Installing Oh My Zsh via curl...${NC}"
    
    if [ -d "$HOME/.oh-my-zsh" ]; then
        echo -e "${GREEN}Oh My Zsh is already installed.${NC}"
        return
    fi
    
    sh -c "$(curl -fsSL https://raw.github.com/ohmyzsh/ohmyzsh/master/tools/install.sh)" "" --unattended
    
    echo -e "${GREEN}Oh My Zsh installed successfully via curl!${NC}"
}

# Main installation function
main() {
    echo -e "${GREEN}Starting curl-based environment setup...${NC}"
    echo -e "${YELLOW}This will install development tools using curl where possible.${NC}"
    
    read -p $'\nDo you want to continue? (y/n): ' -n 1 -r
    echo
    if [[ ! $REPLY =~ ^[Yy]$ ]]; then
        echo -e "${YELLOW}Installation cancelled.${NC}"
        exit 0
    fi
    
    # Make script executable
    chmod +x "$0"
    
    # Install components using curl
    install_homebrew_curl
    install_nodejs_curl
    install_unity_hub_curl
    install_rust_curl
    install_python_curl
    install_docker_curl
    install_vscode_curl
    install_ohmyzsh_curl
    
    echo -e "${GREEN}\nCurl-based installation completed!${NC}"
    echo -e "${YELLOW}Please restart your terminal to load all environment variables.${NC}"
    
    # Show what was installed
    echo -e "${BLUE}\nInstalled via curl:${NC}"
    echo -e "${GREEN}✓ Homebrew Package Manager${NC}"
    echo -e "${GREEN}✓ Node.js (via nvm)${NC}"
    echo -e "${GREEN}✓ Unity Hub${NC}"
    echo -e "${GREEN}✓ Rust (via rustup)${NC}"
    echo -e "${GREEN}✓ Python (via pyenv)${NC}"
    echo -e "${GREEN}✓ Docker Desktop${NC}"
    echo -e "${GREEN}✓ Visual Studio Code${NC}"
    echo -e "${GREEN}✓ Oh My Zsh${NC}"
}

# Run main function
main "$@"
