#!/bin/bash

# iOS IPA Creation Script for Unity Project
# This script creates an IPA file from the existing iOS build without requiring code signing

set -e

echo "🍎 Starting iOS IPA creation..."

# Paths
IOS_BUILD_PATH="Builds/ios"
IPA_OUTPUT_PATH="Builds/ios-ipa"
APP_NAME="香域"
XCODE_PROJECT_PATH="$IOS_BUILD_PATH/Unity-iPhone.xcodeproj"

# Check if iOS build exists
if [ ! -d "$IOS_BUILD_PATH" ] || [ ! -d "$XCODE_PROJECT_PATH" ]; then
    echo "❌ Error: iOS Xcode project not found at $XCODE_PROJECT_PATH"
    echo "Please build iOS project first using Unity."
    exit 1
fi

echo "✅ Found iOS project at: $XCODE_PROJECT_PATH"

# Create output directory
mkdir -p "$IPA_OUTPUT_PATH"
echo "📁 Created output directory: $IPA_OUTPUT_PATH"

# Since iOS builds require code signing, we'll create a manual IPA from existing build files
echo "📦 Creating app bundle from existing iOS build files..."

    
    APP_BUNDLE_PATH="$IPA_OUTPUT_PATH/${APP_NAME}.app"
    mkdir -p "$APP_BUNDLE_PATH"
    
    # Copy essential files
    if [ -f "$IOS_BUILD_PATH/Info.plist" ]; then
        cp "$IOS_BUILD_PATH/Info.plist" "$APP_BUNDLE_PATH/"
        echo "✅ Copied Info.plist"
    fi
    
    # Copy Data directory
    if [ -d "$IOS_BUILD_PATH/Data" ]; then
        cp -R "$IOS_BUILD_PATH/Data" "$APP_BUNDLE_PATH/"
        echo "✅ Copied Data directory"
    fi
    
    # Copy Libraries
    if [ -d "$IOS_BUILD_PATH/Libraries" ]; then
        cp -R "$IOS_BUILD_PATH/Libraries" "$APP_BUNDLE_PATH/"
        echo "✅ Copied Libraries"
    fi
    
    # Copy launch screens
    for file in "$IOS_BUILD_PATH"/LaunchScreen*; do
        if [ -f "$file" ]; then
            cp "$file" "$APP_BUNDLE_PATH/"
        fi
    done
    echo "✅ Copied launch screens"
    
    # Create Payload directory and move app
    PAYLOAD_DIR="$IPA_OUTPUT_PATH/Payload"
    mkdir -p "$PAYLOAD_DIR"
    mv "$APP_BUNDLE_PATH" "$PAYLOAD_DIR/"
    
    # Create IPA
    cd "$IPA_OUTPUT_PATH"
    zip -r "${APP_NAME}-manual.ipa" Payload/
    
    # Clean up
    rm -rf Payload/
    
    # Get file size
    IPA_SIZE=$(ls -lh "${APP_NAME}-manual.ipa" | awk '{print $5}')
    
    echo "🎉 Manual iOS IPA created successfully!"
    echo "📍 Location: $IPA_OUTPUT_PATH/${APP_NAME}-manual.ipa"
    echo "📏 Size: $IPA_SIZE"
    echo ""
    echo "⚠️  Note: This is a manually created IPA without proper code signing."
    echo "   It may not install on devices without jailbreaking or proper certificates."
fi

echo ""
echo "✨ iOS IPA creation process completed!"
