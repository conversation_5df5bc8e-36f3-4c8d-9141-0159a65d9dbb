Unity Editor version:    2022.3.62f1 (4af31df58517)
Branch:                  2022.3/staging
Build type:              Release
Batch mode:              YES
macOS version:           Version 15.5 (Build 24F74)
Darwin version:          24.5.0
Architecture:            arm64
Running under Rosetta:   NO
Available memory:        16384 MB
[Licensing::Module] Trying to connect to existing licensing client channel...
[Licensing::IpcConnector] Successfully connected to: "LicenseClient-cafe" at "2025-06-25T14:30:14.852712Z"
[Licensing::Client] Code 10 while verifying Licensing Client signature (process Id: 47956, path: "/Applications/Unity Hub.app/Contents/Frameworks/UnityLicensingClient_V1.app/Contents/MacOS/Unity.Licensing.Client")
[Licensing::Module] LicensingClient has failed validation; ignoring
[Licensing::Client] Handshaking with LicensingClient:
  Version:                 1.17.0+aa6cfba
  Session Id:              d2ff98b9ae55415baaf637761b6c0158
  Correlation Id:          add1fd4bf48fcb70563aa0051c34e7a0
  External correlation Id: 2663570535011267150
  Machine Id:              SaO/+vEl19cOZCJ9iXNm5zgvkFY=
[Licensing::Module] Successfully connected to LicensingClient on channel: "LicenseClient-cafe" (connect: 0.00s, validation: 0.01s, handshake: 0.28s)
[Licensing::IpcConnector] Successfully connected to: "LicenseClient-cafe-notifications" at "2025-06-25T14:30:15.143969Z"
[Licensing::Module] Error: Access token is unavailable; failed to update
[Licensing::Client] Successfully updated license
[Licensing::Client] Successfully resolved entitlement details
[Licensing::Module] Serial number assigned to: "F4-2R2W-WK36-W2FA-CS3P-XXXX"
[Licensing::Module] Serial number assigned to: "1375833638986-UnityPersXXXX"
Pro License: NO
[Licensing::Client] Successfully resolved entitlement details
[Licensing::Module] Serial number assigned to: "F4-2R2W-WK36-W2FA-CS3P-XXXX"
[Licensing::Module] Serial number assigned to: "1375833638986-UnityPersXXXX"
Launching external process: /Applications/Unity/Hub/Editor/2022.3.62f1/Unity.app/Contents/Resources/PackageManager/Server/UnityPackageManager

COMMAND LINE ARGUMENTS:
/Applications/Unity/Hub/Editor/2022.3.62f1/Unity.app/Contents/MacOS/Unity
-batchmode
-quit
-projectPath
/Users/<USER>/Documents/GitHub/PerfumerProject
-logFile
/Users/<USER>/Documents/GitHub/PerfumerProject/compile-test.log
Successfully changed project path to: /Users/<USER>/Documents/GitHub/PerfumerProject
/Users/<USER>/Documents/GitHub/PerfumerProject
[UnityMemory] Configuration Parameters - Can be set up in boot.config
    "memorysetup-bucket-allocator-granularity=16"
    "memorysetup-bucket-allocator-bucket-count=8"
    "memorysetup-bucket-allocator-block-size=33554432"
    "memorysetup-bucket-allocator-block-count=8"
    "memorysetup-main-allocator-block-size=16777216"
    "memorysetup-thread-allocator-block-size=16777216"
    "memorysetup-gfx-main-allocator-block-size=16777216"
    "memorysetup-gfx-thread-allocator-block-size=16777216"
    "memorysetup-cache-allocator-block-size=4194304"
    "memorysetup-typetree-allocator-block-size=2097152"
    "memorysetup-profiler-bucket-allocator-granularity=16"
    "memorysetup-profiler-bucket-allocator-bucket-count=8"
    "memorysetup-profiler-bucket-allocator-block-size=33554432"
    "memorysetup-profiler-bucket-allocator-block-count=8"
    "memorysetup-profiler-allocator-block-size=16777216"
    "memorysetup-profiler-editor-allocator-block-size=1048576"
    "memorysetup-temp-allocator-size-main=16777216"
    "memorysetup-job-temp-allocator-block-size=2097152"
    "memorysetup-job-temp-allocator-block-size-background=1048576"
    "memorysetup-job-temp-allocator-reduction-small-platforms=262144"
    "memorysetup-allocator-temp-initial-block-size-main=262144"
    "memorysetup-allocator-temp-initial-block-size-worker=262144"
    "memorysetup-temp-allocator-size-background-worker=32768"
    "memorysetup-temp-allocator-size-job-worker=262144"
    "memorysetup-temp-allocator-size-preload-manager=33554432"
    "memorysetup-temp-allocator-size-nav-mesh-worker=65536"
    "memorysetup-temp-allocator-size-audio-worker=65536"
    "memorysetup-temp-allocator-size-cloud-worker=32768"
    "memorysetup-temp-allocator-size-gi-baking-worker=262144"
    "memorysetup-temp-allocator-size-gfx=262144"
Player connection [8499977984]  Target information:

Player connection [8499977984]  * "[IP] ************** [Port] 55504 [Flags] 2 [Guid] 2337829120 [EditorId] 2337829120 [Version] 1048832 [Id] OSXEditor(0,MacBookPro) [Debug] 1 [PackageName] OSXEditor [ProjectName] Editor" 

Player connection [8499977984] Host joined multi-casting on [***********:54997]...
Player connection [8499977984] Host joined alternative multi-casting on [***********:34997]...
Input System module state changed to: Initialized.
[PhysX] Initialized MultithreadedTaskDispatcher with 8 workers.
[Package Manager] UpmClient::Connect -- Connected to IPC stream "Upm-70827" after 0.7 seconds.
[Package Manager] Restoring resolved packages state from cache
[Licensing::Client] Successfully resolved entitlement details
[Package Manager] Registered 53 packages:
  Packages from [https://packages.unity.com]:
    com.unity.nuget.newtonsoft-json@3.2.1 (location: /Users/<USER>/Documents/GitHub/PerfumerProject/Library/PackageCache/com.unity.nuget.newtonsoft-json@3.2.1)
    com.unity.collab-proxy@2.7.1 (location: /Users/<USER>/Documents/GitHub/PerfumerProject/Library/PackageCache/com.unity.collab-proxy@2.7.1)
    com.unity.inputsystem@1.14.0 (location: /Users/<USER>/Documents/GitHub/PerfumerProject/Library/PackageCache/com.unity.inputsystem@1.14.0)
    com.unity.textmeshpro@3.0.7 (location: /Users/<USER>/Documents/GitHub/PerfumerProject/Library/PackageCache/com.unity.textmeshpro@3.0.7)
    com.unity.timeline@1.7.7 (location: /Users/<USER>/Documents/GitHub/PerfumerProject/Library/PackageCache/com.unity.timeline@1.7.7)
    com.unity.toolchain.macos-arm64-linux-x86_64@2.0.4 (location: /Users/<USER>/Documents/GitHub/PerfumerProject/Library/PackageCache/com.unity.toolchain.macos-arm64-linux-x86_64@2.0.4)
    com.unity.visualscripting@1.9.4 (location: /Users/<USER>/Documents/GitHub/PerfumerProject/Library/PackageCache/com.unity.visualscripting@1.9.4)
    com.unity.sysroot@2.0.10 (location: /Users/<USER>/Documents/GitHub/PerfumerProject/Library/PackageCache/com.unity.sysroot@2.0.10)
    com.unity.sysroot.linux-x86_64@2.0.9 (location: /Users/<USER>/Documents/GitHub/PerfumerProject/Library/PackageCache/com.unity.sysroot.linux-x86_64@2.0.9)
    com.unity.ide.visualstudio@2.0.22 (location: /Users/<USER>/Documents/GitHub/PerfumerProject/Library/PackageCache/com.unity.ide.visualstudio@2.0.22)
    com.unity.ide.rider@3.0.36 (location: /Users/<USER>/Documents/GitHub/PerfumerProject/Library/PackageCache/com.unity.ide.rider@3.0.36)
    com.unity.ide.vscode@1.2.5 (location: /Users/<USER>/Documents/GitHub/PerfumerProject/Library/PackageCache/com.unity.ide.vscode@1.2.5)
    com.unity.editorcoroutines@1.0.0 (location: /Users/<USER>/Documents/GitHub/PerfumerProject/Library/PackageCache/com.unity.editorcoroutines@1.0.0)
    com.unity.performance.profile-analyzer@1.2.3 (location: /Users/<USER>/Documents/GitHub/PerfumerProject/Library/PackageCache/com.unity.performance.profile-analyzer@1.2.3)
    com.unity.test-framework@1.1.33 (location: /Users/<USER>/Documents/GitHub/PerfumerProject/Library/PackageCache/com.unity.test-framework@1.1.33)
    com.unity.testtools.codecoverage@1.2.6 (location: /Users/<USER>/Documents/GitHub/PerfumerProject/Library/PackageCache/com.unity.testtools.codecoverage@1.2.6)
    com.unity.settings-manager@2.1.0 (location: /Users/<USER>/Documents/GitHub/PerfumerProject/Library/PackageCache/com.unity.settings-manager@2.1.0)
    com.unity.ext.nunit@1.0.6 (location: /Users/<USER>/Documents/GitHub/PerfumerProject/Library/PackageCache/com.unity.ext.nunit@1.0.6)
  Built-in packages:
    com.unity.2d.sprite@1.0.0 (location: /Users/<USER>/Documents/GitHub/PerfumerProject/Library/PackageCache/com.unity.2d.sprite@1.0.0)
    com.unity.feature.development@1.0.1 (location: /Users/<USER>/Documents/GitHub/PerfumerProject/Library/PackageCache/com.unity.feature.development@1.0.1)
    com.unity.ugui@1.0.0 (location: /Users/<USER>/Documents/GitHub/PerfumerProject/Library/PackageCache/com.unity.ugui@1.0.0)
    com.unity.modules.ai@1.0.0 (location: /Users/<USER>/Documents/GitHub/PerfumerProject/Library/PackageCache/com.unity.modules.ai@1.0.0)
    com.unity.modules.androidjni@1.0.0 (location: /Users/<USER>/Documents/GitHub/PerfumerProject/Library/PackageCache/com.unity.modules.androidjni@1.0.0)
    com.unity.modules.animation@1.0.0 (location: /Users/<USER>/Documents/GitHub/PerfumerProject/Library/PackageCache/com.unity.modules.animation@1.0.0)
    com.unity.modules.assetbundle@1.0.0 (location: /Users/<USER>/Documents/GitHub/PerfumerProject/Library/PackageCache/com.unity.modules.assetbundle@1.0.0)
    com.unity.modules.audio@1.0.0 (location: /Users/<USER>/Documents/GitHub/PerfumerProject/Library/PackageCache/com.unity.modules.audio@1.0.0)
    com.unity.modules.cloth@1.0.0 (location: /Users/<USER>/Documents/GitHub/PerfumerProject/Library/PackageCache/com.unity.modules.cloth@1.0.0)
    com.unity.modules.director@1.0.0 (location: /Users/<USER>/Documents/GitHub/PerfumerProject/Library/PackageCache/com.unity.modules.director@1.0.0)
    com.unity.modules.imageconversion@1.0.0 (location: /Users/<USER>/Documents/GitHub/PerfumerProject/Library/PackageCache/com.unity.modules.imageconversion@1.0.0)
    com.unity.modules.imgui@1.0.0 (location: /Users/<USER>/Documents/GitHub/PerfumerProject/Library/PackageCache/com.unity.modules.imgui@1.0.0)
    com.unity.modules.jsonserialize@1.0.0 (location: /Users/<USER>/Documents/GitHub/PerfumerProject/Library/PackageCache/com.unity.modules.jsonserialize@1.0.0)
    com.unity.modules.particlesystem@1.0.0 (location: /Users/<USER>/Documents/GitHub/PerfumerProject/Library/PackageCache/com.unity.modules.particlesystem@1.0.0)
    com.unity.modules.physics@1.0.0 (location: /Users/<USER>/Documents/GitHub/PerfumerProject/Library/PackageCache/com.unity.modules.physics@1.0.0)
    com.unity.modules.physics2d@1.0.0 (location: /Users/<USER>/Documents/GitHub/PerfumerProject/Library/PackageCache/com.unity.modules.physics2d@1.0.0)
    com.unity.modules.screencapture@1.0.0 (location: /Users/<USER>/Documents/GitHub/PerfumerProject/Library/PackageCache/com.unity.modules.screencapture@1.0.0)
    com.unity.modules.terrain@1.0.0 (location: /Users/<USER>/Documents/GitHub/PerfumerProject/Library/PackageCache/com.unity.modules.terrain@1.0.0)
    com.unity.modules.terrainphysics@1.0.0 (location: /Users/<USER>/Documents/GitHub/PerfumerProject/Library/PackageCache/com.unity.modules.terrainphysics@1.0.0)
    com.unity.modules.tilemap@1.0.0 (location: /Users/<USER>/Documents/GitHub/PerfumerProject/Library/PackageCache/com.unity.modules.tilemap@1.0.0)
    com.unity.modules.ui@1.0.0 (location: /Users/<USER>/Documents/GitHub/PerfumerProject/Library/PackageCache/com.unity.modules.ui@1.0.0)
    com.unity.modules.uielements@1.0.0 (location: /Users/<USER>/Documents/GitHub/PerfumerProject/Library/PackageCache/com.unity.modules.uielements@1.0.0)
    com.unity.modules.umbra@1.0.0 (location: /Users/<USER>/Documents/GitHub/PerfumerProject/Library/PackageCache/com.unity.modules.umbra@1.0.0)
    com.unity.modules.unityanalytics@1.0.0 (location: /Users/<USER>/Documents/GitHub/PerfumerProject/Library/PackageCache/com.unity.modules.unityanalytics@1.0.0)
    com.unity.modules.unitywebrequest@1.0.0 (location: /Users/<USER>/Documents/GitHub/PerfumerProject/Library/PackageCache/com.unity.modules.unitywebrequest@1.0.0)
    com.unity.modules.unitywebrequestassetbundle@1.0.0 (location: /Users/<USER>/Documents/GitHub/PerfumerProject/Library/PackageCache/com.unity.modules.unitywebrequestassetbundle@1.0.0)
    com.unity.modules.unitywebrequestaudio@1.0.0 (location: /Users/<USER>/Documents/GitHub/PerfumerProject/Library/PackageCache/com.unity.modules.unitywebrequestaudio@1.0.0)
    com.unity.modules.unitywebrequesttexture@1.0.0 (location: /Users/<USER>/Documents/GitHub/PerfumerProject/Library/PackageCache/com.unity.modules.unitywebrequesttexture@1.0.0)
    com.unity.modules.unitywebrequestwww@1.0.0 (location: /Users/<USER>/Documents/GitHub/PerfumerProject/Library/PackageCache/com.unity.modules.unitywebrequestwww@1.0.0)
    com.unity.modules.vehicles@1.0.0 (location: /Users/<USER>/Documents/GitHub/PerfumerProject/Library/PackageCache/com.unity.modules.vehicles@1.0.0)
    com.unity.modules.video@1.0.0 (location: /Users/<USER>/Documents/GitHub/PerfumerProject/Library/PackageCache/com.unity.modules.video@1.0.0)
    com.unity.modules.vr@1.0.0 (location: /Users/<USER>/Documents/GitHub/PerfumerProject/Library/PackageCache/com.unity.modules.vr@1.0.0)
    com.unity.modules.wind@1.0.0 (location: /Users/<USER>/Documents/GitHub/PerfumerProject/Library/PackageCache/com.unity.modules.wind@1.0.0)
    com.unity.modules.xr@1.0.0 (location: /Users/<USER>/Documents/GitHub/PerfumerProject/Library/PackageCache/com.unity.modules.xr@1.0.0)
    com.unity.modules.subsystems@1.0.0 (location: /Users/<USER>/Documents/GitHub/PerfumerProject/Library/PackageCache/com.unity.modules.subsystems@1.0.0)
[Subsystems] No new subsystems found in resolved package list.
Package Manager log level set to [2]
[Package Manager] Done registering packages in 0.13 seconds
Artifact Garbage Collection - Removed 2 unused hashed content files
[Licensing::Client] Successfully resolved entitlement details
[Licensing::Module] Serial number assigned to: "F4-2R2W-WK36-W2FA-CS3P-XXXX"
[Licensing::Module] Serial number assigned to: "1375833638986-UnityPersXXXX"
Refreshing native plugins compatible for Editor in 23.43 ms, found 2 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Initialize engine version: 2022.3.62f1 (4af31df58517)
[Subsystems] Discovering subsystems at path /Applications/Unity/Hub/Editor/2022.3.62f1/Unity.app/Contents/Resources/UnitySubsystems
[Subsystems] Discovering subsystems at path /Users/<USER>/Documents/GitHub/PerfumerProject/Assets
GfxDevice: creating device client; threaded=0; jobified=0
 preferred device: Apple M1 (high power)
Metal devices available: 1
0: Apple M1 (high power)
Using device Apple M1 (high power)
Initializing Metal device caps: Apple M1
[Licensing::Client] Successfully resolved entitlement details
[Licensing::Module] Serial number assigned to: "F4-2R2W-WK36-W2FA-CS3P-XXXX"
[Licensing::Module] Serial number assigned to: "1375833638986-UnityPersXXXX"
Initialize mono
Mono path[0] = '/Applications/Unity/Hub/Editor/2022.3.62f1/Unity.app/Contents/Managed'
Mono path[1] = '/Applications/Unity/Hub/Editor/2022.3.62f1/Unity.app/Contents/MonoBleedingEdge/lib/mono/unityjit-macos'
Mono config path = '/Applications/Unity/Hub/Editor/2022.3.62f1/Unity.app/Contents/MonoBleedingEdge/etc'
Using monoOptions --debugger-agent=transport=dt_socket,embedding=1,server=y,suspend=n,address=127.0.0.1:56827
Using cacheserver namespaces - metadata:defaultmetadata, artifacts:defaultartifacts
Using cacheserver namespaces - metadata:defaultmetadata, artifacts:defaultartifacts
ImportWorker Server TCP listen port: 0
Begin MonoManager ReloadAssembly
Registering precompiled unity dll's ...
Register platform support module: /Applications/Unity/Hub/Editor/2022.3.62f1/PlaybackEngines/WebGLSupport/UnityEditor.WebGL.Extensions.dll
Register platform support module: /Applications/Unity/Hub/Editor/2022.3.62f1/Unity.app/Contents/PlaybackEngines/MacStandaloneSupport/UnityEditor.OSXStandalone.Extensions.dll
Register platform support module: /Applications/Unity/Hub/Editor/2022.3.62f1/PlaybackEngines/AndroidPlayer/UnityEditor.Android.Extensions.dll
[Licensing::Client] Successfully resolved entitlement details
[Licensing::Module] Serial number assigned to: "F4-2R2W-WK36-W2FA-CS3P-XXXX"
[Licensing::Module] Serial number assigned to: "1375833638986-UnityPersXXXX"
Register platform support module: /Applications/Unity/Hub/Editor/2022.3.62f1/PlaybackEngines/iOSSupport/UnityEditor.iOS.Extensions.dll
Register platform support module: /Applications/Unity/Hub/Editor/2022.3.62f1/PlaybackEngines/VisionOSPlayer/UnityEditor.VisionOS.Extensions.dll
Register platform support module: /Applications/Unity/Hub/Editor/2022.3.62f1/PlaybackEngines/AppleTVSupport/UnityEditor.AppleTV.Extensions.dll
Register platform support module: /Applications/Unity/Hub/Editor/2022.3.62f1/PlaybackEngines/WindowsStandaloneSupport/UnityEditor.WindowsStandalone.Extensions.dll
Register platform support module: /Applications/Unity/Hub/Editor/2022.3.62f1/PlaybackEngines/LinuxStandaloneSupport/UnityEditor.LinuxStandalone.Extensions.dll
Registered in 0.021712 seconds.
- Loaded All Assemblies, in  0.313 seconds
Native extension for LinuxStandalone target not found
Native extension for WindowsStandalone target not found
[usbmuxd] Start listen thread
[usbmuxd] Listen thread started
[usbmuxd] Send listen message
Native extension for AppleTV target not found
objc[70827]: Class XcodeScriptingDelegate is implemented in both /Applications/Unity/Hub/Editor/2022.3.62f1/PlaybackEngines/AppleTVSupport/arm64/UnityEditor.iOS.Native.dylib (0x30dba45c8) and /Applications/Unity/Hub/Editor/2022.3.62f1/PlaybackEngines/VisionOSPlayer/arm64/UnityEditor.VisionOS.Native.dylib (0x30e9185c8). This may cause spurious casting failures and mysterious crashes. One of the duplicates must be removed or renamed.
[usbmuxd] Start listen thread
[usbmuxd] Listen thread started
[usbmuxd] Send listen message
Native extension for VisionOS target not found
Native extension for iOS target not found
Native extension for Android target not found
Android Extension - Scanning For ADB Devices 99 ms
Native extension for OSXStandalone target not found
Native extension for WebGL target not found
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  0.371 seconds
Domain Reload Profiling: 684ms
	BeginReloadAssembly (85ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (0ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (1ms)
	RebuildCommonClasses (33ms)
	RebuildNativeTypeToScriptingClass (8ms)
	initialDomainReloadingComplete (55ms)
	LoadAllAssembliesAndSetupDomain (131ms)
		LoadAssemblies (88ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (126ms)
			TypeCache.Refresh (125ms)
				TypeCache.ScanAssembly (113ms)
			ScanForSourceGeneratedMonoScriptInfo (0ms)
			ResolveRequiredComponents (1ms)
	FinalizeReload (371ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (335ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (234ms)
			SetLoadedEditorAssemblies (3ms)
			RefreshPlugins (0ms)
			BeforeProcessingInitializeOnLoad (1ms)
			ProcessInitializeOnLoadAttributes (70ms)
			ProcessInitializeOnLoadMethodAttributes (26ms)
			AfterProcessingInitializeOnLoad (0ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (0ms)
[Licensing::Client] Successfully resolved entitlement details
[Licensing::Module] Serial number assigned to: "F4-2R2W-WK36-W2FA-CS3P-XXXX"
[Licensing::Module] Serial number assigned to: "1375833638986-UnityPersXXXX"
[Licensing::Client] Successfully resolved entitlement details
[Licensing::Module] Serial number assigned to: "F4-2R2W-WK36-W2FA-CS3P-XXXX"
[Licensing::Module] Serial number assigned to: "1375833638986-UnityPersXXXX"
Application.AssetDatabase Initial Refresh Start
The GUID inside 'Assets/FlexReader/Manual.pdf.meta' cannot be extracted by the YAML Parser. Attempting to extract it via string matching instead. Please verify the file does not contain unexpected data.
The GUID inside 'Assets/Resources/Prefabs/UI/Item_Sell.prefab.meta' cannot be extracted by the YAML Parser. Attempting to extract it via string matching instead. Please verify the file does not contain unexpected data.
The GUID inside 'Assets/Scenes/Sell.unity.meta' cannot be extracted by the YAML Parser. Attempting to extract it via string matching instead. Please verify the file does not contain unexpected data.
The GUID inside 'Assets/StreamingAssets/PerfumerData.xlsx.meta' cannot be extracted by the YAML Parser. Attempting to extract it via string matching instead. Please verify the file does not contain unexpected data.
[40m[32minfo[39m[22m[49m: Microsoft.Hosting.Lifetime[14]
      Now listening on: http://unix:/tmp/ilpp.sock-eae4890779ba5942efbfb0ce14813848
[40m[32minfo[39m[22m[49m: Microsoft.Hosting.Lifetime[0]
      Application started. Press Ctrl+C to shut down.
[40m[32minfo[39m[22m[49m: Microsoft.Hosting.Lifetime[0]
      Hosting environment: Production
[40m[32minfo[39m[22m[49m: Microsoft.Hosting.Lifetime[0]
      Content root path: /Users/<USER>/Documents/GitHub/PerfumerProject/
[40m[32minfo[39m[22m[49m: Microsoft.AspNetCore.Hosting.Diagnostics[1]
      Request starting HTTP/2 POST http://ilpp/UnityILPP.PostProcessing/Ping application/grpc -
[40m[32minfo[39m[22m[49m: Microsoft.AspNetCore.Routing.EndpointMiddleware[0]
      Executing endpoint 'gRPC - /UnityILPP.PostProcessing/Ping'
[40m[32minfo[39m[22m[49m: Microsoft.AspNetCore.Routing.EndpointMiddleware[1]
      Executed endpoint 'gRPC - /UnityILPP.PostProcessing/Ping'
[40m[32minfo[39m[22m[49m: Microsoft.AspNetCore.Hosting.Diagnostics[2]
      Request finished HTTP/2 POST http://ilpp/UnityILPP.PostProcessing/Ping application/grpc - - 200 - application/grpc 39.6102ms
Starting: /Applications/Unity/Hub/Editor/2022.3.62f1/Unity.app/Contents/bee_backend --ipc --defer-dag-verification --dagfile="Library/Bee/1300b0aE.dag" --continue-on-failure --profile="Library/Bee/backend1.traceevents" ScriptAssemblies
WorkingDir: /Users/<USER>/Documents/GitHub/PerfumerProject
DisplayProgressbar: Compiling Scripts
ExitCode: 0 Duration: 0s132ms
*** Tundra build success (0.12 seconds), 0 items updated, 517 evaluated
AssetDatabase: script compilation time: 0.795563s
Total cache size ********
Total cache size after purge ********
Begin MonoManager ReloadAssembly
Symbol file LoadedFromMemory is not a mono symbol file
- Loaded All Assemblies, in  0.524 seconds
The GUID inside 'Assets/FlexReader/Manual.pdf.meta' cannot be extracted by the YAML Parser. Attempting to extract it via string matching instead. Please verify the file does not contain unexpected data.
The GUID inside 'Assets/Resources/Prefabs/UI/Item_Sell.prefab.meta' cannot be extracted by the YAML Parser. Attempting to extract it via string matching instead. Please verify the file does not contain unexpected data.
The GUID inside 'Assets/Scenes/Sell.unity.meta' cannot be extracted by the YAML Parser. Attempting to extract it via string matching instead. Please verify the file does not contain unexpected data.
The GUID inside 'Assets/StreamingAssets/PerfumerData.xlsx.meta' cannot be extracted by the YAML Parser. Attempting to extract it via string matching instead. Please verify the file does not contain unexpected data.
Refreshing native plugins compatible for Editor in 1.27 ms, found 2 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
The GUID inside 'Assets/FlexReader/Manual.pdf.meta' cannot be extracted by the YAML Parser. Attempting to extract it via string matching instead. Please verify the file does not contain unexpected data.
The GUID inside 'Assets/Resources/Prefabs/UI/Item_Sell.prefab.meta' cannot be extracted by the YAML Parser. Attempting to extract it via string matching instead. Please verify the file does not contain unexpected data.
The GUID inside 'Assets/Scenes/Sell.unity.meta' cannot be extracted by the YAML Parser. Attempting to extract it via string matching instead. Please verify the file does not contain unexpected data.
The GUID inside 'Assets/StreamingAssets/PerfumerData.xlsx.meta' cannot be extracted by the YAML Parser. Attempting to extract it via string matching instead. Please verify the file does not contain unexpected data.
Refreshing native plugins compatible for Editor in 1.23 ms, found 2 plugins.
Native extension for LinuxStandalone target not found
Native extension for WindowsStandalone target not found
Native extension for AppleTV target not found
Native extension for VisionOS target not found
Native extension for iOS target not found
Native extension for Android target not found
Native extension for OSXStandalone target not found
Native extension for WebGL target not found
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  0.530 seconds
Domain Reload Profiling: 1054ms
	BeginReloadAssembly (87ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (3ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (24ms)
	RebuildCommonClasses (28ms)
	RebuildNativeTypeToScriptingClass (9ms)
	initialDomainReloadingComplete (17ms)
	LoadAllAssembliesAndSetupDomain (383ms)
		LoadAssemblies (235ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (186ms)
			TypeCache.Refresh (163ms)
				TypeCache.ScanAssembly (146ms)
			ScanForSourceGeneratedMonoScriptInfo (11ms)
			ResolveRequiredComponents (11ms)
	FinalizeReload (530ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (361ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (50ms)
			SetLoadedEditorAssemblies (3ms)
			RefreshPlugins (0ms)
			BeforeProcessingInitializeOnLoad (46ms)
			ProcessInitializeOnLoadAttributes (246ms)
			ProcessInitializeOnLoadMethodAttributes (14ms)
			AfterProcessingInitializeOnLoad (1ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (2ms)
The GUID inside 'Assets/FlexReader/Manual.pdf.meta' cannot be extracted by the YAML Parser. Attempting to extract it via string matching instead. Please verify the file does not contain unexpected data.
The GUID inside 'Assets/Resources/Prefabs/UI/Item_Sell.prefab.meta' cannot be extracted by the YAML Parser. Attempting to extract it via string matching instead. Please verify the file does not contain unexpected data.
The GUID inside 'Assets/Scenes/Sell.unity.meta' cannot be extracted by the YAML Parser. Attempting to extract it via string matching instead. Please verify the file does not contain unexpected data.
The GUID inside 'Assets/StreamingAssets/PerfumerData.xlsx.meta' cannot be extracted by the YAML Parser. Attempting to extract it via string matching instead. Please verify the file does not contain unexpected data.
Asset Pipeline Refresh (id=e64b2a9a50731471f950e64b141f6c2d): Total: 3.586 seconds - Initiated by InitialRefreshV2(ForceSynchronousImport)
	Summary:
		Imports: total=0 (actual=0, local cache=0, cache server=0)
		Asset DB Process Time: managed=0 ms, native=1650 ms
		Asset DB Callback time: managed=9 ms, native=0 ms
		Scripting: domain reloads=1, domain reload time=1095 ms, compile time=797 ms, other=34 ms
		Project Asset Count: scripts=5485, non-scripts=1510
		Asset File Changes: new=0, changed=0, moved=0, deleted=0
		Scan Filter Count: 1
	InvokeBeforeRefreshCallbacks: 0.188ms
	ApplyChangesToAssetFolders: 0.051ms
	Scan: 388.072ms
	OnSourceAssetsModified: 0.001ms
	GetAllGuidsForCategorization: 0.507ms
	CategorizeAssets: 119.224ms
	ImportOutOfDateAssets: 553.310ms (-258.040ms without children)
		CompileScripts: 796.917ms
		ReloadNativeAssets: 0.006ms
		UnloadImportedAssets: 12.031ms
		EnsureUptoDateAssetsAreRegisteredWithGuidPM: 1.651ms
		InitializingProgressBar: 0.000ms
		PostProcessAllAssetNotificationsAddChangedAssets: 0.000ms
		OnDemandSchedulerStart: 0.744ms
	PostProcessAllAssets: 9.100ms
	GatherAllCurrentPrimaryArtifactRevisions: 0.003ms
	UnloadStreamsBegin: 0.368ms
	PersistCurrentRevisions: 0.196ms
	UnloadStreamsEnd: 0.016ms
	GenerateScriptTypeHashes: 2.371ms
	Untracked: 2515.341ms
Application.AssetDatabase Initial Refresh End
Launching external process: /Applications/Unity/Hub/Editor/2022.3.62f1/Unity.app/Contents/Tools/UnityShaderCompiler
Launched and connected shader compiler UnityShaderCompiler after 0.06 seconds
Scanning for USB devices : 0.029ms
Initializing Unity extensions:
[Licensing::Client] Successfully resolved entitlement details
[Licensing::Module] Serial number assigned to: "F4-2R2W-WK36-W2FA-CS3P-XXXX"
[Licensing::Module] Serial number assigned to: "1375833638986-UnityPersXXXX"
[MODES] ModeService[none].Initialize
[MODES] ModeService[none].LoadModes
[MODES] Loading mode Default (0) for mode-current-id-香域
2025-06-25 22:30:27.006 Unity[70827:946610] NSEventModifierFlagFunction specified to -setKeyEquivalentModifierMask: for item <NSMenuItem: 0x32534e620 Font Asset, ke='Command-F12'>, but is only supported for system-provided menu items; will not be used
[PhysX] Initialized MultithreadedTaskDispatcher with 8 workers.
ProgressiveSceneManager::Cancel()
-- Listing OpenCL platforms(s) --
 * OpenCL platform 0
	PROFILE = FULL_PROFILE
	VERSION = OpenCL 1.2 (Apr 18 2025 21:46:03)
	NAME = Apple
	VENDOR = Apple
-- Listing OpenCL device(s) --
 * OpenCL platform 0, device 0 
	DEVICE_TYPE = 4
	DEVICE_NAME = Apple M1
	DEVICE_VENDOR = Apple
	DEVICE_VERSION = OpenCL 1.2 
	DRIVER_VERSION = 1.2 1.0
	DEVICE_MAX_COMPUTE_UNITS = 8
	DEVICE_MAX_CLOCK_FREQUENCY = 1000
	CL_DEVICE_MAX_CONSTANT_BUFFER_SIZE = 1073741824
	CL_DEVICE_HOST_UNIFIED_MEMORY = true
	CL_DEVICE_MAX_MEM_ALLOC_SIZE = 2147483648
	DEVICE_GLOBAL_MEM_SIZE = 11453251584
-- GPU Progressive lightmapper will use OpenCL device 'Apple M1' from 'Apple'--
   use -OpenCL-PlatformAndDeviceIndices <platformIdx> <deviceIdx> as command line arguments if you want to select a specific adapter for OpenCL.
Unloading 5 Unused Serialized files (Serialized files now loaded: 0)
Unloading 69 unused Assets / (473.4 KB). Loaded Objects now: 5068.
Memory consumption went from 135.1 MB to 134.6 MB.
Total: 3.195667 ms (FindLiveObjects: 0.144375 ms CreateObjectMapping: 0.061083 ms MarkObjects: 2.685542 ms  DeleteObjects: 0.304333 ms)

Batchmode quit successfully invoked - shutting down!
Curl error 42: Callback aborted
Killing ADB server in 0.033118 seconds.
Thread 0x32a3d3000 may have been prematurely finalized
[PhysX] Initialized MultithreadedTaskDispatcher with 8 workers.
Input System module state changed to: ShutdownInProgress.
Input System polling thread exited.
Input System module state changed to: Shutdown.
[Licensing::IpcConnector] LicenseClient-cafe-notifications channel disconnected successfully.
[Licensing::IpcConnector] LicenseClient-cafe channel disconnected successfully.
Thread 0x16d46b000 may have been prematurely finalized
[40m[32minfo[39m[22m[49m: Microsoft.Hosting.Lifetime[0]
      Application is shutting down...
Cleanup mono
debugger-agent: Unable to listen on 48
[usbmuxd] Stop listen thread
[usbmuxd] Error: 
[usbmuxd] Listen thread exiting
[usbmuxd] Stop listen thread
[usbmuxd] Error: 
[usbmuxd] Listen thread exiting
[Performance] Application.InitializeProject                                                                                  :        1 samples, Peak.  5.97 s (1.0x), Avg.  5.97 s, Total. 5.969 s (47.4%)
[Performance] Application.PackageManager.StartServer                                                                         :        1 samples, Peak.  2.01 ms (1.0x), Avg.  2.01 ms, Total. 2.010 ms (0.0%)
[Performance] Application.AcquireProjectLock                                                                                 :        1 samples, Peak.  2.39 ms (1.0x), Avg.  2.39 ms, Total. 2.393 ms (0.0%)
[Performance] Application.InitializeEngineNoGraphics                                                                         :        1 samples, Peak.  31.1 ms (1.0x), Avg.  31.1 ms, Total. 31.12 ms (0.2%)
[Performance] Application.PackageManager.Initialize                                                                          :        1 samples, Peak.   690 ms (1.0x), Avg.   690 ms, Total. 690.1 ms (5.5%)
[Performance] Connecting to Package Manager                                                                                  :       70 samples, Peak.  34.5 us (59.0x), Avg.   585 ns, Total. 40.96 us (0.0%)
[Performance] Application.EngineGraphics.Initialize                                                                          :        1 samples, Peak.  42.1 ms (1.0x), Avg.  42.1 ms, Total. 42.08 ms (0.3%)
[Performance] Application.GI.Initialize                                                                                      :        1 samples, Peak.   578 us (1.0x), Avg.   578 us, Total. 577.6 us (0.0%)
[Performance] Application.LoadAllDefaultResourcesFromEditor                                                                  :        1 samples, Peak.  1.11 ms (1.0x), Avg.  1.11 ms, Total. 1.111 ms (0.0%)
[Performance] Application.LoadMonoAssemblies                                                                                 :        1 samples, Peak.   692 ms (1.0x), Avg.   692 ms, Total. 691.8 ms (5.5%)
[Performance] RestoreManagedReferences                                                                                       :        2 samples, Peak.  40.8 ms (2.0x), Avg.  20.4 ms, Total. 40.77 ms (0.3%)
[Performance] InitializeOnLoad DrivenRectTransformUndo                                                                       :        2 samples, Peak.   319 us (1.1x), Avg.   292 us, Total. 584.4 us (0.0%)
[Performance] InitializeOnLoad SceneSearch                                                                                   :        2 samples, Peak.  87.5 us (1.1x), Avg.  80.8 us, Total. 161.7 us (0.0%)
[Performance] InitializeOnLoad CloudBuild                                                                                    :        2 samples, Peak.  3.67 us (1.2x), Avg.  3.04 us, Total. 6.084 us (0.0%)
[Performance] InitializeOnLoad NativeFormatImporterUtility                                                                   :        2 samples, Peak.  1.64 ms (1.2x), Avg.  1.36 ms, Total. 2.719 ms (0.0%)
[Performance] InitializeOnLoad SearchService                                                                                 :        2 samples, Peak.  86.8 us (1.0x), Avg.  85.8 us, Total. 171.5 us (0.0%)
[Performance] InitializeOnLoad SettingsService                                                                               :        2 samples, Peak.  8.17 us (1.1x), Avg.  7.23 us, Total. 14.46 us (0.0%)
[Performance] InitializeOnLoad WindowLayout                                                                                  :        2 samples, Peak.   155 us (1.0x), Avg.   151 us, Total. 302.0 us (0.0%)
[Performance] InitializeOnLoad AssetStoreContext                                                                             :        2 samples, Peak.  13.4 ms (1.7x), Avg.  7.72 ms, Total. 15.44 ms (0.1%)
[Performance] InitializeOnLoad PoolManager                                                                                   :        2 samples, Peak.   382 us (1.0x), Avg.   380 us, Total. 759.2 us (0.0%)
[Performance] InitializeOnLoad MixerEffectDefinitionReloader                                                                 :        2 samples, Peak.  1.92 ms (1.1x), Avg.  1.82 ms, Total. 3.640 ms (0.0%)
[Performance] InitializeOnLoad ProjectSearch                                                                                 :        2 samples, Peak.  70.3 us (1.4x), Avg.  51.7 us, Total. 103.5 us (0.0%)
[Performance] InitializeOnLoad UISystemProfilerRenderService                                                                 :        2 samples, Peak.  9.25 us (1.1x), Avg.  8.42 us, Total. 16.83 us (0.0%)
[Performance] InitializeOnLoad EditMode                                                                                      :        2 samples, Peak.   337 us (1.0x), Avg.   328 us, Total. 656.3 us (0.0%)
[Performance] InitializeOnLoad UnityConnect                                                                                  :        2 samples, Peak.  36.4 us (1.0x), Avg.  34.9 us, Total. 69.79 us (0.0%)
[Performance] InitializeOnLoad ManagedDebugger                                                                               :        2 samples, Peak.   399 us (1.0x), Avg.   394 us, Total. 787.6 us (0.0%)
[Performance] InitializeOnLoad ShortcutIntegration                                                                           :        2 samples, Peak.  39.2 us (1.0x), Avg.  39.1 us, Total. 78.21 us (0.0%)
[Performance] InitializeOnLoad AddComponentWindow                                                                            :        2 samples, Peak.  83.9 us (1.0x), Avg.  80.0 us, Total. 160.0 us (0.0%)
[Performance] InitializeOnLoad ManagedDebuggerWindow                                                                         :        2 samples, Peak.  82.2 us (1.0x), Avg.  80.3 us, Total. 160.6 us (0.0%)
[Performance] InitializeOnLoad CacheServerWindow                                                                             :        2 samples, Peak.  8.79 us (1.2x), Avg.  7.50 us, Total. 15.00 us (0.0%)
[Performance] InitializeOnLoad PlayModeDownload                                                                              :        2 samples, Peak.  2.13 ms (1.9x), Avg.  1.10 ms, Total. 2.201 ms (0.0%)
[Performance] InitializeOnLoad MenuItems                                                                                     :        2 samples, Peak.   219 us (1.6x), Avg.   140 us, Total. 279.7 us (0.0%)
[Performance] InitializeOnLoad PrefabInstanceChangedListener                                                                 :        2 samples, Peak.   104 us (1.1x), Avg.  99.1 us, Total. 198.2 us (0.0%)
[Performance] InitializeOnLoad ObjectSelectorSearch                                                                          :        2 samples, Peak.  27.2 us (1.1x), Avg.  25.3 us, Total. 50.58 us (0.0%)
[Performance] InitializeOnLoad EditorMonitor                                                                                 :        2 samples, Peak.   532 us (1.3x), Avg.   425 us, Total. 849.5 us (0.0%)
[Performance] InitializeOnLoad RetainedMode                                                                                  :        2 samples, Peak.  1.59 ms (1.0x), Avg.  1.56 ms, Total. 3.110 ms (0.0%)
[Performance] InitializeOnLoad EditorShaderLoader                                                                            :        2 samples, Peak.   449 us (1.1x), Avg.   417 us, Total. 833.9 us (0.0%)
[Performance] InitializeOnLoad EditorDelegateRegistration                                                                    :        2 samples, Peak.   401 us (1.1x), Avg.   358 us, Total. 716.9 us (0.0%)
[Performance] InitializeOnLoad UIDocumentHierarchyWatcher                                                                    :        2 samples, Peak.  47.3 us (1.0x), Avg.  45.8 us, Total. 91.50 us (0.0%)
[Performance] InitializeOnLoad LiveReloadTrackerCreator                                                                      :        2 samples, Peak.   179 us (1.3x), Avg.   139 us, Total. 277.3 us (0.0%)
[Performance] InitializeOnLoad UxmlObjectEditorFactories                                                                     :        2 samples, Peak.   384 us (1.1x), Avg.   336 us, Total. 672.9 us (0.0%)
[Performance] InitializeOnLoad UXMLEditorFactories                                                                           :        2 samples, Peak.  39.8 ms (1.0x), Avg.  38.1 ms, Total. 76.19 ms (0.6%)
[Performance] InitializeOnLoad PurchasingService                                                                             :        2 samples, Peak.  4.62 ms (1.1x), Avg.  4.29 ms, Total. 8.588 ms (0.1%)
[Performance] InitializeOnLoad CloudBuildPoller                                                                              :        2 samples, Peak.  73.9 us (1.0x), Avg.  71.2 us, Total. 142.4 us (0.0%)
[Performance] InitializeOnLoad EditorGameServicesAnalytics                                                                   :        2 samples, Peak.   749 us (1.2x), Avg.   609 us, Total. 1.219 ms (0.0%)
[Performance] InitializeOnLoad AnalyticsService                                                                              :        2 samples, Peak.   120 us (1.0x), Avg.   119 us, Total. 238.8 us (0.0%)
[Performance] InitializeOnLoad BuildService                                                                                  :        2 samples, Peak.   106 us (1.0x), Avg.   104 us, Total. 208.7 us (0.0%)
[Performance] InitializeOnLoad AdsService                                                                                    :        2 samples, Peak.   246 us (1.0x), Avg.   244 us, Total. 487.0 us (0.0%)
[Performance] InitializeOnLoad UDPService                                                                                    :        2 samples, Peak.   102 us (1.0x), Avg.   101 us, Total. 201.1 us (0.0%)
[Performance] InitializeOnLoad CrashService                                                                                  :        2 samples, Peak.   100 us (1.0x), Avg.   100 us, Total. 200.5 us (0.0%)
[Performance] InitializeOnLoad ServicesRepository                                                                            :        2 samples, Peak.   417 ns (1.2x), Avg.   355 ns, Total. 709.0 ns (0.0%)
[Performance] InitializeOnLoad SearchWindow                                                                                  :        2 samples, Peak.  85.8 us (1.1x), Avg.  77.8 us, Total. 155.6 us (0.0%)
[Performance] InitializeOnLoad CustomIndexers                                                                                :        2 samples, Peak.  9.72 ms (1.0x), Avg.  9.36 ms, Total. 18.72 ms (0.1%)
[Performance] InitializeOnLoad SearchMonitor                                                                                 :        2 samples, Peak.   679 us (1.2x), Avg.   582 us, Total. 1.165 ms (0.0%)
[Performance] InitializeOnLoad ParameterControllerEditor                                                                     :        2 samples, Peak.  17.6 us (1.1x), Avg.  16.3 us, Total. 32.54 us (0.0%)
[Performance] InitializeOnLoad LayerSettingsWindow                                                                           :        2 samples, Peak.  20.7 us (1.8x), Avg.  11.7 us, Total. 23.50 us (0.0%)
[Performance] InitializeOnLoad PackageManagerHookGUIDConverter.RegisterPackagesEventHandler                                  :        2 samples, Peak.   492 us (1.7x), Avg.   289 us, Total. 578.2 us (0.0%)
[Performance] InitializeOnLoad EditorDragAndDrop.RegisterEditorClient                                                        :        2 samples, Peak.   191 us (1.2x), Avg.   163 us, Total. 326.5 us (0.0%)
[Performance] InitializeOnLoad EditorEventCallbacks.InitializeFontAssetResourceChangeCallBacks                               :        2 samples, Peak.   773 us (1.0x), Avg.   765 us, Total. 1.530 ms (0.0%)
[Performance] InitializeOnLoad SceneTemplateService.Init                                                                     :        2 samples, Peak.   783 us (1.0x), Avg.   779 us, Total. 1.558 ms (0.0%)
[Performance] InitializeOnLoad DiagnosticSwitchesConsoleMessage.Init                                                         :        2 samples, Peak.  2.21 ms (1.6x), Avg.  1.34 ms, Total. 2.685 ms (0.0%)
[Performance] InitializeOnLoad PackageManagerWindow.EditorInitializedInSafeMode                                              :        2 samples, Peak.  16.5 ms (2.0x), Avg.  8.30 ms, Total. 16.61 ms (0.1%)
[Performance] InitializeOnLoad ToolShortcutContext.Init                                                                      :        2 samples, Peak.  91.2 us (1.0x), Avg.  89.5 us, Total. 179.1 us (0.0%)
[Performance] InitializeOnLoad MemoryProfilerCompilationGuard.InjectCompileGuard                                             :        2 samples, Peak.   103 us (1.1x), Avg.  98.1 us, Total. 196.3 us (0.0%)
[Performance] InitializeOnLoad AssetPostprocessingInternal.RefreshCustomDependencies                                         :        2 samples, Peak.  2.05 ms (1.5x), Avg.  1.36 ms, Total. 2.717 ms (0.0%)
[Performance] InitializeOnLoad SysrootManager.Initialize                                                                     :        2 samples, Peak.  8.42 us (1.2x), Avg.  6.77 us, Total. 13.54 us (0.0%)
[Performance] InitializeOnLoad ScopedRegistryAddedPopup.SubscribeToRegistriesAdded                                           :        2 samples, Peak.  5.02 ms (2.0x), Avg.  2.54 ms, Total. 5.073 ms (0.0%)
[Performance] InitializeOnLoad SceneVisibilityManager.Initialize                                                             :        2 samples, Peak.  1.90 ms (1.5x), Avg.  1.23 ms, Total. 2.452 ms (0.0%)
[Performance] InitializeOnLoad EditorWindow.Initialize                                                                       :        2 samples, Peak.   145 us (1.1x), Avg.   137 us, Total. 273.3 us (0.0%)
[Performance] AssemblyReloadEvents.afterAssemblyReload: UnityEditor.SceneTemplate.SceneTemplateService.AfterAssemblyReload   :        2 samples, Peak.  2.07 ms (1.8x), Avg.  1.13 ms, Total. 2.259 ms (0.0%)
[Performance] ProcessService.EditorAfterLoadAllAssemblies                                                                    :        2 samples, Peak.   233 us (1.2x), Avg.   200 us, Total. 400.3 us (0.0%)
[Performance] Application.ReadLicenseInfo                                                                                    :        1 samples, Peak.  24.5 ms (1.0x), Avg.  24.5 ms, Total. 24.49 ms (0.2%)
[Performance] Application.InitialRefresh                                                                                     :        1 samples, Peak.  3.59 s (1.0x), Avg.  3.59 s, Total. 3.588 s (28.5%)
[Performance] Compiling Scripts                                                                                              :        1 samples, Peak.   796 ms (1.0x), Avg.   796 ms, Total. 795.6 ms (6.3%)
[Performance] AssemblyReloadEvents.beforeAssemblyReload: UnityEditor.SceneTemplate.SceneTemplateService.BeforeAssemblyReload :        1 samples, Peak.   317 us (1.0x), Avg.   317 us, Total. 316.5 us (0.0%)
[Performance] InitializeOnLoad TouchSimulation                                                                               :        1 samples, Peak.   149 us (1.0x), Avg.   149 us, Total. 149.2 us (0.0%)
[Performance] InitializeOnLoad EnableUITKEditor                                                                              :        1 samples, Peak.  14.0 us (1.0x), Avg.  14.0 us, Total. 14.00 us (0.0%)
[Performance] InitializeOnLoad InputSystem                                                                                   :        1 samples, Peak.  33.1 ms (1.0x), Avg.  33.1 ms, Total. 33.10 ms (0.3%)
[Performance] InitializeOnLoad InputSystemUIInputModuleEditor                                                                :        1 samples, Peak.  91.4 us (1.0x), Avg.  91.4 us, Total. 91.38 us (0.0%)
[Performance] InitializeOnLoad DeEditorNotification                                                                          :        1 samples, Peak.   368 us (1.0x), Avg.   368 us, Total. 367.6 us (0.0%)
[Performance] InitializeOnLoad DeScriptExecutionOrderManager                                                                 :        1 samples, Peak.   157 us (1.0x), Avg.   157 us, Total. 157.2 us (0.0%)
[Performance] InitializeOnLoad DOTweenProEditorManager                                                                       :        1 samples, Peak.   312 us (1.0x), Avg.   312 us, Total. 311.7 us (0.0%)
[Performance] InitializeOnLoad Initializer                                                                                   :        2 samples, Peak.   125 us (1.1x), Avg.   109 us, Total. 218.3 us (0.0%)
[Performance] InitializeOnLoad ReadmeEditor                                                                                  :        1 samples, Peak.  90.7 us (1.0x), Avg.  90.7 us, Total. 90.67 us (0.0%)
[Performance] InitializeOnLoad VSUsageUtility                                                                                :        1 samples, Peak.  22.0 ms (1.0x), Avg.  22.0 ms, Total. 22.01 ms (0.2%)
[Performance] InitializeOnLoad BackgroundWatcher                                                                             :        1 samples, Peak.  89.3 us (1.0x), Avg.  89.3 us, Total. 89.29 us (0.0%)
[Performance] InitializeOnLoad UnityTestProtocolStarter                                                                      :        1 samples, Peak.   164 us (1.0x), Avg.   164 us, Total. 163.6 us (0.0%)
[Performance] InitializeOnLoad TestStarter                                                                                   :        1 samples, Peak.   388 us (1.0x), Avg.   388 us, Total. 388.1 us (0.0%)
[Performance] InitializeOnLoad RerunCallbackInitializer                                                                      :        1 samples, Peak.  2.04 ms (1.0x), Avg.  2.04 ms, Total. 2.042 ms (0.0%)
[Performance] InitializeOnLoad VisualStudioEditor                                                                            :        1 samples, Peak.  8.90 ms (1.0x), Avg.  8.90 ms, Total. 8.898 ms (0.1%)
[Performance] InitializeOnLoad TestRunnerApiListener                                                                         :        1 samples, Peak.   520 us (1.0x), Avg.   520 us, Total. 520.2 us (0.0%)
[Performance] InitializeOnLoad VisualStudioIntegration                                                                       :        1 samples, Peak.  12.2 ms (1.0x), Avg.  12.2 ms, Total. 12.19 ms (0.1%)
[Performance] InitializeOnLoad PrefabLayoutRebuilder                                                                         :        1 samples, Peak.   209 us (1.0x), Avg.   209 us, Total. 209.1 us (0.0%)
[Performance] InitializeOnLoad RiderScriptEditor                                                                             :        1 samples, Peak.  5.00 ms (1.0x), Avg.  5.00 ms, Total. 5.001 ms (0.0%)
[Performance] InitializeOnLoad CallbackInitializer                                                                           :        1 samples, Peak.   872 us (1.0x), Avg.   872 us, Total. 872.1 us (0.0%)
[Performance] InitializeOnLoad SpriteEditorWindow                                                                            :        1 samples, Peak.   296 us (1.0x), Avg.   296 us, Total. 295.7 us (0.0%)
[Performance] InitializeOnLoad CoverageReporterStarter                                                                       :        1 samples, Peak.  3.46 ms (1.0x), Avg.  3.46 ms, Total. 3.456 ms (0.0%)
[Performance] InitializeOnLoad PlasticPlugin                                                                                 :        1 samples, Peak.  39.2 ms (1.0x), Avg.  39.2 ms, Total. 39.24 ms (0.3%)
[Performance] InitializeOnLoad FungusEditorPreferences                                                                       :        1 samples, Peak.   163 us (1.0x), Avg.   163 us, Total. 162.6 us (0.0%)
[Performance] InitializeOnLoad HierarchyIcons                                                                                :        1 samples, Peak.   119 us (1.0x), Avg.   119 us, Total. 118.8 us (0.0%)
[Performance] InitializeOnLoad ToolbarBootstrap                                                                              :        1 samples, Peak.  33.9 ms (1.0x), Avg.  33.9 ms, Total. 33.92 ms (0.3%)
[Performance] InitializeOnLoad VSCodeScriptEditor                                                                            :        1 samples, Peak.  1.65 ms (1.0x), Avg.  1.65 ms, Total. 1.648 ms (0.0%)
[Performance] InitializeOnLoad SysrootPackage.IssueWarningIfLinuxIL2CPPNotPresent                                            :        1 samples, Peak.   127 us (1.0x), Avg.   127 us, Total. 127.0 us (0.0%)
[Performance] InitializeOnLoad InputSystemPackageControl.SubscribePackageManagerEvent                                        :        1 samples, Peak.  48.8 us (1.0x), Avg.  48.8 us, Total. 48.83 us (0.0%)
[Performance] InitializeOnLoad InputSystemPluginControl.CheckForExtension                                                    :        1 samples, Peak.  1.04 ms (1.0x), Avg.  1.04 ms, Total. 1.043 ms (0.0%)
[Performance] InitializeOnLoad TestJobDataHolder.ResumeRunningJobs                                                           :        1 samples, Peak.   748 us (1.0x), Avg.   748 us, Total. 748.5 us (0.0%)
[Performance] InitializeOnLoad AnalyticsReporter.RegisterCallbacks                                                           :        1 samples, Peak.   196 us (1.0x), Avg.   196 us, Total. 196.5 us (0.0%)
[Performance] InitializeOnLoad VisualStudioEditor.LegacyVisualStudioCodePackageDisabler                                      :        1 samples, Peak.  2.09 ms (1.0x), Avg.  2.09 ms, Total. 2.087 ms (0.0%)
[Performance] DidReloadScriptsNavMeshExtension.OnScriptReload                                                                :        1 samples, Peak.   169 us (1.0x), Avg.   169 us, Total. 168.6 us (0.0%)
[Performance] DidReloadScriptsFungusEditorResources.OnDidReloadScripts                                                       :        1 samples, Peak.  59.1 ms (1.0x), Avg.  59.1 ms, Total. 59.08 ms (0.5%)
[Performance] DidReloadScriptsEventSelectorPopupWindowContent.OnScriptsReloaded                                              :        1 samples, Peak.   637 us (1.0x), Avg.   637 us, Total. 636.9 us (0.0%)
[Performance] DidReloadScriptsCommandSelectorPopupWindowContent.OnScriptsReloaded                                            :        1 samples, Peak.   296 us (1.0x), Avg.   296 us, Total. 295.7 us (0.0%)
[Performance] DidReloadScriptsVariableSelectPopupWindowContent.OnScriptsReloaded                                             :        1 samples, Peak.   139 us (1.0x), Avg.   139 us, Total. 138.7 us (0.0%)
[Performance] DidReloadScriptsAdvancedDropdownWindow.OnScriptReload                                                          :        2 samples, Peak.   184 us (1.2x), Avg.   153 us, Total. 305.5 us (0.0%)
[Performance] DidReloadScriptsTestRunnerWindow.CompilationCallback                                                           :        1 samples, Peak.   313 us (1.0x), Avg.   313 us, Total. 312.5 us (0.0%)
[Performance] DidReloadScriptsTestListCache.ScriptReloaded                                                                   :        1 samples, Peak.   846 us (1.0x), Avg.   846 us, Total. 846.1 us (0.0%)
[Performance] DidReloadScriptsLuaBindingsEditor.DidReloadScripts                                                             :        1 samples, Peak.   280 us (1.0x), Avg.   280 us, Total. 280.1 us (0.0%)
[Performance] InputActionAssetPostprocessor.OnPostprocessAllAssets                                                           :        2 samples, Peak.   279 us (1.2x), Avg.   238 us, Total. 476.5 us (0.0%)
[Performance] InputActionJsonNameModifierAssetProcessor.OnPostprocessAllAssets                                               :        2 samples, Peak.  32.9 us (1.1x), Avg.  31.3 us, Total. 62.54 us (0.0%)
[Performance] ProjectSettingsPostprocessor.OnPostprocessAllAssets                                                            :        2 samples, Peak.   128 us (1.0x), Avg.   127 us, Total. 254.5 us (0.0%)
[Performance] SyncVS.PostprocessSyncProject                                                                                  :        1 samples, Peak.  1.97 ms (1.0x), Avg.  1.97 ms, Total. 1.971 ms (0.0%)
[Performance] Application.ImportPackagesAndSetTemplateWhenCreatingProject                                                    :        1 samples, Peak.  12.7 ms (1.0x), Avg.  12.7 ms, Total. 12.68 ms (0.1%)
[Performance] Application.SyncCurrentColorSpace                                                                              :        1 samples, Peak.  88.4 ms (1.0x), Avg.  88.4 ms, Total. 88.44 ms (0.7%)
[Performance] Application.OnUsbDevicesChanged                                                                                :        1 samples, Peak.  49.4 us (1.0x), Avg.  49.4 us, Total. 49.42 us (0.0%)
[Performance] Application.AssetInstanceCacheUpdate                                                                           :        1 samples, Peak. 0.0 (nanx), Avg. 0.0, Total. 0.0 (0.0%)
[Performance] Application.UnityExtensions.Initialize                                                                         :        1 samples, Peak.  4.54 ms (1.0x), Avg.  4.54 ms, Total. 4.538 ms (0.0%)
[Performance] CodeEditorProjectSync.SyncEditorProject                                                                        :        1 samples, Peak.   169 ms (1.0x), Avg.   169 ms, Total. 168.9 ms (1.3%)
[Performance] Application.ExecuteStartups                                                                                    :        1 samples, Peak.  42.1 ms (1.0x), Avg.  42.1 ms, Total. 42.10 ms (0.3%)
[Performance] Menu.RegisterMenuInterface                                                                                     :       26 samples, Peak.  10.4 us (20.6x), Avg.   506 ns, Total. 13.17 us (0.0%)
[Performance] Gizmo.RebuildRenderers                                                                                         :        1 samples, Peak.  41.2 ms (1.0x), Avg.  41.2 ms, Total. 41.17 ms (0.3%)
[Performance] Gizmo.AddGizmoRenderers                                                                                        :       96 samples, Peak.   976 us (40.7x), Avg.  24.0 us, Total. 2.302 ms (0.0%)
[Performance] Application.editorInitializingProject                                                                          :        1 samples, Peak.  15.5 ms (1.0x), Avg.  15.5 ms, Total. 15.47 ms (0.1%)
[Performance] Application.InitializeMenu                                                                                     :        1 samples, Peak.   860 ms (1.0x), Avg.   860 ms, Total. 860.3 ms (6.8%)
[Performance] Menu.RebuildAll                                                                                                :        1 samples, Peak.   860 ms (1.0x), Avg.   860 ms, Total. 860.3 ms (6.8%)
[Performance] Menu.BuildRegisteredMenuInterfaces                                                                             :        1 samples, Peak.   856 ms (1.0x), Avg.   856 ms, Total. 856.0 ms (6.8%)
[Performance] Menu.FilterMenuItem                                                                                            :      796 samples, Peak.  25.4 ms (787.4x), Avg.  32.3 us, Total. 25.67 ms (0.2%)
[Performance] UpdateAllMenus                                                                                                 :        1 samples, Peak.   459 us (1.0x), Avg.   459 us, Total. 459.0 us (0.0%)
[Performance] EditorSceneManager.sceneClosing: UnityEditor.SceneVisibilityManager.EditorSceneManagerOnSceneClosing           :        1 samples, Peak.   184 us (1.0x), Avg.   184 us, Total. 183.7 us (0.0%)
[Performance] GUIView.RepaintAll.PlayerLoopController                                                                        :        1 samples, Peak.   791 ns (1.0x), Avg.   791 ns, Total. 791.0 ns (0.0%)
[Performance] EditorSceneManager.newSceneCreated: UnityEditor.SceneTemplate.SceneTemplateService.OnNewSceneCreated           :        1 samples, Peak.   163 us (1.0x), Avg.   163 us, Total. 162.6 us (0.0%)
[Performance] EditorSceneManager.newSceneCreated: UnityEditor.SceneVisibilityManager.EditorSceneManagerOnNewSceneCreated     :        1 samples, Peak.   114 us (1.0x), Avg.   114 us, Total. 113.9 us (0.0%)
[Performance] EditorSceneManager.newSceneCreated: UnityEditor.SceneManagement.StageNavigationManager.OnNewSceneCreated       :        1 samples, Peak.  34.6 us (1.0x), Avg.  34.6 us, Total. 34.63 us (0.0%)
[Performance] Application.InvokeFinishedLoadingProject                                                                       :        1 samples, Peak.  8.99 ms (1.0x), Avg.  8.99 ms, Total. 8.994 ms (0.1%)
[Performance] ProcessService.OnProjectLoaded                                                                                 :        1 samples, Peak.  38.5 us (1.0x), Avg.  38.5 us, Total. 38.46 us (0.0%)
[Performance] VersionControl.Task.Wait                                                                                       :        1 samples, Peak.  5.21 us (1.0x), Avg.  5.21 us, Total. 5.208 us (0.0%)
[Performance] EditorApplication.quitting: UnityEditor.SettingsManagement.FileSettingsRepository.Save                         :        1 samples, Peak.   839 us (1.0x), Avg.   839 us, Total. 838.5 us (0.0%)
[Performance] EditorApplication.quitting: callback in UnityEditor.TextCore.Text.EditorEventCallbacks                         :        1 samples, Peak.   115 ms (1.0x), Avg.   115 ms, Total. 115.2 ms (0.9%)
[Performance] Killing ADB server                                                                                             :        1 samples, Peak.  34.5 ms (1.0x), Avg.  34.5 ms, Total. 34.47 ms (0.3%)
[Performance] Application.Shutdown.PauseProfilerSession                                                                      :        1 samples, Peak.   355 us (1.0x), Avg.   355 us, Total. 355.1 us (0.0%)
[Performance] Application.Shutdown.PauseAssetImportWorkers                                                                   :        1 samples, Peak.   262 us (1.0x), Avg.   262 us, Total. 262.1 us (0.0%)
[Performance] Application.Shutdown.SaveAssets                                                                                :        1 samples, Peak.  6.80 ms (1.0x), Avg.  6.80 ms, Total. 6.797 ms (0.1%)
[Performance] StateMacroSavedEvent.OnWillSaveAssets                                                                          :        1 samples, Peak.   129 us (1.0x), Avg.   129 us, Total. 128.8 us (0.0%)
[Performance] AssetModProcessor.OnWillSaveAssets                                                                             :        1 samples, Peak.   159 us (1.0x), Avg.   159 us, Total. 159.5 us (0.0%)
[Performance] AssetModificationProcessor.OnWillSaveAssets                                                                    :        1 samples, Peak.   225 us (1.0x), Avg.   225 us, Total. 224.9 us (0.0%)
[Performance] UnityCloudProjectLinkMonitor.OnWillSaveAssets                                                                  :        1 samples, Peak.   192 us (1.0x), Avg.   192 us, Total. 192.5 us (0.0%)
[Performance] FlowMacroSavedEvent.OnWillSaveAssets                                                                           :        1 samples, Peak.  87.5 us (1.0x), Avg.  87.5 us, Total. 87.50 us (0.0%)
[Performance] BuilderAssetModificationProcessor.OnWillSaveAssets                                                             :        1 samples, Peak.   319 us (1.0x), Avg.   319 us, Total. 318.8 us (0.0%)
[Performance] TerrainModificationProcessor.OnWillSaveAssets                                                                  :        1 samples, Peak.   432 us (1.0x), Avg.   432 us, Total. 432.3 us (0.0%)
[Performance] AssetDatabase.ImportAssets                                                                                     :        2 samples, Peak.   333 ns (1.8x), Avg.   187 ns, Total. 374.0 ns (0.0%)
[Performance] Application.Shutdown.CleanupRenderPipeline                                                                     :        1 samples, Peak.  8.58 us (1.0x), Avg.  8.58 us, Total. 8.583 us (0.0%)
[Performance] Application.Shutdown.StopPreloadManager                                                                        :        1 samples, Peak.  10.7 ms (1.0x), Avg.  10.7 ms, Total. 10.74 ms (0.1%)
[Performance] Application.Shutdown.DestroyWorld                                                                              :        1 samples, Peak.   564 us (1.0x), Avg.   564 us, Total. 563.7 us (0.0%)
[Performance] Application.Shutdown.CleanupAfterLoad                                                                          :        1 samples, Peak.  2.81 ms (1.0x), Avg.  2.81 ms, Total. 2.813 ms (0.0%)
[Performance] Application.Shutdown.Progress                                                                                  :        1 samples, Peak.  15.8 us (1.0x), Avg.  15.8 us, Total. 15.83 us (0.0%)
[Performance] Application.Shutdown.GICleanupManagers                                                                         :        1 samples, Peak.  1.95 ms (1.0x), Avg.  1.95 ms, Total. 1.952 ms (0.0%)
[Performance] Application.Shutdown.MenuCleanupClass                                                                          :        1 samples, Peak.   163 us (1.0x), Avg.   163 us, Total. 162.8 us (0.0%)
[Performance] Application.Shutdown.ADBSaveStateBeforeShutdown                                                                :        1 samples, Peak.  11.3 us (1.0x), Avg.  11.3 us, Total. 11.33 us (0.0%)
[Performance] Application.Shutdown.RemoteShutdown                                                                            :        1 samples, Peak.   750 ns (1.0x), Avg.   750 ns, Total. 750.0 ns (0.0%)
[Performance] Application.Shutdown.CleanupVCProvider                                                                         :        1 samples, Peak.   179 us (1.0x), Avg.   179 us, Total. 179.3 us (0.0%)
[Performance] Application.Shutdown.InputShutdown                                                                             :        1 samples, Peak.   164 us (1.0x), Avg.   164 us, Total. 164.3 us (0.0%)
[Performance] Application.Shutdown.GizmoManagerDestroy                                                                       :        1 samples, Peak.   300 us (1.0x), Avg.   300 us, Total. 299.5 us (0.0%)
[Performance] Application.Shutdown.ProfilerSession                                                                           :        1 samples, Peak.   471 us (1.0x), Avg.   471 us, Total. 470.8 us (0.0%)
[Performance] Application.Shutdown.ReleaseGfxWindowOnAllGUIViews                                                             :        1 samples, Peak.  1.75 us (1.0x), Avg.  1.75 us, Total. 1.750 us (0.0%)
[Performance] Application.Shutdown.CleanupEngine                                                                             :        1 samples, Peak.   125 ms (1.0x), Avg.   125 ms, Total. 125.1 ms (1.0%)
[Performance] Application.Shutdown.CleanupAssetDatabase                                                                      :        1 samples, Peak.  3.89 ms (1.0x), Avg.  3.89 ms, Total. 3.887 ms (0.0%)
[Performance] Application.Shutdown.ScriptCompilationCleanUp                                                                  :        1 samples, Peak.  15.3 us (1.0x), Avg.  15.3 us, Total. 15.29 us (0.0%)
[Performance] Application.Shutdown.DestroyJobSystem                                                                          :        1 samples, Peak.   296 us (1.0x), Avg.   296 us, Total. 296.5 us (0.0%)
[Performance] Application.Shutdown.CleanupPersistentManager                                                                  :        1 samples, Peak.  2.08 ms (1.0x), Avg.  2.08 ms, Total. 2.078 ms (0.0%)
[Performance] Application.Shutdown.CleanupAsyncReadManager                                                                   :        1 samples, Peak.  92.8 us (1.0x), Avg.  92.8 us, Total. 92.79 us (0.0%)
[Performance] Application.Shutdown.CleanupMono                                                                               :        1 samples, Peak.  56.8 ms (1.0x), Avg.  56.8 ms, Total. 56.81 ms (0.5%)
[Performance] Application.Shutdown.CleanupStdConverters                                                                      :        1 samples, Peak.   199 us (1.0x), Avg.   199 us, Total. 198.7 us (0.0%)
[Performance] Application.Shutdown.UnloadAllPlatformSupportModuleNativeDlls                                                  :        1 samples, Peak.   500 ns (1.0x), Avg.   500 ns, Total. 500.0 ns (0.0%)
[Performance] Application.Shutdown.UnloadAllPlatformSupportNativeLibraries                                                   :        1 samples, Peak.  88.8 us (1.0x), Avg.  88.8 us, Total. 88.83 us (0.0%)
[Performance] Application.Shutdown.CleanupAutoDocumentation                                                                  :        1 samples, Peak.   218 us (1.0x), Avg.   218 us, Total. 218.0 us (0.0%)
[Performance] Application.Shutdown.ShaderNameManagerDestroy                                                                  :        1 samples, Peak.   244 us (1.0x), Avg.   244 us, Total. 244.2 us (0.0%)
[Performance] Application.Shutdown.CleanupCacheServer                                                                        :        1 samples, Peak.   167 ns (1.0x), Avg.   167 ns, Total. 167.0 ns (0.0%)
[Performance] Application.Shutdown.Virtualization_Shutdown                                                                   :        1 samples, Peak.   250 ns (1.0x), Avg.   250 ns, Total. 250.0 ns (0.0%)
[Performance] Application.Shutdown.DevConnections                                                                            :        1 samples, Peak.  53.2 us (1.0x), Avg.  53.2 us, Total. 53.17 us (0.0%)
Exiting batchmode successfully now!
[Package Manager] Server::Kill -- Server was shutdown
