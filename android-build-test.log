Unity Editor version:    2022.3.62f1 (4af31df58517)
Branch:                  2022.3/staging
Build type:              Release
Batch mode:              YES
macOS version:           Version 15.5 (Build 24F74)
Darwin version:          24.5.0
Architecture:            arm64
Running under Rosetta:   NO
Available memory:        16384 MB
[Licensing::Module] Trying to connect to existing licensing client channel...
[Licensing::IpcConnector] Successfully connected to: "LicenseClient-cafe" at "2025-06-25T14:39:35.40837Z"
[Licensing::Client] Code 10 while verifying Licensing Client signature (process Id: 47956, path: "/Applications/Unity Hub.app/Contents/Frameworks/UnityLicensingClient_V1.app/Contents/MacOS/Unity.Licensing.Client")
[Licensing::Module] LicensingClient has failed validation; ignoring
[Licensing::Client] Handshaking with LicensingClient:
  Version:                 1.17.0+aa6cfba
  Session Id:              a96263c134034913bcf35f6a889f0d07
  Correlation Id:          add1fd4bf48fcb70563aa0051c34e7a0
  External correlation Id: 6130620233636135018
  Machine Id:              SaO/+vEl19cOZCJ9iXNm5zgvkFY=
[Licensing::Module] Successfully connected to LicensingClient on channel: "LicenseClient-cafe" (connect: 0.00s, validation: 0.01s, handshake: 0.23s)
[Licensing::IpcConnector] Successfully connected to: "LicenseClient-cafe-notifications" at "2025-06-25T14:39:35.649897Z"
[Licensing::Module] Error: Access token is unavailable; failed to update
[Licensing::Client] Successfully updated license
[Licensing::Client] Successfully resolved entitlement details
[Licensing::Module] Serial number assigned to: "F4-2R2W-WK36-W2FA-CS3P-XXXX"
[Licensing::Module] Serial number assigned to: "1375833638986-UnityPersXXXX"
Pro License: NO
[Licensing::Client] Successfully resolved entitlement details
[Licensing::Module] Serial number assigned to: "F4-2R2W-WK36-W2FA-CS3P-XXXX"
[Licensing::Module] Serial number assigned to: "1375833638986-UnityPersXXXX"
Launching external process: /Applications/Unity/Hub/Editor/2022.3.62f1/Unity.app/Contents/Resources/PackageManager/Server/UnityPackageManager

COMMAND LINE ARGUMENTS:
/Applications/Unity/Hub/Editor/2022.3.62f1/Unity.app/Contents/MacOS/Unity
-batchmode
-quit
-projectPath
/Users/<USER>/Documents/GitHub/PerfumerProject
-buildTarget
Android
-executeMethod
BuildTools.CommandLineBuild.BuildAndroid
-logFile
/Users/<USER>/Documents/GitHub/PerfumerProject/android-build-test.log
Successfully changed project path to: /Users/<USER>/Documents/GitHub/PerfumerProject
/Users/<USER>/Documents/GitHub/PerfumerProject
[UnityMemory] Configuration Parameters - Can be set up in boot.config
    "memorysetup-bucket-allocator-granularity=16"
    "memorysetup-bucket-allocator-bucket-count=8"
    "memorysetup-bucket-allocator-block-size=33554432"
    "memorysetup-bucket-allocator-block-count=8"
    "memorysetup-main-allocator-block-size=16777216"
    "memorysetup-thread-allocator-block-size=16777216"
    "memorysetup-gfx-main-allocator-block-size=16777216"
    "memorysetup-gfx-thread-allocator-block-size=16777216"
    "memorysetup-cache-allocator-block-size=4194304"
    "memorysetup-typetree-allocator-block-size=2097152"
    "memorysetup-profiler-bucket-allocator-granularity=16"
    "memorysetup-profiler-bucket-allocator-bucket-count=8"
    "memorysetup-profiler-bucket-allocator-block-size=33554432"
    "memorysetup-profiler-bucket-allocator-block-count=8"
    "memorysetup-profiler-allocator-block-size=16777216"
    "memorysetup-profiler-editor-allocator-block-size=1048576"
    "memorysetup-temp-allocator-size-main=16777216"
    "memorysetup-job-temp-allocator-block-size=2097152"
    "memorysetup-job-temp-allocator-block-size-background=1048576"
    "memorysetup-job-temp-allocator-reduction-small-platforms=262144"
    "memorysetup-allocator-temp-initial-block-size-main=262144"
    "memorysetup-allocator-temp-initial-block-size-worker=262144"
    "memorysetup-temp-allocator-size-background-worker=32768"
    "memorysetup-temp-allocator-size-job-worker=262144"
    "memorysetup-temp-allocator-size-preload-manager=33554432"
    "memorysetup-temp-allocator-size-nav-mesh-worker=65536"
    "memorysetup-temp-allocator-size-audio-worker=65536"
    "memorysetup-temp-allocator-size-cloud-worker=32768"
    "memorysetup-temp-allocator-size-gi-baking-worker=262144"
    "memorysetup-temp-allocator-size-gfx=262144"
Player connection [8499977984]  Target information:

Player connection [8499977984]  * "[IP] ************** [Port] 55504 [Flags] 2 [Guid] 729139080 [EditorId] 729139080 [Version] 1048832 [Id] OSXEditor(0,MacBookPro) [Debug] 1 [PackageName] OSXEditor [ProjectName] Editor" 

Player connection [8499977984] Host joined multi-casting on [***********:54997]...
Player connection [8499977984] Host joined alternative multi-casting on [***********:34997]...
Input System module state changed to: Initialized.
[PhysX] Initialized MultithreadedTaskDispatcher with 8 workers.
[Package Manager] UpmClient::Connect -- Connected to IPC stream "Upm-72054" after 0.5 seconds.
[Package Manager] Restoring resolved packages state from cache
[Licensing::Client] Successfully resolved entitlement details
[Package Manager] Registered 53 packages:
  Packages from [https://packages.unity.com]:
    com.unity.nuget.newtonsoft-json@3.2.1 (location: /Users/<USER>/Documents/GitHub/PerfumerProject/Library/PackageCache/com.unity.nuget.newtonsoft-json@3.2.1)
    com.unity.collab-proxy@2.7.1 (location: /Users/<USER>/Documents/GitHub/PerfumerProject/Library/PackageCache/com.unity.collab-proxy@2.7.1)
    com.unity.inputsystem@1.14.0 (location: /Users/<USER>/Documents/GitHub/PerfumerProject/Library/PackageCache/com.unity.inputsystem@1.14.0)
    com.unity.textmeshpro@3.0.7 (location: /Users/<USER>/Documents/GitHub/PerfumerProject/Library/PackageCache/com.unity.textmeshpro@3.0.7)
    com.unity.timeline@1.7.7 (location: /Users/<USER>/Documents/GitHub/PerfumerProject/Library/PackageCache/com.unity.timeline@1.7.7)
    com.unity.toolchain.macos-arm64-linux-x86_64@2.0.4 (location: /Users/<USER>/Documents/GitHub/PerfumerProject/Library/PackageCache/com.unity.toolchain.macos-arm64-linux-x86_64@2.0.4)
    com.unity.visualscripting@1.9.4 (location: /Users/<USER>/Documents/GitHub/PerfumerProject/Library/PackageCache/com.unity.visualscripting@1.9.4)
    com.unity.sysroot@2.0.10 (location: /Users/<USER>/Documents/GitHub/PerfumerProject/Library/PackageCache/com.unity.sysroot@2.0.10)
    com.unity.sysroot.linux-x86_64@2.0.9 (location: /Users/<USER>/Documents/GitHub/PerfumerProject/Library/PackageCache/com.unity.sysroot.linux-x86_64@2.0.9)
    com.unity.ide.visualstudio@2.0.22 (location: /Users/<USER>/Documents/GitHub/PerfumerProject/Library/PackageCache/com.unity.ide.visualstudio@2.0.22)
    com.unity.ide.rider@3.0.36 (location: /Users/<USER>/Documents/GitHub/PerfumerProject/Library/PackageCache/com.unity.ide.rider@3.0.36)
    com.unity.ide.vscode@1.2.5 (location: /Users/<USER>/Documents/GitHub/PerfumerProject/Library/PackageCache/com.unity.ide.vscode@1.2.5)
    com.unity.editorcoroutines@1.0.0 (location: /Users/<USER>/Documents/GitHub/PerfumerProject/Library/PackageCache/com.unity.editorcoroutines@1.0.0)
    com.unity.performance.profile-analyzer@1.2.3 (location: /Users/<USER>/Documents/GitHub/PerfumerProject/Library/PackageCache/com.unity.performance.profile-analyzer@1.2.3)
    com.unity.test-framework@1.1.33 (location: /Users/<USER>/Documents/GitHub/PerfumerProject/Library/PackageCache/com.unity.test-framework@1.1.33)
    com.unity.testtools.codecoverage@1.2.6 (location: /Users/<USER>/Documents/GitHub/PerfumerProject/Library/PackageCache/com.unity.testtools.codecoverage@1.2.6)
    com.unity.settings-manager@2.1.0 (location: /Users/<USER>/Documents/GitHub/PerfumerProject/Library/PackageCache/com.unity.settings-manager@2.1.0)
    com.unity.ext.nunit@1.0.6 (location: /Users/<USER>/Documents/GitHub/PerfumerProject/Library/PackageCache/com.unity.ext.nunit@1.0.6)
  Built-in packages:
    com.unity.2d.sprite@1.0.0 (location: /Users/<USER>/Documents/GitHub/PerfumerProject/Library/PackageCache/com.unity.2d.sprite@1.0.0)
    com.unity.feature.development@1.0.1 (location: /Users/<USER>/Documents/GitHub/PerfumerProject/Library/PackageCache/com.unity.feature.development@1.0.1)
    com.unity.ugui@1.0.0 (location: /Users/<USER>/Documents/GitHub/PerfumerProject/Library/PackageCache/com.unity.ugui@1.0.0)
    com.unity.modules.ai@1.0.0 (location: /Users/<USER>/Documents/GitHub/PerfumerProject/Library/PackageCache/com.unity.modules.ai@1.0.0)
    com.unity.modules.androidjni@1.0.0 (location: /Users/<USER>/Documents/GitHub/PerfumerProject/Library/PackageCache/com.unity.modules.androidjni@1.0.0)
    com.unity.modules.animation@1.0.0 (location: /Users/<USER>/Documents/GitHub/PerfumerProject/Library/PackageCache/com.unity.modules.animation@1.0.0)
    com.unity.modules.assetbundle@1.0.0 (location: /Users/<USER>/Documents/GitHub/PerfumerProject/Library/PackageCache/com.unity.modules.assetbundle@1.0.0)
    com.unity.modules.audio@1.0.0 (location: /Users/<USER>/Documents/GitHub/PerfumerProject/Library/PackageCache/com.unity.modules.audio@1.0.0)
    com.unity.modules.cloth@1.0.0 (location: /Users/<USER>/Documents/GitHub/PerfumerProject/Library/PackageCache/com.unity.modules.cloth@1.0.0)
    com.unity.modules.director@1.0.0 (location: /Users/<USER>/Documents/GitHub/PerfumerProject/Library/PackageCache/com.unity.modules.director@1.0.0)
    com.unity.modules.imageconversion@1.0.0 (location: /Users/<USER>/Documents/GitHub/PerfumerProject/Library/PackageCache/com.unity.modules.imageconversion@1.0.0)
    com.unity.modules.imgui@1.0.0 (location: /Users/<USER>/Documents/GitHub/PerfumerProject/Library/PackageCache/com.unity.modules.imgui@1.0.0)
    com.unity.modules.jsonserialize@1.0.0 (location: /Users/<USER>/Documents/GitHub/PerfumerProject/Library/PackageCache/com.unity.modules.jsonserialize@1.0.0)
    com.unity.modules.particlesystem@1.0.0 (location: /Users/<USER>/Documents/GitHub/PerfumerProject/Library/PackageCache/com.unity.modules.particlesystem@1.0.0)
    com.unity.modules.physics@1.0.0 (location: /Users/<USER>/Documents/GitHub/PerfumerProject/Library/PackageCache/com.unity.modules.physics@1.0.0)
    com.unity.modules.physics2d@1.0.0 (location: /Users/<USER>/Documents/GitHub/PerfumerProject/Library/PackageCache/com.unity.modules.physics2d@1.0.0)
    com.unity.modules.screencapture@1.0.0 (location: /Users/<USER>/Documents/GitHub/PerfumerProject/Library/PackageCache/com.unity.modules.screencapture@1.0.0)
    com.unity.modules.terrain@1.0.0 (location: /Users/<USER>/Documents/GitHub/PerfumerProject/Library/PackageCache/com.unity.modules.terrain@1.0.0)
    com.unity.modules.terrainphysics@1.0.0 (location: /Users/<USER>/Documents/GitHub/PerfumerProject/Library/PackageCache/com.unity.modules.terrainphysics@1.0.0)
    com.unity.modules.tilemap@1.0.0 (location: /Users/<USER>/Documents/GitHub/PerfumerProject/Library/PackageCache/com.unity.modules.tilemap@1.0.0)
    com.unity.modules.ui@1.0.0 (location: /Users/<USER>/Documents/GitHub/PerfumerProject/Library/PackageCache/com.unity.modules.ui@1.0.0)
    com.unity.modules.uielements@1.0.0 (location: /Users/<USER>/Documents/GitHub/PerfumerProject/Library/PackageCache/com.unity.modules.uielements@1.0.0)
    com.unity.modules.umbra@1.0.0 (location: /Users/<USER>/Documents/GitHub/PerfumerProject/Library/PackageCache/com.unity.modules.umbra@1.0.0)
    com.unity.modules.unityanalytics@1.0.0 (location: /Users/<USER>/Documents/GitHub/PerfumerProject/Library/PackageCache/com.unity.modules.unityanalytics@1.0.0)
    com.unity.modules.unitywebrequest@1.0.0 (location: /Users/<USER>/Documents/GitHub/PerfumerProject/Library/PackageCache/com.unity.modules.unitywebrequest@1.0.0)
    com.unity.modules.unitywebrequestassetbundle@1.0.0 (location: /Users/<USER>/Documents/GitHub/PerfumerProject/Library/PackageCache/com.unity.modules.unitywebrequestassetbundle@1.0.0)
    com.unity.modules.unitywebrequestaudio@1.0.0 (location: /Users/<USER>/Documents/GitHub/PerfumerProject/Library/PackageCache/com.unity.modules.unitywebrequestaudio@1.0.0)
    com.unity.modules.unitywebrequesttexture@1.0.0 (location: /Users/<USER>/Documents/GitHub/PerfumerProject/Library/PackageCache/com.unity.modules.unitywebrequesttexture@1.0.0)
    com.unity.modules.unitywebrequestwww@1.0.0 (location: /Users/<USER>/Documents/GitHub/PerfumerProject/Library/PackageCache/com.unity.modules.unitywebrequestwww@1.0.0)
    com.unity.modules.vehicles@1.0.0 (location: /Users/<USER>/Documents/GitHub/PerfumerProject/Library/PackageCache/com.unity.modules.vehicles@1.0.0)
    com.unity.modules.video@1.0.0 (location: /Users/<USER>/Documents/GitHub/PerfumerProject/Library/PackageCache/com.unity.modules.video@1.0.0)
    com.unity.modules.vr@1.0.0 (location: /Users/<USER>/Documents/GitHub/PerfumerProject/Library/PackageCache/com.unity.modules.vr@1.0.0)
    com.unity.modules.wind@1.0.0 (location: /Users/<USER>/Documents/GitHub/PerfumerProject/Library/PackageCache/com.unity.modules.wind@1.0.0)
    com.unity.modules.xr@1.0.0 (location: /Users/<USER>/Documents/GitHub/PerfumerProject/Library/PackageCache/com.unity.modules.xr@1.0.0)
    com.unity.modules.subsystems@1.0.0 (location: /Users/<USER>/Documents/GitHub/PerfumerProject/Library/PackageCache/com.unity.modules.subsystems@1.0.0)
[Subsystems] No new subsystems found in resolved package list.
Package Manager log level set to [2]
[Package Manager] Done registering packages in 0.14 seconds
[Licensing::Client] Successfully resolved entitlement details
[Licensing::Module] Serial number assigned to: "F4-2R2W-WK36-W2FA-CS3P-XXXX"
[Licensing::Module] Serial number assigned to: "1375833638986-UnityPersXXXX"
Targeting platform: Android
Refreshing native plugins compatible for Editor in 17.82 ms, found 2 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Initialize engine version: 2022.3.62f1 (4af31df58517)
[Subsystems] Discovering subsystems at path /Applications/Unity/Hub/Editor/2022.3.62f1/Unity.app/Contents/Resources/UnitySubsystems
[Subsystems] Discovering subsystems at path /Users/<USER>/Documents/GitHub/PerfumerProject/Assets
GfxDevice: creating device client; threaded=0; jobified=0
 preferred device: Apple M1 (high power)
Metal devices available: 1
0: Apple M1 (high power)
Using device Apple M1 (high power)
Initializing Metal device caps: Apple M1
[Licensing::Client] Successfully resolved entitlement details
[Licensing::Module] Serial number assigned to: "F4-2R2W-WK36-W2FA-CS3P-XXXX"
[Licensing::Module] Serial number assigned to: "1375833638986-UnityPersXXXX"
Initialize mono
Mono path[0] = '/Applications/Unity/Hub/Editor/2022.3.62f1/Unity.app/Contents/Managed'
Mono path[1] = '/Applications/Unity/Hub/Editor/2022.3.62f1/Unity.app/Contents/MonoBleedingEdge/lib/mono/unityjit-macos'
Mono config path = '/Applications/Unity/Hub/Editor/2022.3.62f1/Unity.app/Contents/MonoBleedingEdge/etc'
Using monoOptions --debugger-agent=transport=dt_socket,embedding=1,server=y,suspend=n,address=127.0.0.1:56054
Using cacheserver namespaces - metadata:defaultmetadata, artifacts:defaultartifacts
Using cacheserver namespaces - metadata:defaultmetadata, artifacts:defaultartifacts
ImportWorker Server TCP listen port: 0
Begin MonoManager ReloadAssembly
Registering precompiled unity dll's ...
Register platform support module: /Applications/Unity/Hub/Editor/2022.3.62f1/PlaybackEngines/WebGLSupport/UnityEditor.WebGL.Extensions.dll
Register platform support module: /Applications/Unity/Hub/Editor/2022.3.62f1/Unity.app/Contents/PlaybackEngines/MacStandaloneSupport/UnityEditor.OSXStandalone.Extensions.dll
Register platform support module: /Applications/Unity/Hub/Editor/2022.3.62f1/PlaybackEngines/AndroidPlayer/UnityEditor.Android.Extensions.dll
[Licensing::Client] Successfully resolved entitlement details
[Licensing::Module] Serial number assigned to: "F4-2R2W-WK36-W2FA-CS3P-XXXX"
[Licensing::Module] Serial number assigned to: "1375833638986-UnityPersXXXX"
Register platform support module: /Applications/Unity/Hub/Editor/2022.3.62f1/PlaybackEngines/iOSSupport/UnityEditor.iOS.Extensions.dll
Register platform support module: /Applications/Unity/Hub/Editor/2022.3.62f1/PlaybackEngines/VisionOSPlayer/UnityEditor.VisionOS.Extensions.dll
Register platform support module: /Applications/Unity/Hub/Editor/2022.3.62f1/PlaybackEngines/AppleTVSupport/UnityEditor.AppleTV.Extensions.dll
Register platform support module: /Applications/Unity/Hub/Editor/2022.3.62f1/PlaybackEngines/WindowsStandaloneSupport/UnityEditor.WindowsStandalone.Extensions.dll
Register platform support module: /Applications/Unity/Hub/Editor/2022.3.62f1/PlaybackEngines/LinuxStandaloneSupport/UnityEditor.LinuxStandalone.Extensions.dll
Registered in 0.017993 seconds.
- Loaded All Assemblies, in  0.280 seconds
Native extension for LinuxStandalone target not found
Native extension for WindowsStandalone target not found
[usbmuxd] Start listen thread
[usbmuxd] Listen thread started
[usbmuxd] Send listen message
Native extension for AppleTV target not found
objc[72054]: Class XcodeScriptingDelegate is implemented in both /Applications/Unity/Hub/Editor/2022.3.62f1/PlaybackEngines/AppleTVSupport/arm64/UnityEditor.iOS.Native.dylib (0x13d3845c8) and /Applications/Unity/Hub/Editor/2022.3.62f1/PlaybackEngines/VisionOSPlayer/arm64/UnityEditor.VisionOS.Native.dylib (0x13d3b05c8). This may cause spurious casting failures and mysterious crashes. One of the duplicates must be removed or renamed.
[usbmuxd] Start listen thread
[usbmuxd] Listen thread started
[usbmuxd] Send listen message
Native extension for VisionOS target not found
Native extension for iOS target not found
Native extension for Android target not found
Android Extension - Scanning For ADB Devices 166 ms
Native extension for OSXStandalone target not found
Native extension for WebGL target not found
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  0.413 seconds
Domain Reload Profiling: 693ms
	BeginReloadAssembly (89ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (0ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (1ms)
	RebuildCommonClasses (26ms)
	RebuildNativeTypeToScriptingClass (8ms)
	initialDomainReloadingComplete (47ms)
	LoadAllAssembliesAndSetupDomain (110ms)
		LoadAssemblies (92ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (104ms)
			TypeCache.Refresh (103ms)
				TypeCache.ScanAssembly (91ms)
			ScanForSourceGeneratedMonoScriptInfo (0ms)
			ResolveRequiredComponents (0ms)
	FinalizeReload (413ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (386ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (288ms)
			SetLoadedEditorAssemblies (5ms)
			RefreshPlugins (0ms)
			BeforeProcessingInitializeOnLoad (2ms)
			ProcessInitializeOnLoadAttributes (68ms)
			ProcessInitializeOnLoadMethodAttributes (23ms)
			AfterProcessingInitializeOnLoad (0ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (0ms)
[Licensing::Client] Successfully resolved entitlement details
[Licensing::Module] Serial number assigned to: "F4-2R2W-WK36-W2FA-CS3P-XXXX"
[Licensing::Module] Serial number assigned to: "1375833638986-UnityPersXXXX"
[Licensing::Client] Successfully resolved entitlement details
[Licensing::Module] Serial number assigned to: "F4-2R2W-WK36-W2FA-CS3P-XXXX"
[Licensing::Module] Serial number assigned to: "1375833638986-UnityPersXXXX"
Application.AssetDatabase Initial Refresh Start
The GUID inside 'Assets/FlexReader/Manual.pdf.meta' cannot be extracted by the YAML Parser. Attempting to extract it via string matching instead. Please verify the file does not contain unexpected data.
The GUID inside 'Assets/Resources/Prefabs/UI/Item_Sell.prefab.meta' cannot be extracted by the YAML Parser. Attempting to extract it via string matching instead. Please verify the file does not contain unexpected data.
The GUID inside 'Assets/Scenes/Sell.unity.meta' cannot be extracted by the YAML Parser. Attempting to extract it via string matching instead. Please verify the file does not contain unexpected data.
The GUID inside 'Assets/StreamingAssets/PerfumerData.xlsx.meta' cannot be extracted by the YAML Parser. Attempting to extract it via string matching instead. Please verify the file does not contain unexpected data.
[ScriptCompilation] Requested script compilation because: Assetdatabase observed changes in script compilation related files
[40m[32minfo[39m[22m[49m: Microsoft.Hosting.Lifetime[14]
      Now listening on: http://unix:/tmp/ilpp.sock-eae4890779ba5942efbfb0ce14813848
[40m[32minfo[39m[22m[49m: Microsoft.Hosting.Lifetime[0]
      Application started. Press Ctrl+C to shut down.
[40m[32minfo[39m[22m[49m: Microsoft.Hosting.Lifetime[0]
      Hosting environment: Production
[40m[32minfo[39m[22m[49m: Microsoft.Hosting.Lifetime[0]
      Content root path: /Users/<USER>/Documents/GitHub/PerfumerProject/
[40m[32minfo[39m[22m[49m: Microsoft.AspNetCore.Hosting.Diagnostics[1]
      Request starting HTTP/2 POST http://ilpp/UnityILPP.PostProcessing/Ping application/grpc -
[40m[32minfo[39m[22m[49m: Microsoft.AspNetCore.Routing.EndpointMiddleware[0]
      Executing endpoint 'gRPC - /UnityILPP.PostProcessing/Ping'
[40m[32minfo[39m[22m[49m: Microsoft.AspNetCore.Routing.EndpointMiddleware[1]
      Executed endpoint 'gRPC - /UnityILPP.PostProcessing/Ping'
[40m[32minfo[39m[22m[49m: Microsoft.AspNetCore.Hosting.Diagnostics[2]
      Request finished HTTP/2 POST http://ilpp/UnityILPP.PostProcessing/Ping application/grpc - - 200 - application/grpc 43.0191ms
Starting: /Applications/Unity/Hub/Editor/2022.3.62f1/Unity.app/Contents/bee_backend --ipc --defer-dag-verification --dagfile="Library/Bee/1300b0aE.dag" --continue-on-failure --profile="Library/Bee/backend1.traceevents" ScriptAssemblies
WorkingDir: /Users/<USER>/Documents/GitHub/PerfumerProject
DisplayProgressbar: Compiling Scripts
ExitCode: 0 Duration: 1s154ms
[514/517    1s] Csc Library/Bee/artifacts/1300b0aE.dag/Assembly-CSharp-Editor.dll (+2 others)
[515/517    0s] CopyFiles Library/ScriptAssemblies/Assembly-CSharp-Editor.pdb
[516/517    0s] CopyFiles Library/ScriptAssemblies/Assembly-CSharp-Editor.dll
*** Tundra build success (1.14 seconds), 3 items updated, 517 evaluated
AssetDatabase: script compilation time: 1.746854s
Total cache size 91898556
Total cache size after purge 91898556
Begin MonoManager ReloadAssembly
Symbol file LoadedFromMemory is not a mono symbol file
- Loaded All Assemblies, in  0.476 seconds
The GUID inside 'Assets/FlexReader/Manual.pdf.meta' cannot be extracted by the YAML Parser. Attempting to extract it via string matching instead. Please verify the file does not contain unexpected data.
The GUID inside 'Assets/Resources/Prefabs/UI/Item_Sell.prefab.meta' cannot be extracted by the YAML Parser. Attempting to extract it via string matching instead. Please verify the file does not contain unexpected data.
The GUID inside 'Assets/Scenes/Sell.unity.meta' cannot be extracted by the YAML Parser. Attempting to extract it via string matching instead. Please verify the file does not contain unexpected data.
The GUID inside 'Assets/StreamingAssets/PerfumerData.xlsx.meta' cannot be extracted by the YAML Parser. Attempting to extract it via string matching instead. Please verify the file does not contain unexpected data.
Refreshing native plugins compatible for Editor in 1.19 ms, found 2 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
The GUID inside 'Assets/FlexReader/Manual.pdf.meta' cannot be extracted by the YAML Parser. Attempting to extract it via string matching instead. Please verify the file does not contain unexpected data.
The GUID inside 'Assets/Resources/Prefabs/UI/Item_Sell.prefab.meta' cannot be extracted by the YAML Parser. Attempting to extract it via string matching instead. Please verify the file does not contain unexpected data.
The GUID inside 'Assets/Scenes/Sell.unity.meta' cannot be extracted by the YAML Parser. Attempting to extract it via string matching instead. Please verify the file does not contain unexpected data.
The GUID inside 'Assets/StreamingAssets/PerfumerData.xlsx.meta' cannot be extracted by the YAML Parser. Attempting to extract it via string matching instead. Please verify the file does not contain unexpected data.
Start importing Assets/Editor/CommandLineBuild.cs using Guid(ed8e12a81a8ab4f7bb3af5e0a9e93901) Importer(-1,00000000000000000000000000000000) [PhysX] Initialized MultithreadedTaskDispatcher with 8 workers.
 -> (artifact id: '07b4efdceb556ab17cb8ad1af5afb027') in 0.005160 seconds
Refreshing native plugins compatible for Editor in 1.19 ms, found 2 plugins.
Native extension for LinuxStandalone target not found
Native extension for WindowsStandalone target not found
Native extension for AppleTV target not found
Native extension for VisionOS target not found
Native extension for iOS target not found
Native extension for Android target not found
Native extension for OSXStandalone target not found
Native extension for WebGL target not found
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  0.419 seconds
Domain Reload Profiling: 895ms
	BeginReloadAssembly (84ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (3ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (23ms)
	RebuildCommonClasses (21ms)
	RebuildNativeTypeToScriptingClass (7ms)
	initialDomainReloadingComplete (15ms)
	LoadAllAssembliesAndSetupDomain (348ms)
		LoadAssemblies (229ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (152ms)
			TypeCache.Refresh (132ms)
				TypeCache.ScanAssembly (117ms)
			ScanForSourceGeneratedMonoScriptInfo (10ms)
			ResolveRequiredComponents (9ms)
	FinalizeReload (419ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (286ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (34ms)
			SetLoadedEditorAssemblies (2ms)
			RefreshPlugins (0ms)
			BeforeProcessingInitializeOnLoad (35ms)
			ProcessInitializeOnLoadAttributes (203ms)
			ProcessInitializeOnLoadMethodAttributes (12ms)
			AfterProcessingInitializeOnLoad (1ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (2ms)
The GUID inside 'Assets/FlexReader/Manual.pdf.meta' cannot be extracted by the YAML Parser. Attempting to extract it via string matching instead. Please verify the file does not contain unexpected data.
The GUID inside 'Assets/Resources/Prefabs/UI/Item_Sell.prefab.meta' cannot be extracted by the YAML Parser. Attempting to extract it via string matching instead. Please verify the file does not contain unexpected data.
The GUID inside 'Assets/Scenes/Sell.unity.meta' cannot be extracted by the YAML Parser. Attempting to extract it via string matching instead. Please verify the file does not contain unexpected data.
The GUID inside 'Assets/StreamingAssets/PerfumerData.xlsx.meta' cannot be extracted by the YAML Parser. Attempting to extract it via string matching instead. Please verify the file does not contain unexpected data.
Refreshing native plugins compatible for Editor in 1.16 ms, found 2 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Asset Pipeline Refresh (id=716a28de69fbc474bbf8c1b8f82dd8a2): Total: 4.103 seconds - Initiated by InitialRefreshV2(ForceSynchronousImport)
	Summary:
		Imports: total=1 (actual=1, local cache=0, cache server=0)
		Asset DB Process Time: managed=1 ms, native=1368 ms
		Asset DB Callback time: managed=29 ms, native=4 ms
		Scripting: domain reloads=1, domain reload time=928 ms, compile time=1748 ms, other=21 ms
		Project Asset Count: scripts=5485, non-scripts=1510
		Asset File Changes: new=0, changed=1, moved=0, deleted=0
		Scan Filter Count: 1
	InvokeBeforeRefreshCallbacks: 0.122ms
	ApplyChangesToAssetFolders: 0.038ms
	Scan: 256.273ms
	OnSourceAssetsModified: 1.459ms
	GetAllGuidsForCategorization: 0.442ms
	CategorizeAssets: 110.644ms
	ImportOutOfDateAssets: 448.667ms (-1321.181ms without children)
		ImportManagerImport: 8.773ms (2.556ms without children)
			ImportInProcess: 6.201ms
			UpdateCategorizedAssets: 0.015ms
		CompileScripts: 1747.915ms
		ReloadNativeAssets: 0.004ms
		UnloadImportedAssets: 10.941ms
		ReloadImportedAssets: 0.096ms
		EnsureUptoDateAssetsAreRegisteredWithGuidPM: 1.426ms
		InitializingProgressBar: 0.000ms
		OnDemandSchedulerStart: 0.693ms
	PostProcessAllAssets: 30.285ms
	Hotreload: 1.995ms
	GatherAllCurrentPrimaryArtifactRevisions: 0.000ms
	UnloadStreamsBegin: 0.334ms
	PersistCurrentRevisions: 0.266ms
	UnloadStreamsEnd: 0.029ms
	GenerateScriptTypeHashes: 1.883ms
	Untracked: 3252.146ms
Application.AssetDatabase Initial Refresh End
Launching external process: /Applications/Unity/Hub/Editor/2022.3.62f1/Unity.app/Contents/Tools/UnityShaderCompiler
Launched and connected shader compiler UnityShaderCompiler after 0.05 seconds
Scanning for USB devices : 0.034ms
Initializing Unity extensions:
[Licensing::Client] Successfully resolved entitlement details
[Licensing::Module] Serial number assigned to: "F4-2R2W-WK36-W2FA-CS3P-XXXX"
[Licensing::Module] Serial number assigned to: "1375833638986-UnityPersXXXX"
[MODES] ModeService[none].Initialize
[MODES] ModeService[none].LoadModes
[MODES] Loading mode Default (0) for mode-current-id-香域
2025-06-25 22:39:47.856 Unity[72054:965230] NSEventModifierFlagFunction specified to -setKeyEquivalentModifierMask: for item <NSMenuItem: 0x318a42ab0 Font Asset, ke='Command-F12'>, but is only supported for system-provided menu items; will not be used
[PhysX] Initialized MultithreadedTaskDispatcher with 8 workers.
ProgressiveSceneManager::Cancel()
-- Listing OpenCL platforms(s) --
 * OpenCL platform 0
	PROFILE = FULL_PROFILE
	VERSION = OpenCL 1.2 (Apr 18 2025 21:46:03)
	NAME = Apple
	VENDOR = Apple
-- Listing OpenCL device(s) --
 * OpenCL platform 0, device 0 
	DEVICE_TYPE = 4
	DEVICE_NAME = Apple M1
	DEVICE_VENDOR = Apple
	DEVICE_VERSION = OpenCL 1.2 
	DRIVER_VERSION = 1.2 1.0
	DEVICE_MAX_COMPUTE_UNITS = 8
	DEVICE_MAX_CLOCK_FREQUENCY = 1000
	CL_DEVICE_MAX_CONSTANT_BUFFER_SIZE = 1073741824
	CL_DEVICE_HOST_UNIFIED_MEMORY = true
	CL_DEVICE_MAX_MEM_ALLOC_SIZE = 2147483648
	DEVICE_GLOBAL_MEM_SIZE = 11453251584
-- GPU Progressive lightmapper will use OpenCL device 'Apple M1' from 'Apple'--
   use -OpenCL-PlatformAndDeviceIndices <platformIdx> <deviceIdx> as command line arguments if you want to select a specific adapter for OpenCL.
Unloading 5 Unused Serialized files (Serialized files now loaded: 0)
Unloading 69 unused Assets / (481.2 KB). Loaded Objects now: 5068.
Memory consumption went from 135.4 MB to 134.9 MB.
Total: 3.047250 ms (FindLiveObjects: 0.139917 ms CreateObjectMapping: 0.056750 ms MarkObjects: 2.598750 ms  DeleteObjects: 0.251375 ms)

2025-06-25 22:39:47.898 Unity[72054:965230] NSEventModifierFlagFunction specified to -setKeyEquivalentModifierMask: for item <NSMenuItem: 0x318e5afe0 Font Asset, ke='Command-F12'>, but is only supported for system-provided menu items; will not be used
Starting Android APK build...
UnityEngine.StackTraceUtility:ExtractStackTrace () (at /Users/<USER>/build/output/unity/unity/Runtime/Export/Scripting/StackTrace.cs:37)
UnityEngine.DebugLogHandler:LogFormat (UnityEngine.LogType,UnityEngine.Object,string,object[])
UnityEngine.Logger:Log (UnityEngine.LogType,object)
UnityEngine.Debug:Log (object)
BuildTools.CommandLineBuild:BuildAndroidAPK () (at Assets/Editor/CommandLineBuild.cs:112)
BuildTools.CommandLineBuild:BuildAndroid () (at Assets/Editor/CommandLineBuild.cs:101)

(Filename: Assets/Editor/CommandLineBuild.cs Line: 112)

Version updated to: 1.0.0
UnityEngine.StackTraceUtility:ExtractStackTrace () (at /Users/<USER>/build/output/unity/unity/Runtime/Export/Scripting/StackTrace.cs:37)
UnityEngine.DebugLogHandler:LogFormat (UnityEngine.LogType,UnityEngine.Object,string,object[])
UnityEngine.Logger:Log (UnityEngine.LogType,object)
UnityEngine.Debug:Log (object)
BuildTools.CommandLineBuild:UpdateVersionInfo (string) (at Assets/Editor/CommandLineBuild.cs:344)
BuildTools.CommandLineBuild:BuildAndroidAPK () (at Assets/Editor/CommandLineBuild.cs:132)
BuildTools.CommandLineBuild:BuildAndroid () (at Assets/Editor/CommandLineBuild.cs:101)

(Filename: Assets/Editor/CommandLineBuild.cs Line: 344)

Building APK to: /Users/<USER>/Documents/GitHub/PerfumerProject/Assets/../Builds/android/香域.apk
UnityEngine.StackTraceUtility:ExtractStackTrace () (at /Users/<USER>/build/output/unity/unity/Runtime/Export/Scripting/StackTrace.cs:37)
UnityEngine.DebugLogHandler:LogFormat (UnityEngine.LogType,UnityEngine.Object,string,object[])
UnityEngine.Logger:Log (UnityEngine.LogType,object)
UnityEngine.Debug:Log (object)
BuildTools.CommandLineBuild:BuildAndroidAPK () (at Assets/Editor/CommandLineBuild.cs:151)
BuildTools.CommandLineBuild:BuildAndroid () (at Assets/Editor/CommandLineBuild.cs:101)

(Filename: Assets/Editor/CommandLineBuild.cs Line: 151)

Scenes: Assets/Scenes/Main.unity, Assets/Scenes/Tarot.unity, Assets/Scenes/Level.unity
UnityEngine.StackTraceUtility:ExtractStackTrace () (at /Users/<USER>/build/output/unity/unity/Runtime/Export/Scripting/StackTrace.cs:37)
UnityEngine.DebugLogHandler:LogFormat (UnityEngine.LogType,UnityEngine.Object,string,object[])
UnityEngine.Logger:Log (UnityEngine.LogType,object)
UnityEngine.Debug:Log (object)
BuildTools.CommandLineBuild:BuildAndroidAPK () (at Assets/Editor/CommandLineBuild.cs:152)
BuildTools.CommandLineBuild:BuildAndroid () (at Assets/Editor/CommandLineBuild.cs:101)

(Filename: Assets/Editor/CommandLineBuild.cs Line: 152)

DisplayProgressbar: Checking prerequisites
PlayerSettings->Active Input Handling is set to Both, this is unsupported on Android and might cause issues with input and application performance. Please choose only one active input handling.
UnityEngine.StackTraceUtility:ExtractStackTrace () (at /Users/<USER>/build/output/unity/unity/Runtime/Export/Scripting/StackTrace.cs:37)
UnityEngine.DebugLogHandler:LogFormat (UnityEngine.LogType,UnityEngine.Object,string,object[])
UnityEngine.Logger:Log (UnityEngine.LogType,object)
UnityEngine.Debug:LogWarning (object)
UnityEditor.Android.PostProcessor.Tasks.CheckPrerequisites:ValidateInputHandling () (at /home/<USER>/build/output/unity/unity/PlatformDependent/AndroidPlayer/Editor/Managed/PostProcessor/Tasks/CheckPrerequisites.cs:122)
UnityEditor.Android.PostProcessor.Tasks.CheckPrerequisites:Execute (UnityEditor.Android.PostProcessor.PostProcessorContext) (at /home/<USER>/build/output/unity/unity/PlatformDependent/AndroidPlayer/Editor/Managed/PostProcessor/Tasks/CheckPrerequisites.cs:55)
UnityEditor.Android.PostProcessor.PostProcessRunner:RunAllTasks (UnityEditor.Android.PostProcessor.PostProcessorContext) (at /home/<USER>/build/output/unity/unity/PlatformDependent/AndroidPlayer/Editor/Managed/PostProcessor/AndroidPostProcessor.cs:112)
UnityEditor.Android.PostProcessAndroidPlayer:PrepareForBuild (UnityEditor.BuildPlayerOptions) (at /home/<USER>/build/output/unity/unity/PlatformDependent/AndroidPlayer/Editor/Managed/PostProcessAndroidPlayer.cs:58)
UnityEditor.Android.AndroidBuildPostprocessor:PrepareForBuild (UnityEditor.BuildPlayerOptions) (at /home/<USER>/build/output/unity/unity/PlatformDependent/AndroidPlayer/Editor/Managed/ExtensionModule.cs:1004)
UnityEditor.PostprocessBuildPlayer:PrepareForBuild (UnityEditor.BuildPlayerOptions) (at /Users/<USER>/build/output/unity/unity/Editor/Mono/BuildPipeline/PostprocessBuildPlayer.cs:162)
UnityEditor.BuildPipeline:BuildPlayerInternal (string[],string,string,UnityEditor.BuildTargetGroup,UnityEditor.BuildTarget,int,UnityEditor.BuildOptions,string[]) (at /Users/<USER>/build/output/unity/unity/Editor/Mono/BuildPipeline.bindings.cs:531)
UnityEditor.BuildPipeline:BuildPlayer (string[],string,string,UnityEditor.BuildTargetGroup,UnityEditor.BuildTarget,int,UnityEditor.BuildOptions,string[]) (at /Users/<USER>/build/output/unity/unity/Editor/Mono/BuildPipeline.bindings.cs:396)
UnityEditor.BuildPipeline:BuildPlayer (UnityEditor.BuildPlayerOptions) (at /Users/<USER>/build/output/unity/unity/Editor/Mono/BuildPipeline.bindings.cs:342)
BuildTools.CommandLineBuild:BuildAndroidAPK () (at Assets/Editor/CommandLineBuild.cs:170)
BuildTools.CommandLineBuild:BuildAndroid () (at Assets/Editor/CommandLineBuild.cs:101)

(Filename: /home/<USER>/build/output/unity/unity/PlatformDependent/AndroidPlayer/Editor/Managed/PostProcessor/Tasks/CheckPrerequisites.cs Line: 122)

Android PostProcess task "Checking prerequisites" took 9.6186 ms
DisplayProgressbar: Detect Java Development Kit (JDK)
Android PostProcess task "Detect Java Development Kit (JDK)" took 340.4879 ms
DisplayProgressbar: Detecting Android SDK
AndroidSDKTools:

	root          : /Applications/Unity/Hub/Editor/2022.3.62f1/PlaybackEngines/AndroidPlayer/SDK
	cmdline-tools : /Applications/Unity/Hub/Editor/2022.3.62f1/PlaybackEngines/AndroidPlayer/SDK/cmdline-tools/6.0
	platform-tools: /Applications/Unity/Hub/Editor/2022.3.62f1/PlaybackEngines/AndroidPlayer/SDK/platform-tools
	build-tools   : /Applications/Unity/Hub/Editor/2022.3.62f1/PlaybackEngines/AndroidPlayer/SDK/build-tools/34.0.0

	adb           : /Applications/Unity/Hub/Editor/2022.3.62f1/PlaybackEngines/AndroidPlayer/SDK/platform-tools/adb

Android PostProcess task "Detecting Android SDK" took 5326.5488 ms
DisplayProgressbar: Detect Android NDK
Android PostProcess task "Detect Android NDK" took 1.0088 ms
Android PostProcess task "Trying to find a suitable Android device" took 0.2784 ms
DisplayProgressbar: Checking Graphics API compatibility
Android PostProcess task "Checking Graphics API compatibility" took 0.4682 ms
DisplayProgressbar: Check resources
Android PostProcess task "Check resources" took 0.1281 ms
DisplayProgressbar: Check Android Player Settings
Android PostProcess task "Check Android Player Settings" took 0.0766 ms
[40m[32minfo[39m[22m[49m: Microsoft.AspNetCore.Hosting.Diagnostics[1]
      Request starting HTTP/2 POST http://ilpp/UnityILPP.PostProcessing/Ping application/grpc -
[40m[32minfo[39m[22m[49m: Microsoft.AspNetCore.Routing.EndpointMiddleware[0]
      Executing endpoint 'gRPC - /UnityILPP.PostProcessing/Ping'
[40m[32minfo[39m[22m[49m: Microsoft.AspNetCore.Routing.EndpointMiddleware[1]
      Executed endpoint 'gRPC - /UnityILPP.PostProcessing/Ping'
[40m[32minfo[39m[22m[49m: Microsoft.AspNetCore.Hosting.Diagnostics[2]
      Request finished HTTP/2 POST http://ilpp/UnityILPP.PostProcessing/Ping application/grpc - - 200 - application/grpc 1.1525ms
Starting: /Applications/Unity/Hub/Editor/2022.3.62f1/Unity.app/Contents/bee_backend --ipc --defer-dag-verification --dagfile="Library/Bee/1300b0aESkipCompile.dag" --continue-on-failure --profile="Library/Bee/backend1.traceevents" ScriptAssembliesAndTypeDB
WorkingDir: /Users/<USER>/Documents/GitHub/PerfumerProject
DisplayProgressbar: Compiling Scripts
ExitCode: 4 Duration: 0s589ms
Rebuilding DAG because FileSignature timestamp changed: Library/Bee/1300b0aESkipCompile-inputdata.json
[2/3        0s] BuildPlayerDataGenerator Library/BuildPlayerData/Editor/TypeDb-All.json
Field 'System.Numerics.Vector2 SixLabors.Fonts.GlyphInstance/ControlPointCollection::SecondControlPoint' from '/Users/<USER>/Documents/GitHub/PerfumerProject/Library/PackageCache/com.unity.testtools.codecoverage@1.2.6/lib/ReportGenerator/ReportGeneratorMerged.dll', exception Failed to resolve System.Numerics.Vector2
Field 'System.Numerics.Vector2 SixLabors.Fonts.GlyphInstance/ControlPointCollection::ThirdControlPoint' from '/Users/<USER>/Documents/GitHub/PerfumerProject/Library/PackageCache/com.unity.testtools.codecoverage@1.2.6/lib/ReportGenerator/ReportGeneratorMerged.dll', exception Failed to resolve System.Numerics.Vector2
Field 'System.Numerics.Vector4 SixLabors.ImageSharp.ComplexVector4::Real' from '/Users/<USER>/Documents/GitHub/PerfumerProject/Library/PackageCache/com.unity.testtools.codecoverage@1.2.6/lib/ReportGenerator/ReportGeneratorMerged.dll', exception Failed to resolve System.Numerics.Vector4
Field 'System.Numerics.Vector4 SixLabors.ImageSharp.ComplexVector4::Imaginary' from '/Users/<USER>/Documents/GitHub/PerfumerProject/Library/PackageCache/com.unity.testtools.codecoverage@1.2.6/lib/ReportGenerator/ReportGeneratorMerged.dll', exception Failed to resolve System.Numerics.Vector4
Field 'System.Numerics.Vector4 SixLabors.ImageSharp.Formats.Jpeg.Components.Block8x8F::V0L' from '/Users/<USER>/Documents/GitHub/PerfumerProject/Library/PackageCache/com.unity.testtools.codecoverage@1.2.6/lib/ReportGenerator/ReportGeneratorMerged.dll', exception Failed to resolve System.Numerics.Vector4
Field 'System.Numerics.Vector4 SixLabors.ImageSharp.Formats.Jpeg.Components.Block8x8F::V0R' from '/Users/<USER>/Documents/GitHub/PerfumerProject/Library/PackageCache/com.unity.testtools.codecoverage@1.2.6/lib/ReportGenerator/ReportGeneratorMerged.dll', exception Failed to resolve System.Numerics.Vector4
Field 'System.Numerics.Vector4 SixLabors.ImageSharp.Formats.Jpeg.Components.Block8x8F::V1L' from '/Users/<USER>/Documents/GitHub/PerfumerProject/Library/PackageCache/com.unity.testtools.codecoverage@1.2.6/lib/ReportGenerator/ReportGeneratorMerged.dll', exception Failed to resolve System.Numerics.Vector4
Field 'System.Numerics.Vector4 SixLabors.ImageSharp.Formats.Jpeg.Components.Block8x8F::V1R' from '/Users/<USER>/Documents/GitHub/PerfumerProject/Library/PackageCache/com.unity.testtools.codecoverage@1.2.6/lib/ReportGenerator/ReportGeneratorMerged.dll', exception Failed to resolve System.Numerics.Vector4
Field 'System.Numerics.Vector4 SixLabors.ImageSharp.Formats.Jpeg.Components.Block8x8F::V2L' from '/Users/<USER>/Documents/GitHub/PerfumerProject/Library/PackageCache/com.unity.testtools.codecoverage@1.2.6/lib/ReportGenerator/ReportGeneratorMerged.dll', exception Failed to resolve System.Numerics.Vector4
Field 'System.Numerics.Vector4 SixLabors.ImageSharp.Formats.Jpeg.Components.Block8x8F::V2R' from '/Users/<USER>/Documents/GitHub/PerfumerProject/Library/PackageCache/com.unity.testtools.codecoverage@1.2.6/lib/ReportGenerator/ReportGeneratorMerged.dll', exception Failed to resolve System.Numerics.Vector4
Field 'System.Numerics.Vector4 SixLabors.ImageSharp.Formats.Jpeg.Components.Block8x8F::V3L' from '/Users/<USER>/Documents/GitHub/PerfumerProject/Library/PackageCache/com.unity.testtools.codecoverage@1.2.6/lib/ReportGenerator/ReportGeneratorMerged.dll', exception Failed to resolve System.Numerics.Vector4
Field 'System.Numerics.Vector4 SixLabors.ImageSharp.Formats.Jpeg.Components.Block8x8F::V3R' from '/Users/<USER>/Documents/GitHub/PerfumerProject/Library/PackageCache/com.unity.testtools.codecoverage@1.2.6/lib/ReportGenerator/ReportGeneratorMerged.dll', exception Failed to resolve System.Numerics.Vector4
Field 'System.Numerics.Vector4 SixLabors.ImageSharp.Formats.Jpeg.Components.Block8x8F::V4L' from '/Users/<USER>/Documents/GitHub/PerfumerProject/Library/PackageCache/com.unity.testtools.codecoverage@1.2.6/lib/ReportGenerator/ReportGeneratorMerged.dll', exception Failed to resolve System.Numerics.Vector4
Field 'System.Numerics.Vector4 SixLabors.ImageSharp.Formats.Jpeg.Components.Block8x8F::V4R' from '/Users/<USER>/Documents/GitHub/PerfumerProject/Library/PackageCache/com.unity.testtools.codecoverage@1.2.6/lib/ReportGenerator/ReportGeneratorMerged.dll', exception Failed to resolve System.Numerics.Vector4
Field 'System.Numerics.Vector4 SixLabors.ImageSharp.Formats.Jpeg.Components.Block8x8F::V5L' from '/Users/<USER>/Documents/GitHub/PerfumerProject/Library/PackageCache/com.unity.testtools.codecoverage@1.2.6/lib/ReportGenerator/ReportGeneratorMerged.dll', exception Failed to resolve System.Numerics.Vector4
Field 'System.Numerics.Vector4 SixLabors.ImageSharp.Formats.Jpeg.Components.Block8x8F::V5R' from '/Users/<USER>/Documents/GitHub/PerfumerProject/Library/PackageCache/com.unity.testtools.codecoverage@1.2.6/lib/ReportGenerator/ReportGeneratorMerged.dll', exception Failed to resolve System.Numerics.Vector4
Field 'System.Numerics.Vector4 SixLabors.ImageSharp.Formats.Jpeg.Components.Block8x8F::V6L' from '/Users/<USER>/Documents/GitHub/PerfumerProject/Library/PackageCache/com.unity.testtools.codecoverage@1.2.6/lib/ReportGenerator/ReportGeneratorMerged.dll', exception Failed to resolve System.Numerics.Vector4
Field 'System.Numerics.Vector4 SixLabors.ImageSharp.Formats.Jpeg.Components.Block8x8F::V6R' from '/Users/<USER>/Documents/GitHub/PerfumerProject/Library/PackageCache/com.unity.testtools.codecoverage@1.2.6/lib/ReportGenerator/ReportGeneratorMerged.dll', exception Failed to resolve System.Numerics.Vector4
Field 'System.Numerics.Vector4 SixLabors.ImageSharp.Formats.Jpeg.Components.Block8x8F::V7L' from '/Users/<USER>/Documents/GitHub/PerfumerProject/Library/PackageCache/com.unity.testtools.codecoverage@1.2.6/lib/ReportGenerator/ReportGeneratorMerged.dll', exception Failed to resolve System.Numerics.Vector4
Field 'System.Numerics.Vector4 SixLabors.ImageSharp.Formats.Jpeg.Components.Block8x8F::V7R' from '/Users/<USER>/Documents/GitHub/PerfumerProject/Library/PackageCache/com.unity.testtools.codecoverage@1.2.6/lib/ReportGenerator/ReportGeneratorMerged.dll', exception Failed to resolve System.Numerics.Vector4
Field 'System.Numerics.Vector4 SixLabors.ImageSharp.Formats.Jpeg.Components.Decoder.ColorConverters.JpegColorConverter/Vector4Octet::V0' from '/Users/<USER>/Documents/GitHub/PerfumerProject/Library/PackageCache/com.unity.testtools.codecoverage@1.2.6/lib/ReportGenerator/ReportGeneratorMerged.dll', exception Failed to resolve System.Numerics.Vector4
Field 'System.Numerics.Vector4 SixLabors.ImageSharp.Formats.Jpeg.Components.Decoder.ColorConverters.JpegColorConverter/Vector4Octet::V1' from '/Users/<USER>/Documents/GitHub/PerfumerProject/Library/PackageCache/com.unity.testtools.codecoverage@1.2.6/lib/ReportGenerator/ReportGeneratorMerged.dll', exception Failed to resolve System.Numerics.Vector4
Field 'System.Numerics.Vector4 SixLabors.ImageSharp.Formats.Jpeg.Components.Decoder.ColorConverters.JpegColorConverter/Vector4Octet::V2' from '/Users/<USER>/Documents/GitHub/PerfumerProject/Library/PackageCache/com.unity.testtools.codecoverage@1.2.6/lib/ReportGenerator/ReportGeneratorMerged.dll', exception Failed to resolve System.Numerics.Vector4
Field 'System.Numerics.Vector4 SixLabors.ImageSharp.Formats.Jpeg.Components.Decoder.ColorConverters.JpegColorConverter/Vector4Octet::V3' from '/Users/<USER>/Documents/GitHub/PerfumerProject/Library/PackageCache/com.unity.testtools.codecoverage@1.2.6/lib/ReportGenerator/ReportGeneratorMerged.dll', exception Failed to resolve System.Numerics.Vector4
Field 'System.Numerics.Vector4 SixLabors.ImageSharp.Formats.Jpeg.Components.Decoder.ColorConverters.JpegColorConverter/Vector4Octet::V4' from '/Users/<USER>/Documents/GitHub/PerfumerProject/Library/PackageCache/com.unity.testtools.codecoverage@1.2.6/lib/ReportGenerator/ReportGeneratorMerged.dll', exception Failed to resolve System.Numerics.Vector4
Field 'System.Numerics.Vector4 SixLabors.ImageSharp.Formats.Jpeg.Components.Decoder.ColorConverters.JpegColorConverter/Vector4Octet::V5' from '/Users/<USER>/Documents/GitHub/PerfumerProject/Library/PackageCache/com.unity.testtools.codecoverage@1.2.6/lib/ReportGenerator/ReportGeneratorMerged.dll', exception Failed to resolve System.Numerics.Vector4
Field 'System.Numerics.Vector4 SixLabors.ImageSharp.Formats.Jpeg.Components.Decoder.ColorConverters.JpegColorConverter/Vector4Octet::V6' from '/Users/<USER>/Documents/GitHub/PerfumerProject/Library/PackageCache/com.unity.testtools.codecoverage@1.2.6/lib/ReportGenerator/ReportGeneratorMerged.dll', exception Failed to resolve System.Numerics.Vector4
Field 'System.Numerics.Vector4 SixLabors.ImageSharp.Formats.Jpeg.Components.Decoder.ColorConverters.JpegColorConverter/Vector4Octet::V7' from '/Users/<USER>/Documents/GitHub/PerfumerProject/Library/PackageCache/com.unity.testtools.codecoverage@1.2.6/lib/ReportGenerator/ReportGeneratorMerged.dll', exception Failed to resolve System.Numerics.Vector4
Field 'System.Numerics.Vector4 SixLabors.ImageSharp.Tuples.Vector4Pair::A' from '/Users/<USER>/Documents/GitHub/PerfumerProject/Library/PackageCache/com.unity.testtools.codecoverage@1.2.6/lib/ReportGenerator/ReportGeneratorMerged.dll', exception Failed to resolve System.Numerics.Vector4
Field 'System.Numerics.Vector4 SixLabors.ImageSharp.Tuples.Vector4Pair::B' from '/Users/<USER>/Documents/GitHub/PerfumerProject/Library/PackageCache/com.unity.testtools.codecoverage@1.2.6/lib/ReportGenerator/ReportGeneratorMerged.dll', exception Failed to resolve System.Numerics.Vector4
*** Tundra requires additional run (0.57 seconds), 1 items updated, 1 evaluated
Starting: /Applications/Unity/Hub/Editor/2022.3.62f1/Unity.app/Contents/Tools/netcorerun/netcorerun "/Applications/Unity/Hub/Editor/2022.3.62f1/Unity.app/Contents/Tools/BuildPipeline/ScriptCompilationBuildProgram.exe" "Library/Bee/1300b0aESkipCompile.dag.json" "Library/Bee/1300b0aESkipCompile-inputdata.json" "Library/Bee/buildprogram0.traceevents"
WorkingDir: /Users/<USER>/Documents/GitHub/PerfumerProject
ExitCode: 0 Duration: 0s220ms
Starting: /Applications/Unity/Hub/Editor/2022.3.62f1/Unity.app/Contents/bee_backend --ipc --defer-dag-verification --dagfile="Library/Bee/1300b0aESkipCompile.dag" --continue-on-failure --dagfilejson="Library/Bee/1300b0aESkipCompile.dag.json" --profile="Library/Bee/backend2.traceevents" ScriptAssembliesAndTypeDB
WorkingDir: /Users/<USER>/Documents/GitHub/PerfumerProject
ExitCode: 0 Duration: 0s24ms
Finished compiling graph: 4 nodes, 5 flattened edges (5 ToBuild, 0 ToUse), maximum node priority 3
*** Tundra build success (0.01 seconds), 0 items updated, 3 evaluated
Total cache size 91898556
Total cache size after purge 91898556
The GUID inside 'Assets/FlexReader/Manual.pdf.meta' cannot be extracted by the YAML Parser. Attempting to extract it via string matching instead. Please verify the file does not contain unexpected data.
UnityEngine.StackTraceUtility:ExtractStackTrace () (at /Users/<USER>/build/output/unity/unity/Runtime/Export/Scripting/StackTrace.cs:37)
UnityEditor.BuildPipeline:BuildPlayerInternal (string[],string,string,UnityEditor.BuildTargetGroup,UnityEditor.BuildTarget,int,UnityEditor.BuildOptions,string[]) (at /Users/<USER>/build/output/unity/unity/Editor/Mono/BuildPipeline.bindings.cs:531)
UnityEditor.BuildPipeline:BuildPlayer (string[],string,string,UnityEditor.BuildTargetGroup,UnityEditor.BuildTarget,int,UnityEditor.BuildOptions,string[]) (at /Users/<USER>/build/output/unity/unity/Editor/Mono/BuildPipeline.bindings.cs:396)
UnityEditor.BuildPipeline:BuildPlayer (UnityEditor.BuildPlayerOptions) (at /Users/<USER>/build/output/unity/unity/Editor/Mono/BuildPipeline.bindings.cs:342)
BuildTools.CommandLineBuild:BuildAndroidAPK () (at Assets/Editor/CommandLineBuild.cs:170)
BuildTools.CommandLineBuild:BuildAndroid () (at Assets/Editor/CommandLineBuild.cs:101)

[/Users/<USER>/build/output/unity/unity/Editor/Mono/BuildPipeline.bindings.cs line 531]

The GUID inside 'Assets/Resources/Prefabs/UI/Item_Sell.prefab.meta' cannot be extracted by the YAML Parser. Attempting to extract it via string matching instead. Please verify the file does not contain unexpected data.
UnityEngine.StackTraceUtility:ExtractStackTrace () (at /Users/<USER>/build/output/unity/unity/Runtime/Export/Scripting/StackTrace.cs:37)
UnityEditor.BuildPipeline:BuildPlayerInternal (string[],string,string,UnityEditor.BuildTargetGroup,UnityEditor.BuildTarget,int,UnityEditor.BuildOptions,string[]) (at /Users/<USER>/build/output/unity/unity/Editor/Mono/BuildPipeline.bindings.cs:531)
UnityEditor.BuildPipeline:BuildPlayer (string[],string,string,UnityEditor.BuildTargetGroup,UnityEditor.BuildTarget,int,UnityEditor.BuildOptions,string[]) (at /Users/<USER>/build/output/unity/unity/Editor/Mono/BuildPipeline.bindings.cs:396)
UnityEditor.BuildPipeline:BuildPlayer (UnityEditor.BuildPlayerOptions) (at /Users/<USER>/build/output/unity/unity/Editor/Mono/BuildPipeline.bindings.cs:342)
BuildTools.CommandLineBuild:BuildAndroidAPK () (at Assets/Editor/CommandLineBuild.cs:170)
BuildTools.CommandLineBuild:BuildAndroid () (at Assets/Editor/CommandLineBuild.cs:101)

[/Users/<USER>/build/output/unity/unity/Editor/Mono/BuildPipeline.bindings.cs line 531]

The GUID inside 'Assets/Scenes/Sell.unity.meta' cannot be extracted by the YAML Parser. Attempting to extract it via string matching instead. Please verify the file does not contain unexpected data.
UnityEngine.StackTraceUtility:ExtractStackTrace () (at /Users/<USER>/build/output/unity/unity/Runtime/Export/Scripting/StackTrace.cs:37)
UnityEditor.BuildPipeline:BuildPlayerInternal (string[],string,string,UnityEditor.BuildTargetGroup,UnityEditor.BuildTarget,int,UnityEditor.BuildOptions,string[]) (at /Users/<USER>/build/output/unity/unity/Editor/Mono/BuildPipeline.bindings.cs:531)
UnityEditor.BuildPipeline:BuildPlayer (string[],string,string,UnityEditor.BuildTargetGroup,UnityEditor.BuildTarget,int,UnityEditor.BuildOptions,string[]) (at /Users/<USER>/build/output/unity/unity/Editor/Mono/BuildPipeline.bindings.cs:396)
UnityEditor.BuildPipeline:BuildPlayer (UnityEditor.BuildPlayerOptions) (at /Users/<USER>/build/output/unity/unity/Editor/Mono/BuildPipeline.bindings.cs:342)
BuildTools.CommandLineBuild:BuildAndroidAPK () (at Assets/Editor/CommandLineBuild.cs:170)
BuildTools.CommandLineBuild:BuildAndroid () (at Assets/Editor/CommandLineBuild.cs:101)

[/Users/<USER>/build/output/unity/unity/Editor/Mono/BuildPipeline.bindings.cs line 531]

The GUID inside 'Assets/StreamingAssets/PerfumerData.xlsx.meta' cannot be extracted by the YAML Parser. Attempting to extract it via string matching instead. Please verify the file does not contain unexpected data.
UnityEngine.StackTraceUtility:ExtractStackTrace () (at /Users/<USER>/build/output/unity/unity/Runtime/Export/Scripting/StackTrace.cs:37)
UnityEditor.BuildPipeline:BuildPlayerInternal (string[],string,string,UnityEditor.BuildTargetGroup,UnityEditor.BuildTarget,int,UnityEditor.BuildOptions,string[]) (at /Users/<USER>/build/output/unity/unity/Editor/Mono/BuildPipeline.bindings.cs:531)
UnityEditor.BuildPipeline:BuildPlayer (string[],string,string,UnityEditor.BuildTargetGroup,UnityEditor.BuildTarget,int,UnityEditor.BuildOptions,string[]) (at /Users/<USER>/build/output/unity/unity/Editor/Mono/BuildPipeline.bindings.cs:396)
UnityEditor.BuildPipeline:BuildPlayer (UnityEditor.BuildPlayerOptions) (at /Users/<USER>/build/output/unity/unity/Editor/Mono/BuildPipeline.bindings.cs:342)
BuildTools.CommandLineBuild:BuildAndroidAPK () (at Assets/Editor/CommandLineBuild.cs:170)
BuildTools.CommandLineBuild:BuildAndroid () (at Assets/Editor/CommandLineBuild.cs:101)

[/Users/<USER>/build/output/unity/unity/Editor/Mono/BuildPipeline.bindings.cs line 531]

Asset Pipeline Refresh (id=f75352062b311496ebe1a1ed4ecc929f): Total: 0.073 seconds - Initiated by RefreshV2(NoUpdateAssetOptions)
[ScriptCompilation] Requested script compilation because: Recompiling scripts for player build.
BuildPlayer: start building target 13
[40m[32minfo[39m[22m[49m: Microsoft.AspNetCore.Hosting.Diagnostics[1]
      Request starting HTTP/2 POST http://ilpp/UnityILPP.PostProcessing/Ping application/grpc -
[40m[32minfo[39m[22m[49m: Microsoft.AspNetCore.Routing.EndpointMiddleware[0]
      Executing endpoint 'gRPC - /UnityILPP.PostProcessing/Ping'
[40m[32minfo[39m[22m[49m: Microsoft.AspNetCore.Routing.EndpointMiddleware[1]
      Executed endpoint 'gRPC - /UnityILPP.PostProcessing/Ping'
[40m[32minfo[39m[22m[49m: Microsoft.AspNetCore.Hosting.Diagnostics[2]
      Request finished HTTP/2 POST http://ilpp/UnityILPP.PostProcessing/Ping application/grpc - - 200 - application/grpc 0.4205ms
Starting: /Applications/Unity/Hub/Editor/2022.3.62f1/Unity.app/Contents/bee_backend --ipc --defer-dag-verification --dagfile="Library/Bee/1300b0aP.dag" --continue-on-failure --profile="Library/Bee/backend1.traceevents" ScriptAssembliesAndTypeDB
WorkingDir: /Users/<USER>/Documents/GitHub/PerfumerProject
Starting: /Applications/Unity/Hub/Editor/2022.3.62f1/Unity.app/Contents/Tools/netcorerun/netcorerun "/Applications/Unity/Hub/Editor/2022.3.62f1/Unity.app/Contents/Tools/BuildPipeline/ScriptCompilationBuildProgram.exe" "Library/Bee/1300b0aP.dag.json" "Library/Bee/1300b0aP-inputdata.json" "Library/Bee/buildprogram0.traceevents"
WorkingDir: /Users/<USER>/Documents/GitHub/PerfumerProject
ExitCode: 4 Duration: 0s86ms
[123/180    0s] CopyFiles Library/Bee/PlayerScriptAssemblies/UnityEngine.TestRunner.dll
[124/180    0s] CopyFiles Library/Bee/PlayerScriptAssemblies/UnityEngine.UI.dll
[125/180    0s] CopyFiles Library/Bee/PlayerScriptAssemblies/UnityEngine.TestRunner.pdb
[126/180    0s] CopyFiles Library/Bee/PlayerScriptAssemblies/UnityEngine.UI.pdb
[134/180    0s] CopyFiles Library/Bee/PlayerScriptAssemblies/Unity.TextMeshPro.pdb
[135/180    0s] CopyFiles Library/Bee/PlayerScriptAssemblies/Unity.TextMeshPro.dll
[136/180    0s] CopyFiles Library/Bee/PlayerScriptAssemblies/NavMeshPlus.dll
[138/180    0s] CopyFiles Library/Bee/PlayerScriptAssemblies/NavMeshPlus.pdb
[140/180    0s] CopyFiles Library/Bee/PlayerScriptAssemblies/Unity.Timeline.pdb
[141/180    0s] CopyFiles Library/Bee/PlayerScriptAssemblies/Unity.Timeline.dll
Rebuilding DAG because FileSignature timestamp changed: Library/Bee/1300b0aP-inputdata.json
*** Tundra requires additional run (0.07 seconds), 10 items updated, 142 evaluated
ExitCode: 0 Duration: 0s274ms
Starting: /Applications/Unity/Hub/Editor/2022.3.62f1/Unity.app/Contents/bee_backend --ipc --defer-dag-verification --dagfile="Library/Bee/1300b0aP.dag" --continue-on-failure --dagfilejson="Library/Bee/1300b0aP.dag.json" --profile="Library/Bee/backend2.traceevents" ScriptAssembliesAndTypeDB
WorkingDir: /Users/<USER>/Documents/GitHub/PerfumerProject
ExitCode: 0 Duration: 0s898ms
Finished compiling graph: 181 nodes, 1448 flattened edges (1306 ToBuild, 26 ToUse), maximum node priority 120
[147/180    0s] CopyFiles Library/Bee/PlayerScriptAssemblies/Unity.InputSystem.pdb
[148/180    0s] CopyFiles Library/Bee/PlayerScriptAssemblies/Unity.InputSystem.dll
[150/180    0s] CopyFiles Library/Bee/PlayerScriptAssemblies/Fungus.dll
[153/180    0s] CopyFiles Library/Bee/PlayerScriptAssemblies/Fungus.pdb
[154/180    0s] CopyFiles Library/Bee/PlayerScriptAssemblies/Unity.InputSystem.ForUI.pdb
[155/180    0s] CopyFiles Library/Bee/PlayerScriptAssemblies/Unity.InputSystem.ForUI.dll
[159/180    0s] CopyFiles Library/Bee/PlayerScriptAssemblies/Unity.VisualScripting.Core.dll
[160/180    0s] CopyFiles Library/Bee/PlayerScriptAssemblies/Unity.VisualScripting.Core.pdb
[164/180    0s] CopyFiles Library/Bee/PlayerScriptAssemblies/Unity.VisualScripting.Flow.dll
[165/180    0s] CopyFiles Library/Bee/PlayerScriptAssemblies/Unity.VisualScripting.Flow.pdb
[169/180    0s] CopyFiles Library/Bee/PlayerScriptAssemblies/Unity.VisualScripting.State.dll
[170/180    0s] CopyFiles Library/Bee/PlayerScriptAssemblies/Unity.VisualScripting.State.pdb
[173/180    0s] CopyFiles Library/Bee/PlayerScriptAssemblies/Assembly-CSharp-firstpass.dll
[174/180    0s] CopyFiles Library/Bee/PlayerScriptAssemblies/Assembly-CSharp-firstpass.pdb
[175/180    0s] Csc Library/Bee/artifacts/1300b0aP.dag/Assembly-CSharp.dll (+2 others)
Assets/Sripts/GameDataManager.cs(11,20): warning CS0108: 'GameDataManager.name' hides inherited member 'Object.name'. Use the new keyword if hiding was intended.
Assets/Sripts/GameDataManager.cs(57,18): warning CS0114: 'GameDataManager.Awake()' hides inherited member 'MonoSingleton<GameDataManager>.Awake()'. To make the current member override that implementation, add the override keyword. Otherwise add the new keyword.
Assets/Script/Tarot/TarotController.cs(110,21): warning CS0219: The variable 'a' is assigned but its value is never used
Assets/Script/Tarot/TarotCardController.cs(30,57): warning CS0067: The event 'TarotCardController.OnCardDetailDisplayed' is never used
Assets/Daterotation/DatePickerGroup.cs(25,31): warning CS0067: The event 'DatePickerGroup._OnDateUpdate' is never used
Assets/Script/Combat/CombatSystem.cs(28,32): warning CS0067: The event 'CombatSystem.OnCombatStart' is never used
Assets/Sparkling_Magic_Dust_VFX/Scripts/MagicDustConstant.cs(19,22): warning CS0414: The field 'MagicDustConstant.dustChange' is assigned but its value is never used
Assets/Sparkling_Magic_Dust_VFX/Scripts/MagicDustConstant.cs(18,22): warning CS0414: The field 'MagicDustConstant.dustActive' is assigned but its value is never used
Assets/Script/Tarot/TarotCardController.cs(12,14): warning CS0414: The field 'TarotCardController.m_isRevealing' is assigned but its value is never used
[176/180    0s] CopyFiles Library/Bee/PlayerScriptAssemblies/Assembly-CSharp.pdb
[177/180    0s] CopyFiles Library/Bee/PlayerScriptAssemblies/Assembly-CSharp.dll
[179/180    0s] BuildPlayerDataGenerator Library/BuildPlayerData/Player/RuntimeInitializeOnLoads.json (+1 other)
*** Tundra build success (0.88 seconds), 18 items updated, 180 evaluated
Assets/Sripts/GameDataManager.cs(11,20): warning CS0108: 'GameDataManager.name' hides inherited member 'Object.name'. Use the new keyword if hiding was intended.
Assets/Sripts/GameDataManager.cs(57,18): warning CS0114: 'GameDataManager.Awake()' hides inherited member 'MonoSingleton<GameDataManager>.Awake()'. To make the current member override that implementation, add the override keyword. Otherwise add the new keyword.
Assets/Script/Tarot/TarotController.cs(110,21): warning CS0219: The variable 'a' is assigned but its value is never used
Assets/Script/Tarot/TarotCardController.cs(30,57): warning CS0067: The event 'TarotCardController.OnCardDetailDisplayed' is never used
Assets/Daterotation/DatePickerGroup.cs(25,31): warning CS0067: The event 'DatePickerGroup._OnDateUpdate' is never used
Assets/Script/Combat/CombatSystem.cs(28,32): warning CS0067: The event 'CombatSystem.OnCombatStart' is never used
Assets/Sparkling_Magic_Dust_VFX/Scripts/MagicDustConstant.cs(19,22): warning CS0414: The field 'MagicDustConstant.dustChange' is assigned but its value is never used
Assets/Sparkling_Magic_Dust_VFX/Scripts/MagicDustConstant.cs(18,22): warning CS0414: The field 'MagicDustConstant.dustActive' is assigned but its value is never used
Assets/Script/Tarot/TarotCardController.cs(12,14): warning CS0414: The field 'TarotCardController.m_isRevealing' is assigned but its value is never used
Total cache size 91898556
Total cache size after purge 91898556
Not rebuilding Data files -- no changes
2025-06-25 22:39:56.772 Unity[72054:965230] NSEventModifierFlagFunction specified to -setKeyEquivalentModifierMask: for item <NSMenuItem: 0x318e5f460 Font Asset, ke='Command-F12'>, but is only supported for system-provided menu items; will not be used
The GUID inside 'Assets/FlexReader/Manual.pdf.meta' cannot be extracted by the YAML Parser. Attempting to extract it via string matching instead. Please verify the file does not contain unexpected data.
UnityEngine.StackTraceUtility:ExtractStackTrace () (at /Users/<USER>/build/output/unity/unity/Runtime/Export/Scripting/StackTrace.cs:37)
UnityEditor.BuildPipeline:BuildPlayerInternal (string[],string,string,UnityEditor.BuildTargetGroup,UnityEditor.BuildTarget,int,UnityEditor.BuildOptions,string[]) (at /Users/<USER>/build/output/unity/unity/Editor/Mono/BuildPipeline.bindings.cs:531)
UnityEditor.BuildPipeline:BuildPlayer (string[],string,string,UnityEditor.BuildTargetGroup,UnityEditor.BuildTarget,int,UnityEditor.BuildOptions,string[]) (at /Users/<USER>/build/output/unity/unity/Editor/Mono/BuildPipeline.bindings.cs:396)
UnityEditor.BuildPipeline:BuildPlayer (UnityEditor.BuildPlayerOptions) (at /Users/<USER>/build/output/unity/unity/Editor/Mono/BuildPipeline.bindings.cs:342)
BuildTools.CommandLineBuild:BuildAndroidAPK () (at Assets/Editor/CommandLineBuild.cs:170)
BuildTools.CommandLineBuild:BuildAndroid () (at Assets/Editor/CommandLineBuild.cs:101)

[/Users/<USER>/build/output/unity/unity/Editor/Mono/BuildPipeline.bindings.cs line 531]

The GUID inside 'Assets/Resources/Prefabs/UI/Item_Sell.prefab.meta' cannot be extracted by the YAML Parser. Attempting to extract it via string matching instead. Please verify the file does not contain unexpected data.
UnityEngine.StackTraceUtility:ExtractStackTrace () (at /Users/<USER>/build/output/unity/unity/Runtime/Export/Scripting/StackTrace.cs:37)
UnityEditor.BuildPipeline:BuildPlayerInternal (string[],string,string,UnityEditor.BuildTargetGroup,UnityEditor.BuildTarget,int,UnityEditor.BuildOptions,string[]) (at /Users/<USER>/build/output/unity/unity/Editor/Mono/BuildPipeline.bindings.cs:531)
UnityEditor.BuildPipeline:BuildPlayer (string[],string,string,UnityEditor.BuildTargetGroup,UnityEditor.BuildTarget,int,UnityEditor.BuildOptions,string[]) (at /Users/<USER>/build/output/unity/unity/Editor/Mono/BuildPipeline.bindings.cs:396)
UnityEditor.BuildPipeline:BuildPlayer (UnityEditor.BuildPlayerOptions) (at /Users/<USER>/build/output/unity/unity/Editor/Mono/BuildPipeline.bindings.cs:342)
BuildTools.CommandLineBuild:BuildAndroidAPK () (at Assets/Editor/CommandLineBuild.cs:170)
BuildTools.CommandLineBuild:BuildAndroid () (at Assets/Editor/CommandLineBuild.cs:101)

[/Users/<USER>/build/output/unity/unity/Editor/Mono/BuildPipeline.bindings.cs line 531]

The GUID inside 'Assets/Scenes/Sell.unity.meta' cannot be extracted by the YAML Parser. Attempting to extract it via string matching instead. Please verify the file does not contain unexpected data.
UnityEngine.StackTraceUtility:ExtractStackTrace () (at /Users/<USER>/build/output/unity/unity/Runtime/Export/Scripting/StackTrace.cs:37)
UnityEditor.BuildPipeline:BuildPlayerInternal (string[],string,string,UnityEditor.BuildTargetGroup,UnityEditor.BuildTarget,int,UnityEditor.BuildOptions,string[]) (at /Users/<USER>/build/output/unity/unity/Editor/Mono/BuildPipeline.bindings.cs:531)
UnityEditor.BuildPipeline:BuildPlayer (string[],string,string,UnityEditor.BuildTargetGroup,UnityEditor.BuildTarget,int,UnityEditor.BuildOptions,string[]) (at /Users/<USER>/build/output/unity/unity/Editor/Mono/BuildPipeline.bindings.cs:396)
UnityEditor.BuildPipeline:BuildPlayer (UnityEditor.BuildPlayerOptions) (at /Users/<USER>/build/output/unity/unity/Editor/Mono/BuildPipeline.bindings.cs:342)
BuildTools.CommandLineBuild:BuildAndroidAPK () (at Assets/Editor/CommandLineBuild.cs:170)
BuildTools.CommandLineBuild:BuildAndroid () (at Assets/Editor/CommandLineBuild.cs:101)

[/Users/<USER>/build/output/unity/unity/Editor/Mono/BuildPipeline.bindings.cs line 531]

The GUID inside 'Assets/StreamingAssets/PerfumerData.xlsx.meta' cannot be extracted by the YAML Parser. Attempting to extract it via string matching instead. Please verify the file does not contain unexpected data.
UnityEngine.StackTraceUtility:ExtractStackTrace () (at /Users/<USER>/build/output/unity/unity/Runtime/Export/Scripting/StackTrace.cs:37)
UnityEditor.BuildPipeline:BuildPlayerInternal (string[],string,string,UnityEditor.BuildTargetGroup,UnityEditor.BuildTarget,int,UnityEditor.BuildOptions,string[]) (at /Users/<USER>/build/output/unity/unity/Editor/Mono/BuildPipeline.bindings.cs:531)
UnityEditor.BuildPipeline:BuildPlayer (string[],string,string,UnityEditor.BuildTargetGroup,UnityEditor.BuildTarget,int,UnityEditor.BuildOptions,string[]) (at /Users/<USER>/build/output/unity/unity/Editor/Mono/BuildPipeline.bindings.cs:396)
UnityEditor.BuildPipeline:BuildPlayer (UnityEditor.BuildPlayerOptions) (at /Users/<USER>/build/output/unity/unity/Editor/Mono/BuildPipeline.bindings.cs:342)
BuildTools.CommandLineBuild:BuildAndroidAPK () (at Assets/Editor/CommandLineBuild.cs:170)
BuildTools.CommandLineBuild:BuildAndroid () (at Assets/Editor/CommandLineBuild.cs:101)

[/Users/<USER>/build/output/unity/unity/Editor/Mono/BuildPipeline.bindings.cs line 531]

Asset Pipeline Refresh (id=1840961e8fe5a4a1dba6a541887156d8): Total: 0.103 seconds - Initiated by RefreshV2(NoUpdateAssetOptions)


Mono dependencies included in the build
Dependency assembly - System.Runtime.Extensions.dll
Dependency assembly - System.Diagnostics.Tracing.dll
Dependency assembly - System.IO.FileSystem.Primitives.dll
Dependency assembly - System.Transactions.dll
Dependency assembly - System.Web.Services.dll
Dependency assembly - netstandard.dll
Dependency assembly - System.Security.Cryptography.Encryption.ECDiffieHellman.dll
Dependency assembly - Mono.Data.Tds.dll
Dependency assembly - System.Runtime.Serialization.Json.dll
Dependency assembly - System.Xml.ReaderWriter.dll
Dependency assembly - System.IO.IsolatedStorage.dll
Dependency assembly - Assembly-CSharp-firstpass.dll
Dependency assembly - System.EnterpriseServices.dll
Dependency assembly - System.Runtime.Numerics.dll
Dependency assembly - System.dll
Dependency assembly - System.Security.Cryptography.Algorithms.dll
Dependency assembly - System.Drawing.Design.dll
Dependency assembly - System.Linq.Parallel.dll
Dependency assembly - System.Runtime.Caching.dll
Dependency assembly - System.Xml.XmlDocument.dll
Dependency assembly - System.Collections.Concurrent.dll
Dependency assembly - System.Security.dll
Dependency assembly - System.Core.dll
Dependency assembly - System.Runtime.CompilerServices.VisualC.dll
Dependency assembly - System.Security.Principal.Windows.dll
Dependency assembly - Microsoft.Win32.Registry.dll
Dependency assembly - System.Reflection.Context.dll
Dependency assembly - System.ServiceModel.Primitives.dll
Dependency assembly - System.Drawing.Primitives.dll
Dependency assembly - Unity.Timeline.dll
Dependency assembly - System.Data.Services.dll
Dependency assembly - System.Drawing.dll
Dependency assembly - System.Net.Mail.dll
Dependency assembly - System.Text.Encoding.CodePages.dll
Dependency assembly - System.DirectoryServices.Protocols.dll
Dependency assembly - System.Web.Extensions.Design.dll
Dependency assembly - System.Security.Cryptography.Cng.dll
Dependency assembly - System.ComponentModel.TypeConverter.dll
Dependency assembly - System.ServiceModel.dll
Dependency assembly - System.Runtime.Handles.dll
Dependency assembly - System.IO.FileSystem.dll
Dependency assembly - System.ServiceModel.Routing.dll
Dependency assembly - System.Reflection.Primitives.dll
Dependency assembly - System.Threading.Thread.dll
Dependency assembly - System.Xml.XPath.XmlDocument.dll
Dependency assembly - System.Net.Utilities.dll
Dependency assembly - System.Net.dll
Dependency assembly - System.Management.dll
Dependency assembly - System.IO.FileSystem.DriveInfo.dll
Dependency assembly - System.ComponentModel.DataAnnotations.dll
Dependency assembly - DemiLib.dll
Dependency assembly - System.Xml.Linq.dll
Dependency assembly - System.Messaging.dll
Dependency assembly - UnityEngine.UI.dll
Dependency assembly - System.Diagnostics.StackTrace.dll
Dependency assembly - System.Net.Sockets.dll
Dependency assembly - System.Threading.Tasks.Parallel.dll
Dependency assembly - System.Diagnostics.Contracts.dll
Dependency assembly - System.Resources.ResourceManager.dll
Dependency assembly - Fungus.dll
Dependency assembly - ICSharpCode.SharpZipLib.dll
Dependency assembly - Microsoft.Win32.Primitives.dll
Dependency assembly - System.IO.Compression.ZipFile.dll
Dependency assembly - System.ServiceProcess.ServiceController.dll
Dependency assembly - System.Data.dll
Dependency assembly - System.Resources.Reader.dll
Dependency assembly - System.Web.Extensions.dll
Dependency assembly - System.Data.Services.Client.dll
Dependency assembly - System.Net.Http.WebRequest.dll
Dependency assembly - Assembly-CSharp.dll
Dependency assembly - System.Data.OracleClient.dll
Dependency assembly - System.Windows.Forms.DataVisualization.dll
Dependency assembly - System.Dynamic.Runtime.dll
Dependency assembly - System.Xml.XmlSerializer.dll
Dependency assembly - Unity.VisualScripting.State.dll
Dependency assembly - System.Diagnostics.TraceEvent.dll
Dependency assembly - System.Linq.dll
Dependency assembly - System.IO.MemoryMappedFiles.dll
Dependency assembly - System.Text.RegularExpressions.dll
Dependency assembly - System.Design.dll
Dependency assembly - System.Configuration.Install.dll
Dependency assembly - System.ServiceModel.Security.dll
Dependency assembly - System.Numerics.dll
Dependency assembly - DOTweenPro.dll
Dependency assembly - System.Globalization.dll
Dependency assembly - System.ServiceProcess.dll
Dependency assembly - System.ServiceModel.Internals.dll
Dependency assembly - System.Threading.Tasks.dll
Dependency assembly - System.Diagnostics.TraceSource.dll
Dependency assembly - System.Xml.XPath.dll
Dependency assembly - System.Security.Principal.dll
Dependency assembly - System.ComponentModel.Composition.dll
Dependency assembly - System.Threading.ThreadPool.dll
Dependency assembly - System.Reflection.Emit.ILGeneration.dll
Dependency assembly - System.IO.dll
Dependency assembly - Unity.TextMeshPro.dll
Dependency assembly - System.ServiceModel.Activation.dll
Dependency assembly - System.Net.NetworkInformation.dll
Dependency assembly - System.Web.dll
Dependency assembly - Accessibility.dll
Dependency assembly - System.Web.ApplicationServices.dll
Dependency assembly - System.Security.Cryptography.DeriveBytes.dll
Dependency assembly - System.Memory.dll
Dependency assembly - System.Runtime.Loader.dll
Dependency assembly - System.Security.Cryptography.Pkcs.dll
Dependency assembly - System.Web.DynamicData.dll
Dependency assembly - Unity.InputSystem.ForUI.dll
Dependency assembly - System.Diagnostics.Tools.dll
Dependency assembly - mscorlib.dll
Dependency assembly - System.ComponentModel.EventBasedAsync.dll
Dependency assembly - System.Security.Cryptography.X509Certificates.dll
Dependency assembly - System.Resources.ReaderWriter.dll
Dependency assembly - System.Runtime.Serialization.dll
Dependency assembly - System.Net.Primitives.dll
Dependency assembly - System.Security.Cryptography.OpenSsl.dll
Dependency assembly - System.DirectoryServices.dll
Dependency assembly - Unity.VisualScripting.Flow.dll
Dependency assembly - System.Xml.Serialization.dll
Dependency assembly - System.Xml.XDocument.dll
Dependency assembly - System.ComponentModel.dll
Dependency assembly - System.Diagnostics.Debug.dll
Dependency assembly - System.ServiceModel.NetTcp.dll
Dependency assembly - System.Runtime.InteropServices.WindowsRuntime.dll
Dependency assembly - System.Security.Cryptography.Encryption.Aes.dll
Dependency assembly - System.Security.Cryptography.Hashing.dll
Dependency assembly - System.Xml.XPath.XDocument.dll
Dependency assembly - System.Runtime.DurableInstancing.dll
Dependency assembly - System.Runtime.Remoting.dll
Dependency assembly - System.Threading.Timer.dll
Dependency assembly - System.Linq.Expressions.dll
Dependency assembly - System.Json.dll
Dependency assembly - System.Text.Encoding.Extensions.dll
Dependency assembly - System.Diagnostics.TextWriterTraceListener.dll
Dependency assembly - System.Runtime.Serialization.Primitives.dll
Dependency assembly - System.Data.Linq.dll
Dependency assembly - System.Security.Cryptography.RSA.dll
Dependency assembly - System.Numerics.Vectors.dll
Dependency assembly - System.ServiceModel.Discovery.dll
Dependency assembly - System.Collections.dll
Dependency assembly - System.Net.AuthenticationManager.dll
Dependency assembly - System.Linq.Queryable.dll
Dependency assembly - Unity.VisualScripting.Core.dll
Dependency assembly - System.Configuration.dll
Dependency assembly - System.Security.Claims.dll
Dependency assembly - DOTween.dll
Dependency assembly - System.Globalization.Calendars.dll
Dependency assembly - System.Net.Requests.dll
Dependency assembly - System.Net.WebSockets.Client.dll
Dependency assembly - System.Globalization.Extensions.dll
Dependency assembly - System.Security.Cryptography.Primitives.dll
Dependency assembly - System.IO.UnmanagedMemoryStream.dll
Dependency assembly - System.Xml.dll
Dependency assembly - System.Net.WebSockets.dll
Dependency assembly - System.ObjectModel.dll
Dependency assembly - System.Security.Cryptography.Encryption.dll
Dependency assembly - System.Data.SqlClient.dll
Dependency assembly - System.Security.SecureString.dll
Dependency assembly - System.Security.Cryptography.Hashing.Algorithms.dll
Dependency assembly - System.Threading.Overlapped.dll
Dependency assembly - System.Runtime.Serialization.Formatters.dll
Dependency assembly - System.ServiceModel.Http.dll
Dependency assembly - System.Threading.dll
Dependency assembly - System.Resources.Writer.dll
Dependency assembly - System.Reflection.DispatchProxy.dll
Dependency assembly - System.Reflection.Extensions.dll
Dependency assembly - System.Reflection.dll
Dependency assembly - Mono.Data.Sqlite.dll
Dependency assembly - System.Security.Cryptography.Csp.dll
Dependency assembly - Mono.Security.dll
Dependency assembly - System.ServiceModel.Duplex.dll
Dependency assembly - System.IdentityModel.Selectors.dll
Dependency assembly - System.Windows.dll
Dependency assembly - System.Security.Cryptography.ProtectedData.dll
Dependency assembly - System.Threading.AccessControl.dll
Dependency assembly - System.Diagnostics.FileVersionInfo.dll
Dependency assembly - System.Reflection.Emit.Lightweight.dll
Dependency assembly - System.Net.Ping.dll
Dependency assembly - System.ComponentModel.Annotations.dll
Dependency assembly - System.Xml.Xsl.Primitives.dll
Dependency assembly - System.IO.FileSystem.Watcher.dll
Dependency assembly - System.Security.Cryptography.RandomNumberGenerator.dll
Dependency assembly - Microsoft.CSharp.dll
Dependency assembly - System.Data.DataSetExtensions.dll
Dependency assembly - System.Runtime.dll
Dependency assembly - System.IO.Compression.dll
Dependency assembly - System.IO.Pipes.dll
Dependency assembly - System.Security.AccessControl.dll
Dependency assembly - System.Runtime.Serialization.Formatters.Soap.dll
Dependency assembly - System.Web.RegularExpressions.dll
Dependency assembly - System.IO.FileSystem.AccessControl.dll
Dependency assembly - System.Runtime.Serialization.Xml.dll
Dependency assembly - System.Security.Cryptography.Encoding.dll
Dependency assembly - Newtonsoft.Json.dll
Dependency assembly - System.Collections.NonGeneric.dll
Dependency assembly - System.Windows.Forms.dll
Dependency assembly - System.Net.ServicePoint.dll
Dependency assembly - System.Reflection.TypeExtensions.dll
Dependency assembly - System.IdentityModel.dll
Dependency assembly - System.Runtime.InteropServices.dll
Dependency assembly - System.Console.dll
Dependency assembly - System.IO.Compression.FileSystem.dll
Dependency assembly - System.Net.Cache.dll
Dependency assembly - System.ServiceModel.Web.dll
Dependency assembly - System.Xaml.dll
Dependency assembly - System.Buffers.dll
Dependency assembly - System.ComponentModel.Primitives.dll
Dependency assembly - System.Net.NameResolution.dll
Dependency assembly - System.Net.WebHeaderCollection.dll
Dependency assembly - System.Security.Cryptography.Encryption.ECDsa.dll
Dependency assembly - System.Data.Common.dll
Dependency assembly - Unity.VisualScripting.Antlr3.Runtime.dll
Dependency assembly - System.Diagnostics.Process.dll
Dependency assembly - Unity.InputSystem.dll
Dependency assembly - System.Text.Encoding.dll
Dependency assembly - System.Collections.Specialized.dll
Dependency assembly - System.Threading.Tasks.Extensions.dll
Dependency assembly - System.AppContext.dll
Dependency assembly - NavMeshPlus.dll
Dependency assembly - System.Net.HttpListener.dll
Dependency assembly - System.Reflection.Emit.dll
Dependency assembly - System.Runtime.InteropServices.RuntimeInformation.dll
Dependency assembly - System.Net.Http.dll
Dependency assembly - Microsoft.Win32.Registry.AccessControl.dll
Dependency assembly - System.ValueTuple.dll
Dependency assembly - System.Data.Entity.dll
Dependency assembly - System.Net.Security.dll

Compressed texture icon_512 is used as icon. This might compromise visual quality of the final image. Uncompressed format might be considered as better import option.
UnityEngine.StackTraceUtility:ExtractStackTrace () (at /Users/<USER>/build/output/unity/unity/Runtime/Export/Scripting/StackTrace.cs:37)
UnityEditor.BuildPipeline:BuildPlayerInternal (string[],string,string,UnityEditor.BuildTargetGroup,UnityEditor.BuildTarget,int,UnityEditor.BuildOptions,string[]) (at /Users/<USER>/build/output/unity/unity/Editor/Mono/BuildPipeline.bindings.cs:531)
UnityEditor.BuildPipeline:BuildPlayer (string[],string,string,UnityEditor.BuildTargetGroup,UnityEditor.BuildTarget,int,UnityEditor.BuildOptions,string[]) (at /Users/<USER>/build/output/unity/unity/Editor/Mono/BuildPipeline.bindings.cs:396)
UnityEditor.BuildPipeline:BuildPlayer (UnityEditor.BuildPlayerOptions) (at /Users/<USER>/build/output/unity/unity/Editor/Mono/BuildPipeline.bindings.cs:342)
BuildTools.CommandLineBuild:BuildAndroidAPK () (at Assets/Editor/CommandLineBuild.cs:170)
BuildTools.CommandLineBuild:BuildAndroid () (at Assets/Editor/CommandLineBuild.cs:101)

[/Users/<USER>/build/output/unity/unity/Editor/Mono/BuildPipeline.bindings.cs line 531]

Compressed texture icon_512 is used as icon. This might compromise visual quality of the final image. Uncompressed format might be considered as better import option.
UnityEngine.StackTraceUtility:ExtractStackTrace () (at /Users/<USER>/build/output/unity/unity/Runtime/Export/Scripting/StackTrace.cs:37)
UnityEditor.BuildPipeline:BuildPlayerInternal (string[],string,string,UnityEditor.BuildTargetGroup,UnityEditor.BuildTarget,int,UnityEditor.BuildOptions,string[]) (at /Users/<USER>/build/output/unity/unity/Editor/Mono/BuildPipeline.bindings.cs:531)
UnityEditor.BuildPipeline:BuildPlayer (string[],string,string,UnityEditor.BuildTargetGroup,UnityEditor.BuildTarget,int,UnityEditor.BuildOptions,string[]) (at /Users/<USER>/build/output/unity/unity/Editor/Mono/BuildPipeline.bindings.cs:396)
UnityEditor.BuildPipeline:BuildPlayer (UnityEditor.BuildPlayerOptions) (at /Users/<USER>/build/output/unity/unity/Editor/Mono/BuildPipeline.bindings.cs:342)
BuildTools.CommandLineBuild:BuildAndroidAPK () (at Assets/Editor/CommandLineBuild.cs:170)
BuildTools.CommandLineBuild:BuildAndroid () (at Assets/Editor/CommandLineBuild.cs:101)

[/Users/<USER>/build/output/unity/unity/Editor/Mono/BuildPipeline.bindings.cs line 531]

Compressed texture icon_512 is used as icon. This might compromise visual quality of the final image. Uncompressed format might be considered as better import option.
UnityEngine.StackTraceUtility:ExtractStackTrace () (at /Users/<USER>/build/output/unity/unity/Runtime/Export/Scripting/StackTrace.cs:37)
UnityEditor.BuildPipeline:BuildPlayerInternal (string[],string,string,UnityEditor.BuildTargetGroup,UnityEditor.BuildTarget,int,UnityEditor.BuildOptions,string[]) (at /Users/<USER>/build/output/unity/unity/Editor/Mono/BuildPipeline.bindings.cs:531)
UnityEditor.BuildPipeline:BuildPlayer (string[],string,string,UnityEditor.BuildTargetGroup,UnityEditor.BuildTarget,int,UnityEditor.BuildOptions,string[]) (at /Users/<USER>/build/output/unity/unity/Editor/Mono/BuildPipeline.bindings.cs:396)
UnityEditor.BuildPipeline:BuildPlayer (UnityEditor.BuildPlayerOptions) (at /Users/<USER>/build/output/unity/unity/Editor/Mono/BuildPipeline.bindings.cs:342)
BuildTools.CommandLineBuild:BuildAndroidAPK () (at Assets/Editor/CommandLineBuild.cs:170)
BuildTools.CommandLineBuild:BuildAndroid () (at Assets/Editor/CommandLineBuild.cs:101)

[/Users/<USER>/build/output/unity/unity/Editor/Mono/BuildPipeline.bindings.cs line 531]

Compressed texture icon_512 is used as icon. This might compromise visual quality of the final image. Uncompressed format might be considered as better import option.
UnityEngine.StackTraceUtility:ExtractStackTrace () (at /Users/<USER>/build/output/unity/unity/Runtime/Export/Scripting/StackTrace.cs:37)
UnityEditor.BuildPipeline:BuildPlayerInternal (string[],string,string,UnityEditor.BuildTargetGroup,UnityEditor.BuildTarget,int,UnityEditor.BuildOptions,string[]) (at /Users/<USER>/build/output/unity/unity/Editor/Mono/BuildPipeline.bindings.cs:531)
UnityEditor.BuildPipeline:BuildPlayer (string[],string,string,UnityEditor.BuildTargetGroup,UnityEditor.BuildTarget,int,UnityEditor.BuildOptions,string[]) (at /Users/<USER>/build/output/unity/unity/Editor/Mono/BuildPipeline.bindings.cs:396)
UnityEditor.BuildPipeline:BuildPlayer (UnityEditor.BuildPlayerOptions) (at /Users/<USER>/build/output/unity/unity/Editor/Mono/BuildPipeline.bindings.cs:342)
BuildTools.CommandLineBuild:BuildAndroidAPK () (at Assets/Editor/CommandLineBuild.cs:170)
BuildTools.CommandLineBuild:BuildAndroid () (at Assets/Editor/CommandLineBuild.cs:101)

[/Users/<USER>/build/output/unity/unity/Editor/Mono/BuildPipeline.bindings.cs line 531]

Compressed texture icon_512 is used as icon. This might compromise visual quality of the final image. Uncompressed format might be considered as better import option.
UnityEngine.StackTraceUtility:ExtractStackTrace () (at /Users/<USER>/build/output/unity/unity/Runtime/Export/Scripting/StackTrace.cs:37)
UnityEditor.BuildPipeline:BuildPlayerInternal (string[],string,string,UnityEditor.BuildTargetGroup,UnityEditor.BuildTarget,int,UnityEditor.BuildOptions,string[]) (at /Users/<USER>/build/output/unity/unity/Editor/Mono/BuildPipeline.bindings.cs:531)
UnityEditor.BuildPipeline:BuildPlayer (string[],string,string,UnityEditor.BuildTargetGroup,UnityEditor.BuildTarget,int,UnityEditor.BuildOptions,string[]) (at /Users/<USER>/build/output/unity/unity/Editor/Mono/BuildPipeline.bindings.cs:396)
UnityEditor.BuildPipeline:BuildPlayer (UnityEditor.BuildPlayerOptions) (at /Users/<USER>/build/output/unity/unity/Editor/Mono/BuildPipeline.bindings.cs:342)
BuildTools.CommandLineBuild:BuildAndroidAPK () (at Assets/Editor/CommandLineBuild.cs:170)
BuildTools.CommandLineBuild:BuildAndroid () (at Assets/Editor/CommandLineBuild.cs:101)

[/Users/<USER>/build/output/unity/unity/Editor/Mono/BuildPipeline.bindings.cs line 531]

Compressed texture icon_512 is used as icon. This might compromise visual quality of the final image. Uncompressed format might be considered as better import option.
UnityEngine.StackTraceUtility:ExtractStackTrace () (at /Users/<USER>/build/output/unity/unity/Runtime/Export/Scripting/StackTrace.cs:37)
UnityEditor.BuildPipeline:BuildPlayerInternal (string[],string,string,UnityEditor.BuildTargetGroup,UnityEditor.BuildTarget,int,UnityEditor.BuildOptions,string[]) (at /Users/<USER>/build/output/unity/unity/Editor/Mono/BuildPipeline.bindings.cs:531)
UnityEditor.BuildPipeline:BuildPlayer (string[],string,string,UnityEditor.BuildTargetGroup,UnityEditor.BuildTarget,int,UnityEditor.BuildOptions,string[]) (at /Users/<USER>/build/output/unity/unity/Editor/Mono/BuildPipeline.bindings.cs:396)
UnityEditor.BuildPipeline:BuildPlayer (UnityEditor.BuildPlayerOptions) (at /Users/<USER>/build/output/unity/unity/Editor/Mono/BuildPipeline.bindings.cs:342)
BuildTools.CommandLineBuild:BuildAndroidAPK () (at Assets/Editor/CommandLineBuild.cs:170)
BuildTools.CommandLineBuild:BuildAndroid () (at Assets/Editor/CommandLineBuild.cs:101)

[/Users/<USER>/build/output/unity/unity/Editor/Mono/BuildPipeline.bindings.cs line 531]

Compressed texture icon_512 is used as icon. This might compromise visual quality of the final image. Uncompressed format might be considered as better import option.
UnityEngine.StackTraceUtility:ExtractStackTrace () (at /Users/<USER>/build/output/unity/unity/Runtime/Export/Scripting/StackTrace.cs:37)
UnityEditor.BuildPipeline:BuildPlayerInternal (string[],string,string,UnityEditor.BuildTargetGroup,UnityEditor.BuildTarget,int,UnityEditor.BuildOptions,string[]) (at /Users/<USER>/build/output/unity/unity/Editor/Mono/BuildPipeline.bindings.cs:531)
UnityEditor.BuildPipeline:BuildPlayer (string[],string,string,UnityEditor.BuildTargetGroup,UnityEditor.BuildTarget,int,UnityEditor.BuildOptions,string[]) (at /Users/<USER>/build/output/unity/unity/Editor/Mono/BuildPipeline.bindings.cs:396)
UnityEditor.BuildPipeline:BuildPlayer (UnityEditor.BuildPlayerOptions) (at /Users/<USER>/build/output/unity/unity/Editor/Mono/BuildPipeline.bindings.cs:342)
BuildTools.CommandLineBuild:BuildAndroidAPK () (at Assets/Editor/CommandLineBuild.cs:170)
BuildTools.CommandLineBuild:BuildAndroid () (at Assets/Editor/CommandLineBuild.cs:101)

[/Users/<USER>/build/output/unity/unity/Editor/Mono/BuildPipeline.bindings.cs line 531]

Compressed texture icon_512 is used as icon. This might compromise visual quality of the final image. Uncompressed format might be considered as better import option.
UnityEngine.StackTraceUtility:ExtractStackTrace () (at /Users/<USER>/build/output/unity/unity/Runtime/Export/Scripting/StackTrace.cs:37)
UnityEditor.BuildPipeline:BuildPlayerInternal (string[],string,string,UnityEditor.BuildTargetGroup,UnityEditor.BuildTarget,int,UnityEditor.BuildOptions,string[]) (at /Users/<USER>/build/output/unity/unity/Editor/Mono/BuildPipeline.bindings.cs:531)
UnityEditor.BuildPipeline:BuildPlayer (string[],string,string,UnityEditor.BuildTargetGroup,UnityEditor.BuildTarget,int,UnityEditor.BuildOptions,string[]) (at /Users/<USER>/build/output/unity/unity/Editor/Mono/BuildPipeline.bindings.cs:396)
UnityEditor.BuildPipeline:BuildPlayer (UnityEditor.BuildPlayerOptions) (at /Users/<USER>/build/output/unity/unity/Editor/Mono/BuildPipeline.bindings.cs:342)
BuildTools.CommandLineBuild:BuildAndroidAPK () (at Assets/Editor/CommandLineBuild.cs:170)
BuildTools.CommandLineBuild:BuildAndroid () (at Assets/Editor/CommandLineBuild.cs:101)

[/Users/<USER>/build/output/unity/unity/Editor/Mono/BuildPipeline.bindings.cs line 531]

Compressed texture icon_512 is used as icon. This might compromise visual quality of the final image. Uncompressed format might be considered as better import option.
UnityEngine.StackTraceUtility:ExtractStackTrace () (at /Users/<USER>/build/output/unity/unity/Runtime/Export/Scripting/StackTrace.cs:37)
UnityEditor.BuildPipeline:BuildPlayerInternal (string[],string,string,UnityEditor.BuildTargetGroup,UnityEditor.BuildTarget,int,UnityEditor.BuildOptions,string[]) (at /Users/<USER>/build/output/unity/unity/Editor/Mono/BuildPipeline.bindings.cs:531)
UnityEditor.BuildPipeline:BuildPlayer (string[],string,string,UnityEditor.BuildTargetGroup,UnityEditor.BuildTarget,int,UnityEditor.BuildOptions,string[]) (at /Users/<USER>/build/output/unity/unity/Editor/Mono/BuildPipeline.bindings.cs:396)
UnityEditor.BuildPipeline:BuildPlayer (UnityEditor.BuildPlayerOptions) (at /Users/<USER>/build/output/unity/unity/Editor/Mono/BuildPipeline.bindings.cs:342)
BuildTools.CommandLineBuild:BuildAndroidAPK () (at Assets/Editor/CommandLineBuild.cs:170)
BuildTools.CommandLineBuild:BuildAndroid () (at Assets/Editor/CommandLineBuild.cs:101)

[/Users/<USER>/build/output/unity/unity/Editor/Mono/BuildPipeline.bindings.cs line 531]

Compressed texture icon_512 is used as icon. This might compromise visual quality of the final image. Uncompressed format might be considered as better import option.
UnityEngine.StackTraceUtility:ExtractStackTrace () (at /Users/<USER>/build/output/unity/unity/Runtime/Export/Scripting/StackTrace.cs:37)
UnityEditor.BuildPipeline:BuildPlayerInternal (string[],string,string,UnityEditor.BuildTargetGroup,UnityEditor.BuildTarget,int,UnityEditor.BuildOptions,string[]) (at /Users/<USER>/build/output/unity/unity/Editor/Mono/BuildPipeline.bindings.cs:531)
UnityEditor.BuildPipeline:BuildPlayer (string[],string,string,UnityEditor.BuildTargetGroup,UnityEditor.BuildTarget,int,UnityEditor.BuildOptions,string[]) (at /Users/<USER>/build/output/unity/unity/Editor/Mono/BuildPipeline.bindings.cs:396)
UnityEditor.BuildPipeline:BuildPlayer (UnityEditor.BuildPlayerOptions) (at /Users/<USER>/build/output/unity/unity/Editor/Mono/BuildPipeline.bindings.cs:342)
BuildTools.CommandLineBuild:BuildAndroidAPK () (at Assets/Editor/CommandLineBuild.cs:170)
BuildTools.CommandLineBuild:BuildAndroid () (at Assets/Editor/CommandLineBuild.cs:101)

[/Users/<USER>/build/output/unity/unity/Editor/Mono/BuildPipeline.bindings.cs line 531]

Compressed texture icon_512 is used as icon. This might compromise visual quality of the final image. Uncompressed format might be considered as better import option.
UnityEngine.StackTraceUtility:ExtractStackTrace () (at /Users/<USER>/build/output/unity/unity/Runtime/Export/Scripting/StackTrace.cs:37)
UnityEditor.BuildPipeline:BuildPlayerInternal (string[],string,string,UnityEditor.BuildTargetGroup,UnityEditor.BuildTarget,int,UnityEditor.BuildOptions,string[]) (at /Users/<USER>/build/output/unity/unity/Editor/Mono/BuildPipeline.bindings.cs:531)
UnityEditor.BuildPipeline:BuildPlayer (string[],string,string,UnityEditor.BuildTargetGroup,UnityEditor.BuildTarget,int,UnityEditor.BuildOptions,string[]) (at /Users/<USER>/build/output/unity/unity/Editor/Mono/BuildPipeline.bindings.cs:396)
UnityEditor.BuildPipeline:BuildPlayer (UnityEditor.BuildPlayerOptions) (at /Users/<USER>/build/output/unity/unity/Editor/Mono/BuildPipeline.bindings.cs:342)
BuildTools.CommandLineBuild:BuildAndroidAPK () (at Assets/Editor/CommandLineBuild.cs:170)
BuildTools.CommandLineBuild:BuildAndroid () (at Assets/Editor/CommandLineBuild.cs:101)

[/Users/<USER>/build/output/unity/unity/Editor/Mono/BuildPipeline.bindings.cs line 531]

Compressed texture icon_512 is used as icon. This might compromise visual quality of the final image. Uncompressed format might be considered as better import option.
UnityEngine.StackTraceUtility:ExtractStackTrace () (at /Users/<USER>/build/output/unity/unity/Runtime/Export/Scripting/StackTrace.cs:37)
UnityEditor.BuildPipeline:BuildPlayerInternal (string[],string,string,UnityEditor.BuildTargetGroup,UnityEditor.BuildTarget,int,UnityEditor.BuildOptions,string[]) (at /Users/<USER>/build/output/unity/unity/Editor/Mono/BuildPipeline.bindings.cs:531)
UnityEditor.BuildPipeline:BuildPlayer (string[],string,string,UnityEditor.BuildTargetGroup,UnityEditor.BuildTarget,int,UnityEditor.BuildOptions,string[]) (at /Users/<USER>/build/output/unity/unity/Editor/Mono/BuildPipeline.bindings.cs:396)
UnityEditor.BuildPipeline:BuildPlayer (UnityEditor.BuildPlayerOptions) (at /Users/<USER>/build/output/unity/unity/Editor/Mono/BuildPipeline.bindings.cs:342)
BuildTools.CommandLineBuild:BuildAndroidAPK () (at Assets/Editor/CommandLineBuild.cs:170)
BuildTools.CommandLineBuild:BuildAndroid () (at Assets/Editor/CommandLineBuild.cs:101)

[/Users/<USER>/build/output/unity/unity/Editor/Mono/BuildPipeline.bindings.cs line 531]

DisplayProgressbar: Initializing
Android PostProcess task "Initializing" took 0.2819 ms
DisplayProgressbar: Check gradle project collisions
Android PostProcess task "Check gradle project collisions" took 0.1572 ms
DisplayProgressbar: Preparing banner
Android PostProcess task "Preparing banner" took 0.2596 ms
[40m[32minfo[39m[22m[49m: Microsoft.AspNetCore.Hosting.Diagnostics[1]
      Request starting HTTP/2 POST http://ilpp/UnityILPP.PostProcessing/Ping application/grpc -
[40m[32minfo[39m[22m[49m: Microsoft.AspNetCore.Routing.EndpointMiddleware[0]
      Executing endpoint 'gRPC - /UnityILPP.PostProcessing/Ping'
[40m[32minfo[39m[22m[49m: Microsoft.AspNetCore.Routing.EndpointMiddleware[1]
      Executed endpoint 'gRPC - /UnityILPP.PostProcessing/Ping'
[40m[32minfo[39m[22m[49m: Microsoft.AspNetCore.Hosting.Diagnostics[2]
      Request finished HTTP/2 POST http://ilpp/UnityILPP.PostProcessing/Ping application/grpc - - 200 - application/grpc 0.1864ms
Starting: /Applications/Unity/Hub/Editor/2022.3.62f1/Unity.app/Contents/bee_backend --ipc --dagfile="Library/Bee/Playerf1b11ce1.dag" --profile="Library/Bee/backend1.traceevents" Player
WorkingDir: /Users/<USER>/Documents/GitHub/PerfumerProject
Rebuilding DAG because FileSignature timestamp changed: Library/Bee/Playerf1b11ce1-inputdata.json
*** Tundra requires additional run (0.03 seconds), 0 items updated, 0 evaluated
Starting: /Applications/Unity/Hub/Editor/2022.3.62f1/Unity.app/Contents/Tools/netcorerun/netcorerun "/Applications/Unity/Hub/Editor/2022.3.62f1/PlaybackEngines/AndroidPlayer/AndroidPlayerBuildProgram.exe" "/Applications/Unity/Hub/Editor/2022.3.62f1/PlaybackEngines/AndroidPlayer/Bee:/Applications/Unity/Hub/Editor/2022.3.62f1/Unity.app/Contents/Tools/BuildPipeline" "Library/Bee/Playerf1b11ce1.dag.json" "Library/Bee/Playerf1b11ce1-inputdata.json" "Library/Bee/buildprogram0.traceevents"
WorkingDir: /Users/<USER>/Documents/GitHub/PerfumerProject
ExitCode: 4 Duration: 0s108ms
DisplayProgressbar: Incremental Player Build
ExitCode: 0 Duration: 0s484ms
Starting: /Applications/Unity/Hub/Editor/2022.3.62f1/Unity.app/Contents/bee_backend --ipc --dagfile="Library/Bee/Playerf1b11ce1.dag" --dagfilejson="Library/Bee/Playerf1b11ce1.dag.json" --profile="Library/Bee/backend2.traceevents" Player
WorkingDir: /Users/<USER>/Documents/GitHub/PerfumerProject
Finished compiling graph: 1316 nodes, 3597 flattened edges (3593 ToBuild, 6 ToUse), maximum node priority 841
[ 260/1311  0s] CopyFiles /Users/<USER>/Documents/GitHub/PerfumerProject/Library/Bee/Android/Prj/IL2CPP/Gradle/unityLibrary/src/main/assets/bin/Data/globalgamemanagers.assets.split1
[ 261/1311  0s] CopyFiles /Users/<USER>/Documents/GitHub/PerfumerProject/Library/Bee/Android/Prj/IL2CPP/Gradle/unityLibrary/src/main/assets/bin/Data/globalgamemanagers.assets.split0
[ 262/1311  0s] CopyFiles /Users/<USER>/Documents/GitHub/PerfumerProject/Library/Bee/Android/Prj/IL2CPP/Gradle/unityLibrary/src/main/jniLibs/arm64-v8a/libmain.so
[ 263/1311  0s] CopyFiles /Users/<USER>/Documents/GitHub/PerfumerProject/Library/Bee/Android/Prj/IL2CPP/Gradle/unityLibrary/src/main/assets/bin/Data/level2
[ 264/1311  0s] CopyFiles /Users/<USER>/Documents/GitHub/PerfumerProject/Library/Bee/Android/Prj/IL2CPP/Gradle/unityLibrary/src/main/assets/bin/Data/level1
[ 265/1311  0s] CopyFiles /Users/<USER>/Documents/GitHub/PerfumerProject/Library/Bee/Android/Prj/IL2CPP/Gradle/unityLibrary/src/main/assets/bin/Data/sharedassets0.assets.split1
[ 266/1311  0s] CopyFiles /Users/<USER>/Documents/GitHub/PerfumerProject/Library/Bee/Android/Prj/IL2CPP/Gradle/unityLibrary/src/main/assets/bin/Data/sharedassets2.assets.split1
[ 267/1311  0s] CopyFiles /Users/<USER>/Documents/GitHub/PerfumerProject/Library/Bee/Android/Prj/IL2CPP/Gradle/unityLibrary/src/main/assets/bin/Data/sharedassets2.assets.split0
[ 268/1311  0s] CopyFiles /Users/<USER>/Documents/GitHub/PerfumerProject/Library/Bee/Android/Prj/IL2CPP/Gradle/unityLibrary/src/main/assets/bin/Data/level0
[ 269/1311  0s] CopyFiles /Users/<USER>/Documents/GitHub/PerfumerProject/Library/Bee/Android/Prj/IL2CPP/Gradle/unityLibrary/src/main/assets/bin/Data/sharedassets0.assets.split3
[ 270/1311  0s] CopyFiles /Users/<USER>/Documents/GitHub/PerfumerProject/Library/Bee/Android/Prj/IL2CPP/Gradle/unityLibrary/src/main/assets/bin/Data/sharedassets0.assets.split52
[ 271/1311  0s] CopyFiles /Users/<USER>/Documents/GitHub/PerfumerProject/Library/Bee/Android/Prj/IL2CPP/Gradle/unityLibrary/src/main/assets/bin/Data/sharedassets0.assets.split41
[ 272/1311  0s] CopyFiles /Users/<USER>/Documents/GitHub/PerfumerProject/Library/Bee/Android/Prj/IL2CPP/Gradle/unityLibrary/src/main/assets/bin/Data/sharedassets0.assets.split30
[ 273/1311  0s] CopyFiles /Users/<USER>/Documents/GitHub/PerfumerProject/Library/Bee/Android/Prj/IL2CPP/Gradle/unityLibrary/src/main/assets/bin/Data/sharedassets0.assets.split12
[ 274/1311  0s] CopyFiles /Users/<USER>/Documents/GitHub/PerfumerProject/Library/Bee/Android/Prj/IL2CPP/Gradle/unityLibrary/src/main/assets/bin/Data/sharedassets0.assets.split19
[ 275/1311  0s] CopyFiles /Users/<USER>/Documents/GitHub/PerfumerProject/Library/Bee/Android/Prj/IL2CPP/Gradle/unityLibrary/src/main/assets/bin/Data/sharedassets0.assets.split47
[ 276/1311  0s] CopyFiles /Users/<USER>/Documents/GitHub/PerfumerProject/Library/Bee/Android/Prj/IL2CPP/Gradle/unityLibrary/src/main/assets/bin/Data/sharedassets0.assets.split26
[ 277/1311  0s] CopyFiles /Users/<USER>/Documents/GitHub/PerfumerProject/Library/Bee/Android/Prj/IL2CPP/Gradle/unityLibrary/src/main/assets/bin/Data/sharedassets0.assets.split4
[ 278/1311  0s] CopyFiles /Users/<USER>/Documents/GitHub/PerfumerProject/Library/Bee/Android/Prj/IL2CPP/Gradle/unityLibrary/src/main/assets/bin/Data/sharedassets0.assets.split57
[ 279/1311  0s] CopyFiles /Users/<USER>/Documents/GitHub/PerfumerProject/Library/Bee/Android/Prj/IL2CPP/Gradle/unityLibrary/src/main/assets/bin/Data/sharedassets0.assets.split40
[ 280/1311  0s] CopyFiles /Users/<USER>/Documents/GitHub/PerfumerProject/Library/Bee/Android/Prj/IL2CPP/Gradle/unityLibrary/src/main/assets/bin/Data/sharedassets0.assets.split55
[ 281/1311  0s] CopyFiles /Users/<USER>/Documents/GitHub/PerfumerProject/Library/Bee/Android/Prj/IL2CPP/Gradle/unityLibrary/src/main/assets/bin/Data/sharedassets0.assets.split25
[ 282/1311  0s] CopyFiles /Users/<USER>/Documents/GitHub/PerfumerProject/Library/Bee/Android/Prj/IL2CPP/Gradle/unityLibrary/src/main/assets/bin/Data/sharedassets0.assets.split45
[ 283/1311  0s] CopyFiles /Users/<USER>/Documents/GitHub/PerfumerProject/Library/Bee/Android/Prj/IL2CPP/Gradle/unityLibrary/src/main/assets/bin/Data/sharedassets0.assets.split2
[ 284/1311  0s] CopyFiles /Users/<USER>/Documents/GitHub/PerfumerProject/Library/Bee/Android/Prj/IL2CPP/Gradle/unityLibrary/src/main/assets/bin/Data/sharedassets0.assets.split29
[ 285/1311  0s] CopyFiles /Users/<USER>/Documents/GitHub/PerfumerProject/Library/Bee/Android/Prj/IL2CPP/Gradle/unityLibrary/src/main/assets/bin/Data/sharedassets0.assets.split44
[ 286/1311  0s] CopyFiles /Users/<USER>/Documents/GitHub/PerfumerProject/Library/Bee/Android/Prj/IL2CPP/Gradle/unityLibrary/src/main/assets/bin/Data/sharedassets0.assets.split60
[ 287/1311  0s] CopyFiles /Users/<USER>/Documents/GitHub/PerfumerProject/Library/Bee/Android/Prj/IL2CPP/Gradle/unityLibrary/src/main/assets/bin/Data/sharedassets0.assets.split38
[ 288/1311  0s] CopyFiles /Users/<USER>/Documents/GitHub/PerfumerProject/Library/Bee/Android/Prj/IL2CPP/Gradle/unityLibrary/src/main/assets/bin/Data/sharedassets0.assets.split18
[ 289/1311  0s] CopyFiles /Users/<USER>/Documents/GitHub/PerfumerProject/Library/Bee/Android/Prj/IL2CPP/Gradle/unityLibrary/src/main/assets/bin/Data/sharedassets0.assets.split8
[ 290/1311  0s] CopyFiles /Users/<USER>/Documents/GitHub/PerfumerProject/Library/Bee/Android/Prj/IL2CPP/Gradle/unityLibrary/src/main/assets/bin/Data/sharedassets0.assets.split31
[ 291/1311  0s] CopyFiles /Users/<USER>/Documents/GitHub/PerfumerProject/Library/Bee/Android/Prj/IL2CPP/Gradle/unityLibrary/src/main/assets/bin/Data/sharedassets0.assets.split5
[ 292/1311  0s] CopyFiles /Users/<USER>/Documents/GitHub/PerfumerProject/Library/Bee/Android/Prj/IL2CPP/Gradle/unityLibrary/src/main/assets/bin/Data/sharedassets0.assets.split33
[ 293/1311  0s] CopyFiles /Users/<USER>/Documents/GitHub/PerfumerProject/Library/Bee/Android/Prj/IL2CPP/Gradle/unityLibrary/src/main/assets/bin/Data/sharedassets0.assets.split15
[ 294/1311  0s] CopyFiles /Users/<USER>/Documents/GitHub/PerfumerProject/Library/Bee/Android/Prj/IL2CPP/Gradle/unityLibrary/src/main/assets/bin/Data/sharedassets0.assets.split13
[ 295/1311  0s] CopyFiles /Users/<USER>/Documents/GitHub/PerfumerProject/Library/Bee/Android/Prj/IL2CPP/Gradle/unityLibrary/src/main/assets/bin/Data/sharedassets0.assets.split27
[ 296/1311  0s] CopyFiles /Users/<USER>/Documents/GitHub/PerfumerProject/Library/Bee/Android/Prj/IL2CPP/Gradle/unityLibrary/src/main/assets/bin/Data/sharedassets0.assets.split59
[ 297/1311  0s] CopyFiles /Users/<USER>/Documents/GitHub/PerfumerProject/Library/Bee/Android/Prj/IL2CPP/Gradle/unityLibrary/src/main/assets/bin/Data/sharedassets0.assets.split23
[ 298/1311  0s] CopyFiles /Users/<USER>/Documents/GitHub/PerfumerProject/Library/Bee/Android/Prj/IL2CPP/Gradle/unityLibrary/src/main/assets/bin/Data/sharedassets0.assets.split0
[ 299/1311  0s] CopyFiles /Users/<USER>/Documents/GitHub/PerfumerProject/Library/Bee/Android/Prj/IL2CPP/Gradle/unityLibrary/src/main/assets/bin/Data/sharedassets0.assets.split46
[ 300/1311  0s] CopyFiles /Users/<USER>/Documents/GitHub/PerfumerProject/Library/Bee/Android/Prj/IL2CPP/Gradle/unityLibrary/src/main/assets/bin/Data/sharedassets0.assets.split64
[ 301/1311  0s] CopyFiles /Users/<USER>/Documents/GitHub/PerfumerProject/Library/Bee/Android/Prj/IL2CPP/Gradle/unityLibrary/src/main/assets/bin/Data/sharedassets0.assets.split9
[ 302/1311  0s] CopyFiles /Users/<USER>/Documents/GitHub/PerfumerProject/Library/Bee/Android/Prj/IL2CPP/Gradle/unityLibrary/src/main/assets/bin/Data/sharedassets0.assets.split35
[ 303/1311  0s] CopyFiles /Users/<USER>/Documents/GitHub/PerfumerProject/Library/Bee/Android/Prj/IL2CPP/Gradle/unityLibrary/src/main/assets/bin/Data/sharedassets0.assets.split6
[ 304/1311  0s] CopyFiles /Users/<USER>/Documents/GitHub/PerfumerProject/Library/Bee/Android/Prj/IL2CPP/Gradle/unityLibrary/src/main/assets/bin/Data/sharedassets0.assets.split34
[ 305/1311  0s] CopyFiles /Users/<USER>/Documents/GitHub/PerfumerProject/Library/Bee/Android/Prj/IL2CPP/Gradle/unityLibrary/src/main/assets/bin/Data/sharedassets0.assets.split20
[ 306/1311  0s] CopyFiles /Users/<USER>/Documents/GitHub/PerfumerProject/Library/Bee/Android/Prj/IL2CPP/Gradle/unityLibrary/src/main/assets/bin/Data/sharedassets0.assets.split54
[ 307/1311  0s] CopyFiles /Users/<USER>/Documents/GitHub/PerfumerProject/Library/Bee/Android/Prj/IL2CPP/Gradle/unityLibrary/src/main/assets/bin/Data/sharedassets0.assets.split58
[ 308/1311  0s] CopyFiles /Users/<USER>/Documents/GitHub/PerfumerProject/Library/Bee/Android/Prj/IL2CPP/Gradle/unityLibrary/src/main/assets/bin/Data/sharedassets0.assets.split36
[ 309/1311  0s] CopyFiles /Users/<USER>/Documents/GitHub/PerfumerProject/Library/Bee/Android/Prj/IL2CPP/Gradle/unityLibrary/src/main/assets/bin/Data/sharedassets0.assets.split16
[ 310/1311  0s] CopyFiles /Users/<USER>/Documents/GitHub/PerfumerProject/Library/Bee/Android/Prj/IL2CPP/Gradle/unityLibrary/src/main/assets/bin/Data/sharedassets0.assets.split22
[ 311/1311  0s] CopyFiles /Users/<USER>/Documents/GitHub/PerfumerProject/Library/Bee/Android/Prj/IL2CPP/Gradle/unityLibrary/src/main/assets/bin/Data/sharedassets0.assets.split28
[ 312/1311  0s] CopyFiles /Users/<USER>/Documents/GitHub/PerfumerProject/Library/Bee/Android/Prj/IL2CPP/Gradle/unityLibrary/src/main/assets/bin/Data/sharedassets0.assets.split62
[ 313/1311  0s] CopyFiles /Users/<USER>/Documents/GitHub/PerfumerProject/Library/Bee/Android/Prj/IL2CPP/Gradle/unityLibrary/src/main/assets/bin/Data/sharedassets0.assets.split56
[ 314/1311  0s] CopyFiles /Users/<USER>/Documents/GitHub/PerfumerProject/Library/Bee/Android/Prj/IL2CPP/Gradle/unityLibrary/src/main/assets/bin/Data/sharedassets0.assets.split65
[ 315/1311  0s] CopyFiles /Users/<USER>/Documents/GitHub/PerfumerProject/Library/Bee/Android/Prj/IL2CPP/Gradle/unityLibrary/src/main/assets/bin/Data/sharedassets0.assets.split61
[ 316/1311  0s] CopyFiles /Users/<USER>/Documents/GitHub/PerfumerProject/Library/Bee/Android/Prj/IL2CPP/Gradle/unityLibrary/src/main/assets/bin/Data/sharedassets0.assets.split51
[ 317/1311  0s] CopyFiles /Users/<USER>/Documents/GitHub/PerfumerProject/Library/Bee/Android/Prj/IL2CPP/Gradle/unityLibrary/src/main/assets/bin/Data/sharedassets0.assets.split17
[ 318/1311  0s] CopyFiles /Users/<USER>/Documents/GitHub/PerfumerProject/Library/Bee/Android/Prj/IL2CPP/Gradle/unityLibrary/src/main/assets/bin/Data/sharedassets0.assets.split49
[ 319/1311  0s] CopyFiles /Users/<USER>/Documents/GitHub/PerfumerProject/Library/Bee/Android/Prj/IL2CPP/Gradle/unityLibrary/src/main/assets/bin/Data/sharedassets0.assets.split42
[ 320/1311  0s] CopyFiles /Users/<USER>/Documents/GitHub/PerfumerProject/Library/Bee/Android/Prj/IL2CPP/Gradle/unityLibrary/src/main/assets/bin/Data/sharedassets0.assets.split24
[ 321/1311  0s] CopyFiles /Users/<USER>/Documents/GitHub/PerfumerProject/Library/Bee/Android/Prj/IL2CPP/Gradle/unityLibrary/src/main/assets/bin/Data/sharedassets0.assets.split10
[ 322/1311  0s] CopyFiles /Users/<USER>/Documents/GitHub/PerfumerProject/Library/Bee/Android/Prj/IL2CPP/Gradle/unityLibrary/src/main/assets/bin/Data/sharedassets0.assets.split21
[ 323/1311  0s] CopyFiles /Users/<USER>/Documents/GitHub/PerfumerProject/Library/Bee/Android/Prj/IL2CPP/Gradle/unityLibrary/src/main/assets/bin/Data/sharedassets0.assets.split50
[ 324/1311  0s] CopyFiles /Users/<USER>/Documents/GitHub/PerfumerProject/Library/Bee/Android/Prj/IL2CPP/Gradle/unityLibrary/src/main/assets/bin/Data/sharedassets0.assets.split43
[ 325/1311  0s] CopyFiles /Users/<USER>/Documents/GitHub/PerfumerProject/Library/Bee/Android/Prj/IL2CPP/Gradle/unityLibrary/src/main/assets/bin/Data/sharedassets0.assets.split39
[ 326/1311  0s] CopyFiles /Users/<USER>/Documents/GitHub/PerfumerProject/Library/Bee/Android/Prj/IL2CPP/Gradle/unityLibrary/src/main/assets/bin/Data/sharedassets0.assets.split63
[ 327/1311  0s] CopyFiles /Users/<USER>/Documents/GitHub/PerfumerProject/Library/Bee/Android/Prj/IL2CPP/Gradle/unityLibrary/src/main/assets/bin/Data/sharedassets0.assets.split14
[ 328/1311  0s] CopyFiles /Users/<USER>/Documents/GitHub/PerfumerProject/Library/Bee/Android/Prj/IL2CPP/Gradle/unityLibrary/src/main/assets/bin/Data/sharedassets0.assets.split37
[ 329/1311  0s] CopyFiles /Users/<USER>/Documents/GitHub/PerfumerProject/Library/Bee/Android/Prj/IL2CPP/Gradle/unityLibrary/src/main/assets/bin/Data/sharedassets0.assets.split11
[ 330/1311  0s] CopyFiles /Users/<USER>/Documents/GitHub/PerfumerProject/Library/Bee/Android/Prj/IL2CPP/Gradle/unityLibrary/src/main/assets/bin/Data/sharedassets0.assets.split32
[ 331/1311  0s] CopyFiles /Users/<USER>/Documents/GitHub/PerfumerProject/Library/Bee/Android/Prj/IL2CPP/Gradle/unityLibrary/src/main/assets/bin/Data/sharedassets0.assets.split53
[ 332/1311  0s] CopyFiles /Users/<USER>/Documents/GitHub/PerfumerProject/Library/Bee/Android/Prj/IL2CPP/Gradle/unityLibrary/src/main/assets/bin/Data/sharedassets1.assets.split3
[ 333/1311  0s] CopyFiles /Users/<USER>/Documents/GitHub/PerfumerProject/Library/Bee/Android/Prj/IL2CPP/Gradle/unityLibrary/src/main/assets/bin/Data/sharedassets1.assets.split1
[ 334/1311  0s] CopyFiles /Users/<USER>/Documents/GitHub/PerfumerProject/Library/Bee/Android/Prj/IL2CPP/Gradle/unityLibrary/src/main/assets/bin/Data/sharedassets0.assets.split7
[ 335/1311  0s] CopyFiles /Users/<USER>/Documents/GitHub/PerfumerProject/Library/Bee/Android/Prj/IL2CPP/Gradle/unityLibrary/src/main/assets/bin/Data/sharedassets0.assets.split48
[ 336/1311  0s] CopyFiles /Users/<USER>/Documents/GitHub/PerfumerProject/Library/Bee/Android/Prj/IL2CPP/Gradle/unityLibrary/src/main/assets/bin/Data/sharedassets1.assets.split7
[ 337/1311  0s] CopyFiles /Users/<USER>/Documents/GitHub/PerfumerProject/Library/Bee/Android/Prj/IL2CPP/Gradle/unityLibrary/src/main/assets/bin/Data/sharedassets1.assets.split19
[ 338/1311  0s] CopyFiles /Users/<USER>/Documents/GitHub/PerfumerProject/Library/Bee/Android/Prj/IL2CPP/Gradle/unityLibrary/src/main/assets/bin/Data/sharedassets1.assets.split12
[ 339/1311  0s] CopyFiles /Users/<USER>/Documents/GitHub/PerfumerProject/Library/Bee/Android/Prj/IL2CPP/Gradle/unityLibrary/src/main/assets/bin/Data/sharedassets1.assets.split8
[ 340/1311  0s] CopyFiles /Users/<USER>/Documents/GitHub/PerfumerProject/Library/Bee/Android/Prj/IL2CPP/Gradle/unityLibrary/src/main/assets/bin/Data/sharedassets1.assets.split4
[ 341/1311  0s] CopyFiles /Users/<USER>/Documents/GitHub/PerfumerProject/Library/Bee/Android/Prj/IL2CPP/Gradle/unityLibrary/src/main/assets/bin/Data/sharedassets1.assets.split15
[ 342/1311  0s] CopyFiles /Users/<USER>/Documents/GitHub/PerfumerProject/Library/Bee/Android/Prj/IL2CPP/Gradle/unityLibrary/src/main/assets/bin/Data/sharedassets1.assets.split13
[ 343/1311  0s] CopyFiles /Users/<USER>/Documents/GitHub/PerfumerProject/Library/Bee/Android/Prj/IL2CPP/Gradle/unityLibrary/src/main/assets/bin/Data/sharedassets1.assets.split2
[ 344/1311  0s] CopyFiles /Users/<USER>/Documents/GitHub/PerfumerProject/Library/Bee/Android/Prj/IL2CPP/Gradle/unityLibrary/src/main/assets/bin/Data/sharedassets1.assets.split18
[ 345/1311  0s] CopyFiles /Users/<USER>/Documents/GitHub/PerfumerProject/Library/Bee/Android/Prj/IL2CPP/Gradle/unityLibrary/src/main/assets/bin/Data/sharedassets1.assets.split5
[ 346/1311  0s] CopyFiles /Users/<USER>/Documents/GitHub/PerfumerProject/Library/Bee/Android/Prj/IL2CPP/Gradle/unityLibrary/src/main/assets/bin/Data/sharedassets1.assets.split20
[ 347/1311  0s] CopyFiles /Users/<USER>/Documents/GitHub/PerfumerProject/Library/Bee/Android/Prj/IL2CPP/Gradle/unityLibrary/src/main/assets/bin/Data/sharedassets1.assets.split6
[ 348/1311  0s] CopyFiles /Users/<USER>/Documents/GitHub/PerfumerProject/Library/Bee/Android/Prj/IL2CPP/Gradle/unityLibrary/src/main/assets/bin/Data/sharedassets1.assets.split22
[ 349/1311  0s] CopyFiles /Users/<USER>/Documents/GitHub/PerfumerProject/Library/Bee/Android/Prj/IL2CPP/Gradle/unityLibrary/src/main/assets/bin/Data/sharedassets1.assets.split0
[ 350/1311  0s] CopyFiles /Users/<USER>/Documents/GitHub/PerfumerProject/Library/Bee/Android/Prj/IL2CPP/Gradle/unityLibrary/src/main/assets/bin/Data/sharedassets1.assets.split9
[ 351/1311  0s] CopyFiles /Users/<USER>/Documents/GitHub/PerfumerProject/Library/Bee/Android/Prj/IL2CPP/Gradle/unityLibrary/src/main/assets/bin/Data/sharedassets1.assets.split17
[ 352/1311  0s] CopyFiles /Users/<USER>/Documents/GitHub/PerfumerProject/Library/Bee/Android/Prj/IL2CPP/Gradle/unityLibrary/src/main/assets/bin/Data/sharedassets1.assets.split16
[ 353/1311  0s] CopyFiles /Users/<USER>/Documents/GitHub/PerfumerProject/Library/Bee/Android/Prj/IL2CPP/Gradle/unityLibrary/src/main/assets/bin/Data/sharedassets1.assets.split10
[ 354/1311  0s] CopyFiles /Users/<USER>/Documents/GitHub/PerfumerProject/Library/Bee/Android/Prj/IL2CPP/Gradle/unityLibrary/src/main/assets/bin/Data/sharedassets1.assets.split14
[ 355/1311  0s] WriteText /Users/<USER>/Documents/GitHub/PerfumerProject/Library/Bee/Android/Prj/IL2CPP/Gradle/unityLibrary/src/main/resources/META-INF/com.android.games.engine.build_fingerprint
[ 356/1311  0s] CopyFiles /Users/<USER>/Documents/GitHub/PerfumerProject/Library/Bee/Android/Prj/IL2CPP/Gradle/unityLibrary/src/main/assets/bin/Data/sharedassets1.assets.split21
[ 357/1311  0s] CopyFiles /Users/<USER>/Documents/GitHub/PerfumerProject/Library/Bee/Android/Prj/IL2CPP/Gradle/unityLibrary/src/main/assets/bin/Data/sharedassets1.assets.split11
[ 358/1311  0s] CopyFiles /Users/<USER>/Documents/GitHub/PerfumerProject/Library/Bee/Android/Prj/IL2CPP/Gradle/unityLibrary/libs/unity-classes.jar
[ 359/1311  0s] CopyFiles /Users/<USER>/Documents/GitHub/PerfumerProject/Library/Bee/Android/Prj/IL2CPP/Gradle/unityLibrary/src/main/res/values/ids.xml
[ 360/1311  0s] CopyFiles /Users/<USER>/Documents/GitHub/PerfumerProject/Library/Bee/Android/Prj/IL2CPP/Gradle/unityLibrary/src/main/assets/bin/Data/unity default resources
[ 361/1311  0s] CopyFiles /Users/<USER>/Documents/GitHub/PerfumerProject/Library/Bee/Android/Prj/IL2CPP/Gradle/unityLibrary/src/main/java/com/unity3d/player/UnityPlayerActivity.java
[ 362/1311  0s] CopyFiles /Users/<USER>/Documents/GitHub/PerfumerProject/Library/Bee/Android/Prj/IL2CPP/Gradle/unityLibrary/src/main/res/values-v21/styles.xml
[ 363/1311  0s] WriteText /Users/<USER>/Documents/GitHub/PerfumerProject/Library/Bee/Android/Prj/IL2CPP/Gradle/unityLibrary/src/main/res/values/colors.xml
[ 364/1311  0s] CopyFiles /Users/<USER>/Documents/GitHub/PerfumerProject/Library/Bee/Android/Prj/IL2CPP/Gradle/unityLibrary/src/main/res/values-v30/freeformwindow.xml
[ 365/1311  0s] CopyFiles /Users/<USER>/Documents/GitHub/PerfumerProject/Library/Bee/Android/Prj/IL2CPP/Gradle/unityLibrary/src/main/res/values/styles.xml
[ 366/1311  0s] CopyFiles /Users/<USER>/Documents/GitHub/PerfumerProject/Library/Bee/Android/Prj/IL2CPP/Gradle/unityLibrary/src/main/res/values-v31/styles.xml
[ 367/1311  0s] WriteText /Users/<USER>/Documents/GitHub/PerfumerProject/Library/Bee/Android/Prj/IL2CPP/Gradle/launcher/src/main/res/values/strings.xml
[ 368/1311  0s] CopyFiles /Users/<USER>/Documents/GitHub/PerfumerProject/Library/Bee/Android/Prj/IL2CPP/Gradle/launcher/src/main/res/mipmap-hdpi/app_icon.png
[ 369/1311  0s] CopyFiles /Users/<USER>/Documents/GitHub/PerfumerProject/Library/Bee/Android/Prj/IL2CPP/Gradle/unityLibrary/src/main/res/values/freeformwindow.xml
[ 370/1311  0s] CopyFiles /Users/<USER>/Documents/GitHub/PerfumerProject/Library/Bee/Android/Prj/IL2CPP/Gradle/launcher/src/main/res/mipmap-mdpi/app_icon.png
[ 371/1311  0s] CopyFiles /Users/<USER>/Documents/GitHub/PerfumerProject/Library/Bee/Android/Prj/IL2CPP/Gradle/launcher/src/main/res/mipmap-xhdpi/app_icon.png
[ 372/1311  0s] CopyFiles /Users/<USER>/Documents/GitHub/PerfumerProject/Library/Bee/Android/Prj/IL2CPP/Gradle/launcher/src/main/res/mipmap-ldpi/app_icon.png
[ 373/1311  0s] CopyFiles /Users/<USER>/Documents/GitHub/PerfumerProject/Library/Bee/Android/Prj/IL2CPP/Gradle/launcher/src/main/res/mipmap-ldpi/app_icon_round.png
[ 374/1311  0s] CopyFiles /Users/<USER>/Documents/GitHub/PerfumerProject/Library/Bee/Android/Prj/IL2CPP/Gradle/unityLibrary/src/main/assets/PerfumerData.xlsx
[ 375/1311  0s] CopyFiles /Users/<USER>/Documents/GitHub/PerfumerProject/Library/Bee/Android/Prj/IL2CPP/Gradle/launcher/src/main/res/mipmap-xxxhdpi/app_icon.png
[ 376/1311  0s] CopyFiles /Users/<USER>/Documents/GitHub/PerfumerProject/Library/Bee/Android/Prj/IL2CPP/Gradle/launcher/src/main/res/mipmap-xxhdpi/app_icon.png
[ 377/1311  0s] CopyFiles /Users/<USER>/Documents/GitHub/PerfumerProject/Library/Bee/Android/Prj/IL2CPP/Gradle/unityLibrary/src/main/assets/bin/Data/sharedassets0.resource
[ 378/1311  0s] CopyFiles /Users/<USER>/Documents/GitHub/PerfumerProject/Library/Bee/Android/Prj/IL2CPP/Gradle/unityLibrary/src/main/assets/bin/Data/ScriptingAssemblies.json
[ 379/1311  0s] CopyFiles /Users/<USER>/Documents/GitHub/PerfumerProject/Library/Bee/Android/Prj/IL2CPP/Gradle/launcher/src/main/res/mipmap-mdpi/app_icon_round.png
[ 380/1311  0s] CopyFiles /Users/<USER>/Documents/GitHub/PerfumerProject/Library/Bee/Android/Prj/IL2CPP/Gradle/launcher/src/main/res/mipmap-xhdpi/app_icon_round.png
[ 381/1311  0s] CopyFiles /Users/<USER>/Documents/GitHub/PerfumerProject/Library/Bee/Android/Prj/IL2CPP/Gradle/unityLibrary/src/main/assets/bin/Data/RuntimeInitializeOnLoads.json
[ 382/1311  0s] CopyFiles /Users/<USER>/Documents/GitHub/PerfumerProject/Library/Bee/Android/Prj/IL2CPP/Gradle/unityLibrary/src/main/assets/bin/Data/Resources/unity_builtin_extra
[ 383/1311  0s] CopyFiles /Users/<USER>/Documents/GitHub/PerfumerProject/Library/Bee/Android/Prj/IL2CPP/Gradle/launcher/src/main/res/mipmap-hdpi/app_icon_round.png
[ 384/1311  0s] CopyFiles /Users/<USER>/Documents/GitHub/PerfumerProject/Library/Bee/Android/Prj/IL2CPP/Gradle/launcher/src/main/res/mipmap-xxhdpi/app_icon_round.png
[ 385/1311  0s] CopyFiles /Users/<USER>/Documents/GitHub/PerfumerProject/Library/Bee/Android/Prj/IL2CPP/Gradle/launcher/src/main/res/mipmap-xxxhdpi/app_icon_round.png
[ 386/1311  0s] CopyFiles /Users/<USER>/Documents/GitHub/PerfumerProject/Library/Bee/Android/Prj/IL2CPP/Gradle/unityLibrary/src/main/assets/bin/Data/ffbd0831d997545eab75c364da082c1b
[ 387/1311  0s] CopyFiles /Users/<USER>/Documents/GitHub/PerfumerProject/Library/Bee/Android/Prj/IL2CPP/Gradle/unityLibrary/src/main/assets/bin/Data/fade42e8bc714b018fac513c043d323b
[ 388/1311  0s] CopyFiles /Users/<USER>/Documents/GitHub/PerfumerProject/Library/Bee/Android/Prj/IL2CPP/Gradle/unityLibrary/src/main/assets/bin/Data/f952c082cb03451daed3ee968ac6c63e
[ 389/1311  0s] CopyFiles /Users/<USER>/Documents/GitHub/PerfumerProject/Library/Bee/Android/Prj/IL2CPP/Gradle/unityLibrary/src/main/assets/bin/Data/globalgamemanagers
[ 390/1311  0s] CopyFiles /Users/<USER>/Documents/GitHub/PerfumerProject/Library/Bee/Android/Prj/IL2CPP/Gradle/unityLibrary/src/main/assets/bin/Data/f7ada0af4f174f0694ca6a487b8f543d
[ 391/1311  0s] CopyFiles /Users/<USER>/Documents/GitHub/PerfumerProject/Library/Bee/Android/Prj/IL2CPP/Gradle/unityLibrary/src/main/assets/bin/Data/fbafb8431307c42c9b1bfb9b27f6bd98
[ 392/1311  0s] CopyFiles /Users/<USER>/Documents/GitHub/PerfumerProject/Library/Bee/Android/Prj/IL2CPP/Gradle/unityLibrary/src/main/assets/bin/Data/e8ab00ef0d9684dcb9deb52811d91218
[ 393/1311  0s] CopyFiles /Users/<USER>/Documents/GitHub/PerfumerProject/Library/Bee/Android/Prj/IL2CPP/Gradle/unityLibrary/src/main/assets/bin/Data/f195bc6d5bdad3d4889ddcd308ee8d06
[ 394/1311  0s] CopyFiles /Users/<USER>/Documents/GitHub/PerfumerProject/Library/Bee/Android/Prj/IL2CPP/Gradle/unityLibrary/src/main/assets/bin/Data/eae76c041b933476bbe9bdd4c3d80793
[ 395/1311  0s] CopyFiles /Users/<USER>/Documents/GitHub/PerfumerProject/Library/Bee/Android/Prj/IL2CPP/Gradle/unityLibrary/src/main/assets/bin/Data/e73a58f6e2794ae7b1b7e50b7fb811b0
[ 396/1311  0s] CopyFiles /Users/<USER>/Documents/GitHub/PerfumerProject/Library/Bee/Android/Prj/IL2CPP/Gradle/unityLibrary/src/main/assets/bin/Data/e41b12c9271cbd245bf0748910cf2fa2
[ 397/1311  0s] CopyFiles /Users/<USER>/Documents/GitHub/PerfumerProject/Library/Bee/Android/Prj/IL2CPP/Gradle/unityLibrary/src/main/assets/bin/Data/e0d427add844a4d9faf970a3afa07583
[ 398/1311  0s] CopyFiles /Users/<USER>/Documents/GitHub/PerfumerProject/Library/Bee/Android/Prj/IL2CPP/Gradle/unityLibrary/src/main/assets/bin/Data/dd89cf5b9246416f84610a006f916af7
[ 399/1311  0s] CopyFiles /Users/<USER>/Documents/GitHub/PerfumerProject/Library/Bee/Android/Prj/IL2CPP/Gradle/unityLibrary/src/main/assets/bin/Data/dffef66376be4fa480fb02b19edbe903
[ 400/1311  0s] CopyFiles /Users/<USER>/Documents/GitHub/PerfumerProject/Library/Bee/Android/Prj/IL2CPP/Gradle/unityLibrary/src/main/assets/bin/Data/da9081d744879014ea4fc41f68a92105
[ 401/1311  0s] CopyFiles /Users/<USER>/Documents/GitHub/PerfumerProject/Library/Bee/Android/Prj/IL2CPP/Gradle/unityLibrary/src/main/assets/bin/Data/e0c2b90c058ff43f4a56a266d4fa721b
[ 402/1311  0s] CopyFiles /Users/<USER>/Documents/GitHub/PerfumerProject/Library/Bee/Android/Prj/IL2CPP/Gradle/unityLibrary/src/main/assets/bin/Data/d82c1b31c7e74239bff1220585707d2b
[ 403/1311  0s] CopyFiles /Users/<USER>/Documents/GitHub/PerfumerProject/Library/Bee/Android/Prj/IL2CPP/Gradle/unityLibrary/src/main/assets/bin/Data/d77186045353dff4db35f8e0d9dc75f0
[ 404/1311  0s] CopyFiles /Users/<USER>/Documents/GitHub/PerfumerProject/Library/Bee/Android/Prj/IL2CPP/Gradle/unityLibrary/src/main/assets/bin/Data/d6f174e740b5040c68466fb971c4cec2
[ 405/1311  0s] CopyFiles /Users/<USER>/Documents/GitHub/PerfumerProject/Library/Bee/Android/Prj/IL2CPP/Gradle/unityLibrary/src/main/assets/bin/Data/d61e7926ea821fa4693969e8c906b788
[ 406/1311  0s] CopyFiles /Users/<USER>/Documents/GitHub/PerfumerProject/Library/Bee/Android/Prj/IL2CPP/Gradle/unityLibrary/src/main/assets/bin/Data/d189f86f383a341ac8dcfe22cb6309a1
[ 407/1311  0s] CopyFiles /Users/<USER>/Documents/GitHub/PerfumerProject/Library/Bee/Android/Prj/IL2CPP/Gradle/unityLibrary/src/main/assets/bin/Data/cf81c85f95fe47e1a27f6ae460cf182c
[ 408/1311  0s] CopyFiles /Users/<USER>/Documents/GitHub/PerfumerProject/Library/Bee/Android/Prj/IL2CPP/Gradle/unityLibrary/src/main/assets/bin/Data/cc3247f4be15c0641bda48e960794497
[ 409/1311  0s] CopyFiles /Users/<USER>/Documents/GitHub/PerfumerProject/Library/Bee/Android/Prj/IL2CPP/Gradle/unityLibrary/src/main/assets/bin/Data/c6289d5f8fa843145a2355af9cb09719
[ 410/1311  0s] CopyFiles /Users/<USER>/Documents/GitHub/PerfumerProject/Library/Bee/Android/Prj/IL2CPP/Gradle/unityLibrary/src/main/assets/bin/Data/c356764ac08ce4af2806a601a4f1e6e9
[ 411/1311  0s] CopyFiles /Users/<USER>/Documents/GitHub/PerfumerProject/Library/Bee/Android/Prj/IL2CPP/Gradle/unityLibrary/src/main/assets/bin/Data/c207de86481ff7d48a2fba2fcc374723
[ 412/1311  0s] CopyFiles /Users/<USER>/Documents/GitHub/PerfumerProject/Library/Bee/Android/Prj/IL2CPP/Gradle/unityLibrary/src/main/assets/bin/Data/bffd475e4bfb82f49810ceafe8d17b78
[ 413/1311  0s] CopyFiles /Users/<USER>/Documents/GitHub/PerfumerProject/Library/Bee/Android/Prj/IL2CPP/Gradle/unityLibrary/src/main/assets/bin/Data/c1504b928e7d3dd40b9694deeaa10bbd
[ 414/1311  0s] CopyFiles /Users/<USER>/Documents/GitHub/PerfumerProject/Library/Bee/Android/Prj/IL2CPP/Gradle/unityLibrary/src/main/assets/bin/Data/c190b81ee9b05094ea2b081386f319db
[ 415/1311  0s] CopyFiles /Users/<USER>/Documents/GitHub/PerfumerProject/Library/Bee/Android/Prj/IL2CPP/Gradle/unityLibrary/src/main/assets/bin/Data/c41005c129ba4d66911b75229fd70b45
[ 416/1311  0s] CopyFiles /Users/<USER>/Documents/GitHub/PerfumerProject/Library/Bee/Android/Prj/IL2CPP/Gradle/unityLibrary/src/main/assets/bin/Data/b547d8b395d0cb041ad446e87d4ee399
[ 417/1311  0s] CopyFiles /Users/<USER>/Documents/GitHub/PerfumerProject/Library/Bee/Android/Prj/IL2CPP/Gradle/unityLibrary/src/main/assets/bin/Data/b87d71ae0daa94b408c88f249c7d5b91
[ 418/1311  0s] CopyFiles /Users/<USER>/Documents/GitHub/PerfumerProject/Library/Bee/Android/Prj/IL2CPP/Gradle/unityLibrary/src/main/assets/bin/Data/b20518d45890e4be59ba82946f88026c
[ 419/1311  0s] CopyFiles /Users/<USER>/Documents/GitHub/PerfumerProject/Library/Bee/Android/Prj/IL2CPP/Gradle/unityLibrary/src/main/assets/bin/Data/bc1ede39bf3643ee8e493720e4259791
[ 420/1311  0s] CopyFiles /Users/<USER>/Documents/GitHub/PerfumerProject/Library/Bee/Android/Prj/IL2CPP/Gradle/unityLibrary/src/main/assets/bin/Data/bd2b99773f3e0489aae9f9b5053ad360
[ 421/1311  0s] CopyFiles /Users/<USER>/Documents/GitHub/PerfumerProject/Library/Bee/Android/Prj/IL2CPP/Gradle/unityLibrary/src/main/assets/bin/Data/bb145366ce7024469a5758b08d31802c
[ 422/1311  0s] CopyFiles /Users/<USER>/Documents/GitHub/PerfumerProject/Library/Bee/Android/Prj/IL2CPP/Gradle/unityLibrary/src/main/assets/bin/Data/aa23ab70688268944bdddc6a67e7a775
[ 423/1311  0s] CopyFiles /Users/<USER>/Documents/GitHub/PerfumerProject/Library/Bee/Android/Prj/IL2CPP/Gradle/unityLibrary/src/main/assets/bin/Data/a52043f144750154191f485d35333ebe
[ 424/1311  0s] CopyFiles /Users/<USER>/Documents/GitHub/PerfumerProject/Library/Bee/Android/Prj/IL2CPP/Gradle/unityLibrary/src/main/assets/bin/Data/a02a7d8c237544f1962732b55a9aebf1
[ 425/1311  0s] CopyFiles /Users/<USER>/Documents/GitHub/PerfumerProject/Library/Bee/Android/Prj/IL2CPP/Gradle/unityLibrary/src/main/assets/bin/Data/ad88e655eeece8d47ba8133848934e78
[ 426/1311  0s] CopyFiles /Users/<USER>/Documents/GitHub/PerfumerProject/Library/Bee/Android/Prj/IL2CPP/Gradle/unityLibrary/src/main/assets/bin/Data/9c3ab7a98d51241bbb499643399fa761
[ 427/1311  0s] CopyFiles /Users/<USER>/Documents/GitHub/PerfumerProject/Library/Bee/Android/Prj/IL2CPP/Gradle/unityLibrary/src/main/assets/bin/Data/ac9bdd63a052c794582204b1d49f6939
[ 428/1311  0s] CopyFiles /Users/<USER>/Documents/GitHub/PerfumerProject/Library/Bee/Android/Prj/IL2CPP/Gradle/unityLibrary/src/main/assets/bin/Data/9bdedfa4650174106a3115624f193b99
[ 429/1311  0s] CopyFiles /Users/<USER>/Documents/GitHub/PerfumerProject/Library/Bee/Android/Prj/IL2CPP/Gradle/unityLibrary/src/main/assets/bin/Data/9bb64c8df70904eecac3f71f81bf339b
[ 430/1311  0s] CopyFiles /Users/<USER>/Documents/GitHub/PerfumerProject/Library/Bee/Android/Prj/IL2CPP/Gradle/unityLibrary/src/main/assets/bin/Data/93fddea8208764a2dbb189cc238aed40
[ 431/1311  0s] CopyFiles /Users/<USER>/Documents/GitHub/PerfumerProject/Library/Bee/Android/Prj/IL2CPP/Gradle/unityLibrary/src/main/assets/bin/Data/8a19432eef4e44247949edb78c590f64
[ 432/1311  0s] CopyFiles /Users/<USER>/Documents/GitHub/PerfumerProject/Library/Bee/Android/Prj/IL2CPP/Gradle/unityLibrary/src/main/assets/bin/Data/98197923ccc884542ae342c836761a58
[ 433/1311  0s] CopyFiles /Users/<USER>/Documents/GitHub/PerfumerProject/Library/Bee/Android/Prj/IL2CPP/Gradle/unityLibrary/src/main/assets/bin/Data/8a005a9e0713f4cc1b5ad29fb07657d3
[ 434/1311  0s] CopyFiles /Users/<USER>/Documents/GitHub/PerfumerProject/Library/Bee/Android/Prj/IL2CPP/Gradle/unityLibrary/src/main/assets/bin/Data/85187c2149c549c5b33f0cdb02836b17
[ 435/1311  0s] CopyFiles /Users/<USER>/Documents/GitHub/PerfumerProject/Library/Bee/Android/Prj/IL2CPP/Gradle/unityLibrary/src/main/assets/bin/Data/8345c33c7c8ada64083f41d452b315eb
[ 436/1311  0s] CopyFiles /Users/<USER>/Documents/GitHub/PerfumerProject/Library/Bee/Android/Prj/IL2CPP/Gradle/unityLibrary/src/main/assets/bin/Data/79459efec17a4d00a321bdcc27bbc385
[ 437/1311  0s] CopyFiles /Users/<USER>/Documents/GitHub/PerfumerProject/Library/Bee/Android/Prj/IL2CPP/Gradle/unityLibrary/src/main/assets/bin/Data/7adcbbe62d6554d81affcab2c56ad5ac
[ 438/1311  0s] CopyFiles /Users/<USER>/Documents/GitHub/PerfumerProject/Library/Bee/Android/Prj/IL2CPP/Gradle/unityLibrary/src/main/assets/bin/Data/79197ecfbc3a4294a89ce589dac02cf2
[ 439/1311  0s] CopyFiles /Users/<USER>/Documents/GitHub/PerfumerProject/Library/Bee/Android/Prj/IL2CPP/Gradle/unityLibrary/src/main/assets/bin/Data/6ba8287b46c1545a789e1b569cad35eb
[ 440/1311  0s] CopyFiles /Users/<USER>/Documents/GitHub/PerfumerProject/Library/Bee/Android/Prj/IL2CPP/Gradle/unityLibrary/src/main/assets/bin/Data/6b342675f8266f54da062a507ec6b7ce
[ 441/1311  0s] CopyFiles /Users/<USER>/Documents/GitHub/PerfumerProject/Library/Bee/Android/Prj/IL2CPP/Gradle/unityLibrary/src/main/assets/bin/Data/64bd3e45f7a714f16b3c213f55f17fea
[ 442/1311  0s] CopyFiles /Users/<USER>/Documents/GitHub/PerfumerProject/Library/Bee/Android/Prj/IL2CPP/Gradle/unityLibrary/src/main/assets/bin/Data/62bf6f91cacca4a4ba2667c52a93f79d
[ 443/1311  0s] CopyFiles /Users/<USER>/Documents/GitHub/PerfumerProject/Library/Bee/Android/Prj/IL2CPP/Gradle/unityLibrary/src/main/assets/bin/Data/62573ea76cbf34643bf28a259a6c901c
[ 444/1311  0s] CopyFiles /Users/<USER>/Documents/GitHub/PerfumerProject/Library/Bee/Android/Prj/IL2CPP/Gradle/unityLibrary/src/main/assets/bin/Data/613b99a82fe95405db46062e1c071434
[ 445/1311  0s] CopyFiles /Users/<USER>/Documents/GitHub/PerfumerProject/Library/Bee/Android/Prj/IL2CPP/Gradle/unityLibrary/src/main/assets/bin/Data/5e7fbc8d4eb714b279eeeef2262c1e1a
[ 446/1311  0s] CopyFiles /Users/<USER>/Documents/GitHub/PerfumerProject/Library/Bee/Android/Prj/IL2CPP/Gradle/unityLibrary/src/main/assets/bin/Data/5d7c3c10b2ea9fe4bb1135079ac25750
[ 447/1311  0s] CopyFiles /Users/<USER>/Documents/GitHub/PerfumerProject/Library/Bee/Android/Prj/IL2CPP/Gradle/unityLibrary/src/main/assets/bin/Data/5a6dad5f26731924dac6a37bd032cdf8
[ 448/1311  0s] CopyFiles /Users/<USER>/Documents/GitHub/PerfumerProject/Library/Bee/Android/Prj/IL2CPP/Gradle/unityLibrary/src/main/assets/bin/Data/54f358fd5713c34479ba58bdded2ff02
[ 449/1311  0s] CopyFiles /Users/<USER>/Documents/GitHub/PerfumerProject/Library/Bee/Android/Prj/IL2CPP/Gradle/unityLibrary/src/main/assets/bin/Data/8e10860aa0b87dd45a0b67a915ca5894
[ 450/1311  0s] CopyFiles /Users/<USER>/Documents/GitHub/PerfumerProject/Library/Bee/Android/Prj/IL2CPP/Gradle/unityLibrary/src/main/assets/bin/Data/7a4c270a5c18f6549a7683844dbf9f22
[ 451/1311  0s] CopyFiles /Users/<USER>/Documents/GitHub/PerfumerProject/Library/Bee/Android/Prj/IL2CPP/Gradle/unityLibrary/src/main/assets/bin/Data/502e0c9b755f34db0b90bf78857d8a1c
[ 452/1311  0s] CopyFiles /Users/<USER>/Documents/GitHub/PerfumerProject/Library/Bee/Android/Prj/IL2CPP/Gradle/unityLibrary/src/main/assets/bin/Data/78bc8db714c3749cc8bd8c0b7e525b85
[ 453/1311  0s] CopyFiles /Users/<USER>/Documents/GitHub/PerfumerProject/Library/Bee/Android/Prj/IL2CPP/Gradle/unityLibrary/src/main/assets/bin/Data/49031c561e16d4fcf91c12153f8e0b25
[ 454/1311  0s] CopyFiles /Users/<USER>/Documents/GitHub/PerfumerProject/Library/Bee/Android/Prj/IL2CPP/Gradle/unityLibrary/src/main/assets/bin/Data/4d55f86cf3b124c8fb1158da26ffa96d
[ 455/1311  0s] CopyFiles /Users/<USER>/Documents/GitHub/PerfumerProject/Library/Bee/Android/Prj/IL2CPP/Gradle/unityLibrary/src/main/assets/bin/Data/48bb5f55d8670e349b6e614913f9d910
[ 456/1311  0s] CopyFiles /Users/<USER>/Documents/GitHub/PerfumerProject/Library/Bee/Android/Prj/IL2CPP/Gradle/unityLibrary/src/main/assets/bin/Data/4507cb7350bbdf647b8ba3d9e87e8be0
[ 457/1311  0s] CopyFiles /Users/<USER>/Documents/GitHub/PerfumerProject/Library/Bee/Android/Prj/IL2CPP/Gradle/unityLibrary/src/main/assets/bin/Data/4442b79fcbcbb4aac97f42d6dc3d4e0b
[ 458/1311  0s] CopyFiles /Users/<USER>/Documents/GitHub/PerfumerProject/Library/Bee/Android/Prj/IL2CPP/Gradle/unityLibrary/src/main/assets/bin/Data/243a94cf6d8dda84f8351f4289587571
[ 459/1311  0s] CopyFiles /Users/<USER>/Documents/GitHub/PerfumerProject/Library/Bee/Android/Prj/IL2CPP/Gradle/unityLibrary/src/main/assets/bin/Data/3ddf9f33ba98e4b31ba4d2b9722bea00
[ 460/1311  0s] CopyFiles /Users/<USER>/Documents/GitHub/PerfumerProject/Library/Bee/Android/Prj/IL2CPP/Gradle/unityLibrary/src/main/assets/bin/Data/48405e3dbda9a4a7694c21c9a2e712e1
[ 461/1311  0s] CopyFiles /Users/<USER>/Documents/GitHub/PerfumerProject/Library/Bee/Android/Prj/IL2CPP/Gradle/unityLibrary/src/main/assets/bin/Data/240416d45df4c6c4daec987220246861
[ 462/1311  0s] CopyFiles /Users/<USER>/Documents/GitHub/PerfumerProject/Library/Bee/Android/Prj/IL2CPP/Gradle/unityLibrary/src/main/assets/bin/Data/3e8abceb566c0c547bb0f184438ffe40
[ 463/1311  0s] CopyFiles /Users/<USER>/Documents/GitHub/PerfumerProject/Library/Bee/Android/Prj/IL2CPP/Gradle/unityLibrary/src/main/assets/bin/Data/3f5b5dff67a942289a9defa416b206f3
[ 464/1311  0s] CopyFiles /Users/<USER>/Documents/GitHub/PerfumerProject/Library/Bee/Android/Prj/IL2CPP/Gradle/unityLibrary/src/main/assets/bin/Data/22eb050bacf8c401ca8503e24bff3570
[ 465/1311  0s] CopyFiles /Users/<USER>/Documents/GitHub/PerfumerProject/Library/Bee/Android/Prj/IL2CPP/Gradle/unityLibrary/src/main/assets/bin/Data/1b656a189e6154422a74e05a56c3f245
[ 466/1311  0s] CopyFiles /Users/<USER>/Documents/GitHub/PerfumerProject/Library/Bee/Android/Prj/IL2CPP/Gradle/unityLibrary/src/main/assets/bin/Data/226248ac6f184e448af731df91b91958
[ 467/1311  0s] CopyFiles /Users/<USER>/Documents/GitHub/PerfumerProject/Library/Bee/Android/Prj/IL2CPP/Gradle/unityLibrary/src/main/assets/bin/Data/1e3b057af24249748ff873be7fafee47
[ 468/1311  0s] CopyFiles /Users/<USER>/Documents/GitHub/PerfumerProject/Library/Bee/Android/Prj/IL2CPP/Gradle/unityLibrary/src/main/assets/bin/Data/128e987d567d4e2c824d754223b3f3b0
[ 469/1311  0s] CopyFiles /Users/<USER>/Documents/GitHub/PerfumerProject/Library/Bee/Android/Prj/IL2CPP/Gradle/unityLibrary/src/main/assets/bin/Data/044bd16cdbd87f844af165a4c03076c7
[ 470/1311  0s] CopyFiles /Users/<USER>/Documents/GitHub/PerfumerProject/Library/Bee/Android/Prj/IL2CPP/Gradle/unityLibrary/src/main/assets/bin/Data/0e6bcf37a2876432fa58eff8888bf177
[ 471/1311  0s] CopyFiles /Users/<USER>/Documents/GitHub/PerfumerProject/Library/Bee/Android/Prj/IL2CPP/Gradle/unityLibrary/src/main/assets/bin/Data/0000000000000000f000000000000000
[ 472/1311  0s] CopyFiles /Users/<USER>/Documents/GitHub/PerfumerProject/Library/Bee/Android/Prj/IL2CPP/Gradle/unityLibrary/src/main/assets/bin/Data/05e8903e53b464591b902273f0a3cf3d
[ 473/1311  0s] CopyFiles /Users/<USER>/Documents/GitHub/PerfumerProject/Library/Bee/Android/Prj/IL2CPP/Gradle/unityLibrary/src/main/assets/bin/Data/02e2405df2df8df4280185fd69b76976
[ 474/1311  0s] AddBootConfigGUID Library/Bee/artifacts/Android/boot.config
[ 475/1311  0s] CopyFiles /Users/<USER>/Documents/GitHub/PerfumerProject/Library/Bee/Android/Prj/IL2CPP/Gradle/unityLibrary/src/main/assets/bin/Data/boot.config
[ 476/1311  0s] ActionGenerateProjectFiles /Users/<USER>/Documents/GitHub/PerfumerProject/Library/Bee/Android/Prj/IL2CPP/Gradle/build.gradle (+7 others)
[ 477/1311  5s] UnityLinker /Users/<USER>/Documents/GitHub/PerfumerProject/Library/Bee/artifacts/unitylinker_xy1a.traceevents
/Applications/Unity/Hub/Editor/2022.3.62f1/Unity.app/Contents/il2cpp/build/deploy/UnityLinker --allowed-assembly=/Applications/Unity/Hub/Editor/2022.3.62f1/PlaybackEngines/AndroidPlayer/Variations/il2cpp/Managed/UnityEngine.AIModule.dll --allowed-assembly=/Applications/Unity/Hub/Editor/2022.3.62f1/PlaybackEngines/AndroidPlayer/Variations/il2cpp/Managed/UnityEngine.ARModule.dll --allowed-assembly=/Applications/Unity/Hub/Editor/2022.3.62f1/PlaybackEngines/AndroidPlayer/Variations/il2cpp/Managed/UnityEngine.AccessibilityModule.dll --allowed-assembly=/Applications/Unity/Hub/Editor/2022.3.62f1/PlaybackEngines/AndroidPlayer/Variations/il2cpp/Managed/UnityEngine.AndroidJNIModule.dll --allowed-assembly=/Applications/Unity/Hub/Editor/2022.3.62f1/PlaybackEngines/AndroidPlayer/Variations/il2cpp/Managed/UnityEngine.AnimationModule.dll --allowed-assembly=/Applications/Unity/Hub/Editor/2022.3.62f1/PlaybackEngines/AndroidPlayer/Variations/il2cpp/Managed/UnityEngine.AssetBundleModule.dll --allowed-assembly=/Applications/Unity/Hub/Editor/2022.3.62f1/PlaybackEngines/AndroidPlayer/Variations/il2cpp/Managed/UnityEngine.AudioModule.dll --allowed-assembly=/Applications/Unity/Hub/Editor/2022.3.62f1/PlaybackEngines/AndroidPlayer/Variations/il2cpp/Managed/UnityEngine.ClothModule.dll --allowed-assembly=/Applications/Unity/Hub/Editor/2022.3.62f1/PlaybackEngines/AndroidPlayer/Variations/il2cpp/Managed/UnityEngine.ContentLoadModule.dll --allowed-assembly=/Applications/Unity/Hub/Editor/2022.3.62f1/PlaybackEngines/AndroidPlayer/Variations/il2cpp/Managed/UnityEngine.CoreModule.dll --allowed-assembly=/Applications/Unity/Hub/Editor/2022.3.62f1/PlaybackEngines/AndroidPlayer/Variations/il2cpp/Managed/UnityEngine.CrashReportingModule.dll --allowed-assembly=/Applications/Unity/Hub/Editor/2022.3.62f1/PlaybackEngines/AndroidPlayer/Variations/il2cpp/Managed/UnityEngine.DSPGraphModule.dll --allowed-assembly=/Applications/Unity/Hub/Editor/2022.3.62f1/PlaybackEngines/AndroidPlayer/Variations/il2cpp/Managed/UnityEngine.DirectorModule.dll --allowed-assembly=/Applications/Unity/Hub/Editor/2022.3.62f1/PlaybackEngines/AndroidPlayer/Variations/il2cpp/Managed/UnityEngine.GIModule.dll --allowed-assembly=/Applications/Unity/Hub/Editor/2022.3.62f1/PlaybackEngines/AndroidPlayer/Variations/il2cpp/Managed/UnityEngine.GameCenterModule.dll --allowed-assembly=/Applications/Unity/Hub/Editor/2022.3.62f1/PlaybackEngines/AndroidPlayer/Variations/il2cpp/Managed/UnityEngine.GridModule.dll --allowed-assembly=/Applications/Unity/Hub/Editor/2022.3.62f1/PlaybackEngines/AndroidPlayer/Variations/il2cpp/Managed/UnityEngine.HotReloadModule.dll --allowed-assembly=/Applications/Unity/Hub/Editor/2022.3.62f1/PlaybackEngines/AndroidPlayer/Variations/il2cpp/Managed/UnityEngine.IMGUIModule.dll --allowed-assembly=/Applications/Unity/Hub/Editor/2022.3.62f1/PlaybackEngines/AndroidPlayer/Variations/il2cpp/Managed/UnityEngine.ImageConversionModule.dll --allowed-assembly=/Applications/Unity/Hub/Editor/2022.3.62f1/PlaybackEngines/AndroidPlayer/Variations/il2cpp/Managed/UnityEngine.InputLegacyModule.dll --allowed-assembly=/Applications/Unity/Hub/Editor/2022.3.62f1/PlaybackEngines/AndroidPlayer/Variations/il2cpp/Managed/UnityEngine.InputModule.dll --allowed-assembly=/Applications/Unity/Hub/Editor/2022.3.62f1/PlaybackEngines/AndroidPlayer/Variations/il2cpp/Managed/UnityEngine.JSONSerializeModule.dll --allowed-assembly=/Applications/Unity/Hub/Editor/2022.3.62f1/PlaybackEngines/AndroidPlayer/Variations/il2cpp/Managed/UnityEngine.LocalizationModule.dll --allowed-assembly=/Applications/Unity/Hub/Editor/2022.3.62f1/PlaybackEngines/AndroidPlayer/Variations/il2cpp/Managed/UnityEngine.ParticleSystemModule.dll --allowed-assembly=/Applications/Unity/Hub/Editor/2022.3.62f1/PlaybackEngines/AndroidPlayer/Variations/il2cpp/Managed/UnityEngine.PerformanceReportingModule.dll --allowed-assembly=/Applications/Unity/Hub/Editor/2022.3.62f1/PlaybackEngines/AndroidPlayer/Variations/il2cpp/Managed/UnityEngine.Physics2DModule.dll --allowed-assembly=/Applications/Unity/Hub/Editor/2022.3.62f1/PlaybackEngines/AndroidPlayer/Variations/il2cpp/Managed/UnityEngine.PhysicsModule.dll --allowed-assembly=/Applications/Unity/Hub/Editor/2022.3.62f1/PlaybackEngines/AndroidPlayer/Variations/il2cpp/Managed/UnityEngine.ProfilerModule.dll --allowed-assembly=/Applications/Unity/Hub/Editor/2022.3.62f1/PlaybackEngines/AndroidPlayer/Variations/il2cpp/Managed/UnityEngine.PropertiesModule.dll --allowed-assembly=/Applications/Unity/Hub/Editor/2022.3.62f1/PlaybackEngines/AndroidPlayer/Variations/il2cpp/Managed/UnityEngine.RuntimeInitializeOnLoadManagerInitializerModule.dll --allowed-assembly=/Applications/Unity/Hub/Editor/2022.3.62f1/PlaybackEngines/AndroidPlayer/Variations/il2cpp/Managed/UnityEngine.ScreenCaptureModule.dll --allowed-assembly=/Applications/Unity/Hub/Editor/2022.3.62f1/PlaybackEngines/AndroidPlayer/Variations/il2cpp/Managed/UnityEngine.SharedInternalsModule.dll --allowed-assembly=/Applications/Unity/Hub/Editor/2022.3.62f1/PlaybackEngines/AndroidPlayer/Variations/il2cpp/Managed/UnityEngine.SpriteMaskModule.dll --allowed-assembly=/Applications/Unity/Hub/Editor/2022.3.62f1/PlaybackEngines/AndroidPlayer/Variations/il2cpp/Managed/UnityEngine.SpriteShapeModule.dll --allowed-assembly=/Applications/Unity/Hub/Editor/2022.3.62f1/PlaybackEngines/AndroidPlayer/Variations/il2cpp/Managed/UnityEngine.StreamingModule.dll --allowed-assembly=/Applications/Unity/Hub/Editor/2022.3.62f1/PlaybackEngines/AndroidPlayer/Variations/il2cpp/Managed/UnityEngine.SubstanceModule.dll --allowed-assembly=/Applications/Unity/Hub/Editor/2022.3.62f1/PlaybackEngines/AndroidPlayer/Variations/il2cpp/Managed/UnityEngine.SubsystemsModule.dll --allowed-assembly=/Applications/Unity/Hub/Editor/2022.3.62f1/PlaybackEngines/AndroidPlayer/Variations/il2cpp/Managed/UnityEngine.TLSModule.dll --allowed-assembly=/Applications/Unity/Hub/Editor/2022.3.62f1/PlaybackEngines/AndroidPlayer/Variations/il2cpp/Managed/UnityEngine.TerrainModule.dll --allowed-assembly=/Applications/Unity/Hub/Editor/2022.3.62f1/PlaybackEngines/AndroidPlayer/Variations/il2cpp/Managed/UnityEngine.TerrainPhysicsModule.dll --allowed-assembly=/Applications/Unity/Hub/Editor/2022.3.62f1/PlaybackEngines/AndroidPlayer/Variations/il2cpp/Managed/UnityEngine.TextCoreFontEngineModule.dll --allowed-assembly=/Applications/Unity/Hub/Editor/2022.3.62f1/PlaybackEngines/AndroidPlayer/Variations/il2cpp/Managed/UnityEngine.TextCoreTextEngineModule.dll --allowed-assembly=/Applications/Unity/Hub/Editor/2022.3.62f1/PlaybackEngines/AndroidPlayer/Variations/il2cpp/Managed/UnityEngine.TextRenderingModule.dll --allowed-assembly=/Applications/Unity/Hub/Editor/2022.3.62f1/PlaybackEngines/AndroidPlayer/Variations/il2cpp/Managed/UnityEngine.TilemapModule.dll --allowed-assembly=/Applications/Unity/Hub/Editor/2022.3.62f1/PlaybackEngines/AndroidPlayer/Variations/il2cpp/Managed/UnityEngine.UIElementsModule.dll --allowed-assembly=/Applications/Unity/Hub/Editor/2022.3.62f1/PlaybackEngines/AndroidPlayer/Variations/il2cpp/Managed/UnityEngine.UIModule.dll --allowed-assembly=/Applications/Unity/Hub/Editor/2022.3.62f1/PlaybackEngines/AndroidPlayer/Variations/il2cpp/Managed/UnityEngine.UmbraModule.dll --allowed-assembly=/Applications/Unity/Hub/Editor/2022.3.62f1/PlaybackEngines/AndroidPlayer/Variations/il2cpp/Managed/UnityEngine.UnityAnalyticsCommonModule.dll --allowed-assembly=/Applications/Unity/Hub/Editor/2022.3.62f1/PlaybackEngines/AndroidPlayer/Variations/il2cpp/Managed/UnityEngine.UnityAnalyticsModule.dll --allowed-assembly=/Applications/Unity/Hub/Editor/2022.3.62f1/PlaybackEngines/AndroidPlayer/Variations/il2cpp/Managed/UnityEngine.UnityConnectModule.dll --allowed-assembly=/Applications/Unity/Hub/Editor/2022.3.62f1/PlaybackEngines/AndroidPlayer/Variations/il2cpp/Managed/UnityEngine.UnityCurlModule.dll --allowed-assembly=/Applications/Unity/Hub/Editor/2022.3.62f1/PlaybackEngines/AndroidPlayer/Variations/il2cpp/Managed/UnityEngine.UnityTestProtocolModule.dll --allowed-assembly=/Applications/Unity/Hub/Editor/2022.3.62f1/PlaybackEngines/AndroidPlayer/Variations/il2cpp/Managed/UnityEngine.UnityWebRequestAssetBundleModule.dll --allowed-assembly=/Applications/Unity/Hub/Editor/2022.3.62f1/PlaybackEngines/AndroidPlayer/Variations/il2cpp/Managed/UnityEngine.UnityWebRequestAudioModule.dll --allowed-assembly=/Applications/Unity/Hub/Editor/2022.3.62f1/PlaybackEngines/AndroidPlayer/Variations/il2cpp/Managed/UnityEngine.UnityWebRequestModule.dll --allowed-assembly=/Applications/Unity/Hub/Editor/2022.3.62f1/PlaybackEngines/AndroidPlayer/Variations/il2cpp/Managed/UnityEngine.UnityWebRequestTextureModule.dll --allowed-assembly=/Applications/Unity/Hub/Editor/2022.3.62f1/PlaybackEngines/AndroidPlayer/Variations/il2cpp/Managed/UnityEngine.UnityWebRequestWWWModule.dll --allowed-assembly=/Applications/Unity/Hub/Editor/2022.3.62f1/PlaybackEngines/AndroidPlayer/Variations/il2cpp/Managed/UnityEngine.VFXModule.dll --allowed-assembly=/Applications/Unity/Hub/Editor/2022.3.62f1/PlaybackEngines/AndroidPlayer/Variations/il2cpp/Managed/UnityEngine.VRModule.dll --allowed-assembly=/Applications/Unity/Hub/Editor/2022.3.62f1/PlaybackEngines/AndroidPlayer/Variations/il2cpp/Managed/UnityEngine.VehiclesModule.dll --allowed-assembly=/Applications/Unity/Hub/Editor/2022.3.62f1/PlaybackEngines/AndroidPlayer/Variations/il2cpp/Managed/UnityEngine.VideoModule.dll --allowed-assembly=/Applications/Unity/Hub/Editor/2022.3.62f1/PlaybackEngines/AndroidPlayer/Variations/il2cpp/Managed/UnityEngine.WindModule.dll --allowed-assembly=/Applications/Unity/Hub/Editor/2022.3.62f1/PlaybackEngines/AndroidPlayer/Variations/il2cpp/Managed/UnityEngine.XRModule.dll --allowed-assembly=/Applications/Unity/Hub/Editor/2022.3.62f1/PlaybackEngines/AndroidPlayer/Variations/il2cpp/Managed/UnityEngine.dll --allowed-assembly=/Users/<USER>/Documents/GitHub/PerfumerProject/Library/Bee/PlayerScriptAssemblies/Assembly-CSharp-firstpass.dll --allowed-assembly=/Users/<USER>/Documents/GitHub/PerfumerProject/Library/Bee/PlayerScriptAssemblies/Assembly-CSharp.dll --allowed-assembly=/Users/<USER>/Documents/GitHub/PerfumerProject/Library/Bee/PlayerScriptAssemblies/Unity.VisualScripting.Core.dll --allowed-assembly=/Users/<USER>/Documents/GitHub/PerfumerProject/Library/Bee/PlayerScriptAssemblies/UnityEngine.UI.dll --allowed-assembly=/Users/<USER>/Documents/GitHub/PerfumerProject/Library/Bee/PlayerScriptAssemblies/Unity.VisualScripting.Flow.dll --allowed-assembly=/Users/<USER>/Documents/GitHub/PerfumerProject/Library/Bee/PlayerScriptAssemblies/Unity.Timeline.dll --allowed-assembly=/Users/<USER>/Documents/GitHub/PerfumerProject/Library/Bee/PlayerScriptAssemblies/Unity.InputSystem.dll --allowed-assembly=/Users/<USER>/Documents/GitHub/PerfumerProject/Library/Bee/PlayerScriptAssemblies/Unity.InputSystem.ForUI.dll --allowed-assembly=/Users/<USER>/Documents/GitHub/PerfumerProject/Library/Bee/PlayerScriptAssemblies/NavMeshPlus.dll --allowed-assembly=/Users/<USER>/Documents/GitHub/PerfumerProject/Library/Bee/PlayerScriptAssemblies/Unity.TextMeshPro.dll --allowed-assembly=/Users/<USER>/Documents/GitHub/PerfumerProject/Library/Bee/PlayerScriptAssemblies/Unity.VisualScripting.State.dll --allowed-assembly=/Users/<USER>/Documents/GitHub/PerfumerProject/Library/Bee/PlayerScriptAssemblies/Fungus.dll --allowed-assembly=/Users/<USER>/Documents/GitHub/PerfumerProject/Assets/Plugins/Demigiant/DemiLib/Core/DemiLib.dll --allowed-assembly=/Users/<USER>/Documents/GitHub/PerfumerProject/Assets/Plugins/Demigiant/DOTween/DOTween.dll --allowed-assembly=/Users/<USER>/Documents/GitHub/PerfumerProject/Assets/FlexReader/ICSharpCode.SharpZipLib.dll --allowed-assembly=/Users/<USER>/Documents/GitHub/PerfumerProject/Library/PackageCache/com.unity.visualscripting@1.9.4/Runtime/VisualScripting.Flow/Dependencies/NCalc/Unity.VisualScripting.Antlr3.Runtime.dll --allowed-assembly=/Users/<USER>/Documents/GitHub/PerfumerProject/Assets/Plugins/Demigiant/DOTweenPro/DOTweenPro.dll --allowed-assembly=/Users/<USER>/Documents/GitHub/PerfumerProject/Library/PackageCache/com.unity.nuget.newtonsoft-json@3.2.1/Runtime/AOT/Newtonsoft.Json.dll --allowed-assembly=/Applications/Unity/Hub/Editor/2022.3.62f1/Unity.app/Contents/MonoBleedingEdge/lib/mono/unityaot-linux/Accessibility.dll --allowed-assembly=/Applications/Unity/Hub/Editor/2022.3.62f1/Unity.app/Contents/MonoBleedingEdge/lib/mono/unityaot-linux/Facades/Microsoft.Win32.Primitives.dll --allowed-assembly=/Applications/Unity/Hub/Editor/2022.3.62f1/Unity.app/Contents/MonoBleedingEdge/lib/mono/unityaot-linux/Facades/Microsoft.Win32.Registry.AccessControl.dll --allowed-assembly=/Applications/Unity/Hub/Editor/2022.3.62f1/Unity.app/Contents/MonoBleedingEdge/lib/mono/unityaot-linux/Facades/Microsoft.Win32.Registry.dll --allowed-assembly=/Applications/Unity/Hub/Editor/2022.3.62f1/Unity.app/Contents/MonoBleedingEdge/lib/mono/unityaot-linux/Facades/System.AppContext.dll --allowed-assembly=/Applications/Unity/Hub/Editor/2022.3.62f1/Unity.app/Contents/MonoBleedingEdge/lib/mono/unityaot-linux/Facades/System.Buffers.dll --allowed-assembly=/Applications/Unity/Hub/Editor/2022.3.62f1/Unity.app/Contents/MonoBleedingEdge/lib/mono/unityaot-linux/Facades/System.Collections.Concurrent.dll --allowed-assembly=/Applications/Unity/Hub/Editor/2022.3.62f1/Unity.app/Contents/MonoBleedingEdge/lib/mono/unityaot-linux/Facades/System.Collections.NonGeneric.dll --allowed-assembly=/Applications/Unity/Hub/Editor/2022.3.62f1/Unity.app/Contents/MonoBleedingEdge/lib/mono/unityaot-linux/Facades/System.Collections.Specialized.dll --allowed-assembly=/Applications/Unity/Hub/Editor/2022.3.62f1/Unity.app/Contents/MonoBleedingEdge/lib/mono/unityaot-linux/Facades/System.Collections.dll --allowed-assembly=/Applications/Unity/Hub/Editor/2022.3.62f1/Unity.app/Contents/MonoBleedingEdge/lib/mono/unityaot-linux/Facades/System.ComponentModel.Annotations.dll --allowed-assembly=/Applications/Unity/Hub/Editor/2022.3.62f1/Unity.app/Contents/MonoBleedingEdge/lib/mono/unityaot-linux/Facades/System.ComponentModel.EventBasedAsync.dll --allowed-assembly=/Applications/Unity/Hub/Editor/2022.3.62f1/Unity.app/Contents/MonoBleedingEdge/lib/mono/unityaot-linux/Facades/System.ComponentModel.Primitives.dll --allowed-assembly=/Applications/Unity/Hub/Editor/2022.3.62f1/Unity.app/Contents/MonoBleedingEdge/lib/mono/unityaot-linux/Facades/System.ComponentModel.TypeConverter.dll --allowed-assembly=/Applications/Unity/Hub/Editor/2022.3.62f1/Unity.app/Contents/MonoBleedingEdge/lib/mono/unityaot-linux/Facades/System.ComponentModel.dll --allowed-assembly=/Applications/Unity/Hub/Editor/2022.3.62f1/Unity.app/Contents/MonoBleedingEdge/lib/mono/unityaot-linux/Facades/System.Console.dll --allowed-assembly=/Applications/Unity/Hub/Editor/2022.3.62f1/Unity.app/Contents/MonoBleedingEdge/lib/mono/unityaot-linux/Facades/System.Data.Common.dll --allowed-assembly=/Applications/Unity/Hub/Editor/2022.3.62f1/Unity.app/Contents/MonoBleedingEdge/lib/mono/unityaot-linux/Facades/System.Data.SqlClient.dll --allowed-assembly=/Applications/Unity/Hub/Editor/2022.3.62f1/Unity.app/Contents/MonoBleedingEdge/lib/mono/unityaot-linux/Facades/System.Diagnostics.Contracts.dll --allowed-assembly=/Applications/Unity/Hub/Editor/2022.3.62f1/Unity.app/Contents/MonoBleedingEdge/lib/mono/unityaot-linux/Facades/System.Diagnostics.Debug.dll --allowed-assembly=/Applications/Unity/Hub/Editor/2022.3.62f1/Unity.app/Contents/MonoBleedingEdge/lib/mono/unityaot-linux/Facades/System.Diagnostics.FileVersionInfo.dll --allowed-assembly=/Applications/Unity/Hub/Editor/2022.3.62f1/Unity.app/Contents/MonoBleedingEdge/lib/mono/unityaot-linux/Facades/System.Diagnostics.Process.dll --allowed-assembly=/Applications/Unity/Hub/Editor/2022.3.62f1/Unity.app/Contents/MonoBleedingEdge/lib/mono/unityaot-linux/Facades/System.Diagnostics.StackTrace.dll --allowed-assembly=/Applications/Unity/Hub/Editor/2022.3.62f1/Unity.app/Contents/MonoBleedingEdge/lib/mono/unityaot-linux/Facades/System.Diagnostics.TextWriterTraceListener.dll --allowed-assembly=/Applications/Unity/Hub/Editor/2022.3.62f1/Unity.app/Contents/MonoBleedingEdge/lib/mono/unityaot-linux/Facades/System.Diagnostics.Tools.dll --allowed-assembly=/Applications/Unity/Hub/Editor/2022.3.62f1/Unity.app/Contents/MonoBleedingEdge/lib/mono/unityaot-linux/Facades/System.Diagnostics.TraceEvent.dll --allowed-assembly=/Applications/Unity/Hub/Editor/2022.3.62f1/Unity.app/Contents/MonoBleedingEdge/lib/mono/unityaot-linux/Facades/System.Diagnostics.TraceSource.dll --allowed-assembly=/Applications/Unity/Hub/Editor/2022.3.62f1/Unity.app/Contents/MonoBleedingEdge/lib/mono/unityaot-linux/Facades/System.Diagnostics.Tracing.dll --allowed-assembly=/Applications/Unity/Hub/Editor/2022.3.62f1/Unity.app/Contents/MonoBleedingEdge/lib/mono/unityaot-linux/Facades/System.Drawing.Primitives.dll --allowed-assembly=/Applications/Unity/Hub/Editor/2022.3.62f1/Unity.app/Contents/MonoBleedingEdge/lib/mono/unityaot-linux/Facades/System.Dynamic.Runtime.dll --allowed-assembly=/Applications/Unity/Hub/Editor/2022.3.62f1/Unity.app/Contents/MonoBleedingEdge/lib/mono/unityaot-linux/Facades/System.Globalization.Calendars.dll --allowed-assembly=/Applications/Unity/Hub/Editor/2022.3.62f1/Unity.app/Contents/MonoBleedingEdge/lib/mono/unityaot-linux/Facades/System.Globalization.Extensions.dll --allowed-assembly=/Applications/Unity/Hub/Editor/2022.3.62f1/Unity.app/Contents/MonoBleedingEdge/lib/mono/unityaot-linux/Facades/System.Globalization.dll --allowed-assembly=/Applications/Unity/Hub/Editor/2022.3.62f1/Unity.app/Contents/MonoBleedingEdge/lib/mono/unityaot-linux/Facades/System.IO.Compression.ZipFile.dll --allowed-assembly=/Applications/Unity/Hub/Editor/2022.3.62f1/Unity.app/Contents/MonoBleedingEdge/lib/mono/unityaot-linux/Facades/System.IO.FileSystem.AccessControl.dll --allowed-assembly=/Applications/Unity/Hub/Editor/2022.3.62f1/Unity.app/Contents/MonoBleedingEdge/lib/mono/unityaot-linux/Facades/System.IO.FileSystem.DriveInfo.dll --allowed-assembly=/Applications/Unity/Hub/Editor/2022.3.62f1/Unity.app/Contents/MonoBleedingEdge/lib/mono/unityaot-linux/Facades/System.IO.FileSystem.Primitives.dll --allowed-assembly=/Applications/Unity/Hub/Editor/2022.3.62f1/Unity.app/Contents/MonoBleedingEdge/lib/mono/unityaot-linux/Facades/System.IO.FileSystem.Watcher.dll --allowed-assembly=/Applications/Unity/Hub/Editor/2022.3.62f1/Unity.app/Contents/MonoBleedingEdge/lib/mono/unityaot-linux/Facades/System.IO.FileSystem.dll --allowed-assembly=/Applications/Unity/Hub/Editor/2022.3.62f1/Unity.app/Contents/MonoBleedingEdge/lib/mono/unityaot-linux/Facades/System.IO.IsolatedStorage.dll --allowed-assembly=/Applications/Unity/Hub/Editor/2022.3.62f1/Unity.app/Contents/MonoBleedingEdge/lib/mono/unityaot-linux/Facades/System.IO.MemoryMappedFiles.dll --allowed-assembly=/Applications/Unity/Hub/Editor/2022.3.62f1/Unity.app/Contents/MonoBleedingEdge/lib/mono/unityaot-linux/Facades/System.IO.Pipes.dll --allowed-assembly=/Applications/Unity/Hub/Editor/2022.3.62f1/Unity.app/Contents/MonoBleedingEdge/lib/mono/unityaot-linux/Facades/System.IO.UnmanagedMemoryStream.dll --allowed-assembly=/Applications/Unity/Hub/Editor/2022.3.62f1/Unity.app/Contents/MonoBleedingEdge/lib/mono/unityaot-linux/Facades/System.IO.dll --allowed-assembly=/Applications/Unity/Hub/Editor/2022.3.62f1/Unity.app/Contents/MonoBleedingEdge/lib/mono/unityaot-linux/Facades/System.Linq.Expressions.dll --allowed-assembly=/Applications/Unity/Hub/Editor/2022.3.62f1/Unity.app/Contents/MonoBleedingEdge/lib/mono/unityaot-linux/Facades/System.Linq.Parallel.dll --allowed-assembly=/Applications/Unity/Hub/Editor/2022.3.62f1/Unity.app/Contents/MonoBleedingEdge/lib/mono/unityaot-linux/Facades/System.Linq.Queryable.dll --allowed-assembly=/Applications/Unity/Hub/Editor/2022.3.62f1/Unity.app/Contents/MonoBleedingEdge/lib/mono/unityaot-linux/Facades/System.Linq.dll --allowed-assembly=/Applications/Unity/Hub/Editor/2022.3.62f1/Unity.app/Contents/MonoBleedingEdge/lib/mono/unityaot-linux/Facades/System.Memory.dll --allowed-assembly=/Applications/Unity/Hub/Editor/2022.3.62f1/Unity.app/Contents/MonoBleedingEdge/lib/mono/unityaot-linux/Facades/System.Net.AuthenticationManager.dll --allowed-assembly=/Applications/Unity/Hub/Editor/2022.3.62f1/Unity.app/Contents/MonoBleedingEdge/lib/mono/unityaot-linux/Facades/System.Net.Cache.dll --allowed-assembly=/Applications/Unity/Hub/Editor/2022.3.62f1/Unity.app/Contents/MonoBleedingEdge/lib/mono/unityaot-linux/Facades/System.Net.HttpListener.dll --allowed-assembly=/Applications/Unity/Hub/Editor/2022.3.62f1/Unity.app/Contents/MonoBleedingEdge/lib/mono/unityaot-linux/Facades/System.Net.Mail.dll --allowed-assembly=/Applications/Unity/Hub/Editor/2022.3.62f1/Unity.app/Contents/MonoBleedingEdge/lib/mono/unityaot-linux/Facades/System.Net.NameResolution.dll --allowed-assembly=/Applications/Unity/Hub/Editor/2022.3.62f1/Unity.app/Contents/MonoBleedingEdge/lib/mono/unityaot-linux/Facades/System.Net.NetworkInformation.dll --allowed-assembly=/Applications/Unity/Hub/Editor/2022.3.62f1/Unity.app/Contents/MonoBleedingEdge/lib/mono/unityaot-linux/Facades/System.Net.Ping.dll --allowed-assembly=/Applications/Unity/Hub/Editor/2022.3.62f1/Unity.app/Contents/MonoBleedingEdge/lib/mono/unityaot-linux/Facades/System.Net.Primitives.dll --allowed-assembly=/Applications/Unity/Hub/Editor/2022.3.62f1/Unity.app/Contents/MonoBleedingEdge/lib/mono/unityaot-linux/Facades/System.Net.Requests.dll --allowed-assembly=/Applications/Unity/Hub/Editor/2022.3.62f1/Unity.app/Contents/MonoBleedingEdge/lib/mono/unityaot-linux/Facades/System.Net.Security.dll --allowed-assembly=/Applications/Unity/Hub/Editor/2022.3.62f1/Unity.app/Contents/MonoBleedingEdge/lib/mono/unityaot-linux/Facades/System.Net.ServicePoint.dll --allowed-assembly=/Applications/Unity/Hub/Editor/2022.3.62f1/Unity.app/Contents/MonoBleedingEdge/lib/mono/unityaot-linux/Facades/System.Net.Sockets.dll --allowed-assembly=/Applications/Unity/Hub/Editor/2022.3.62f1/Unity.app/Contents/MonoBleedingEdge/lib/mono/unityaot-linux/Facades/System.Net.Utilities.dll --allowed-assembly=/Applications/Unity/Hub/Editor/2022.3.62f1/Unity.app/Contents/MonoBleedingEdge/lib/mono/unityaot-linux/Facades/System.Net.WebHeaderCollection.dll --allowed-assembly=/Applications/Unity/Hub/Editor/2022.3.62f1/Unity.app/Contents/MonoBleedingEdge/lib/mono/unityaot-linux/Facades/System.Net.WebSockets.Client.dll --allowed-assembly=/Applications/Unity/Hub/Editor/2022.3.62f1/Unity.app/Contents/MonoBleedingEdge/lib/mono/unityaot-linux/Facades/System.Net.WebSockets.dll --allowed-assembly=/Applications/Unity/Hub/Editor/2022.3.62f1/Unity.app/Contents/MonoBleedingEdge/lib/mono/unityaot-linux/Facades/System.ObjectModel.dll --allowed-assembly=/Applications/Unity/Hub/Editor/2022.3.62f1/Unity.app/Contents/MonoBleedingEdge/lib/mono/unityaot-linux/Facades/System.Reflection.DispatchProxy.dll --allowed-assembly=/Applications/Unity/Hub/Editor/2022.3.62f1/Unity.app/Contents/MonoBleedingEdge/lib/mono/unityaot-linux/Facades/System.Reflection.Emit.ILGeneration.dll --allowed-assembly=/Applications/Unity/Hub/Editor/2022.3.62f1/Unity.app/Contents/MonoBleedingEdge/lib/mono/unityaot-linux/Facades/System.Reflection.Emit.Lightweight.dll --allowed-assembly=/Applications/Unity/Hub/Editor/2022.3.62f1/Unity.app/Contents/MonoBleedingEdge/lib/mono/unityaot-linux/Facades/System.Reflection.Emit.dll --allowed-assembly=/Applications/Unity/Hub/Editor/2022.3.62f1/Unity.app/Contents/MonoBleedingEdge/lib/mono/unityaot-linux/Facades/System.Reflection.Extensions.dll --allowed-assembly=/Applications/Unity/Hub/Editor/2022.3.62f1/Unity.app/Contents/MonoBleedingEdge/lib/mono/unityaot-linux/Facades/System.Reflection.Primitives.dll --allowed-assembly=/Applications/Unity/Hub/Editor/2022.3.62f1/Unity.app/Contents/MonoBleedingEdge/lib/mono/unityaot-linux/Facades/System.Reflection.TypeExtensions.dll --allowed-assembly=/Applications/Unity/Hub/Editor/2022.3.62f1/Unity.app/Contents/MonoBleedingEdge/lib/mono/unityaot-linux/Facades/System.Reflection.dll --allowed-assembly=/Applications/Unity/Hub/Editor/2022.3.62f1/Unity.app/Contents/MonoBleedingEdge/lib/mono/unityaot-linux/Facades/System.Resources.Reader.dll --allowed-assembly=/Applications/Unity/Hub/Editor/2022.3.62f1/Unity.app/Contents/MonoBleedingEdge/lib/mono/unityaot-linux/Facades/System.Resources.ReaderWriter.dll --allowed-assembly=/Applications/Unity/Hub/Editor/2022.3.62f1/Unity.app/Contents/MonoBleedingEdge/lib/mono/unityaot-linux/Facades/System.Resources.ResourceManager.dll --allowed-assembly=/Applications/Unity/Hub/Editor/2022.3.62f1/Unity.app/Contents/MonoBleedingEdge/lib/mono/unityaot-linux/Facades/System.Resources.Writer.dll --allowed-assembly=/Applications/Unity/Hub/Editor/2022.3.62f1/Unity.app/Contents/MonoBleedingEdge/lib/mono/unityaot-linux/Facades/System.Runtime.CompilerServices.VisualC.dll --allowed-assembly=/Applications/Unity/Hub/Editor/2022.3.62f1/Unity.app/Contents/MonoBleedingEdge/lib/mono/unityaot-linux/Facades/System.Runtime.Extensions.dll --allowed-assembly=/Applications/Unity/Hub/Editor/2022.3.62f1/Unity.app/Contents/MonoBleedingEdge/lib/mono/unityaot-linux/Facades/System.Runtime.Handles.dll --allowed-assembly=/Applications/Unity/Hub/Editor/2022.3.62f1/Unity.app/Contents/MonoBleedingEdge/lib/mono/unityaot-linux/Facades/System.Runtime.InteropServices.RuntimeInformation.dll --allowed-assembly=/Applications/Unity/Hub/Editor/2022.3.62f1/Unity.app/Contents/MonoBleedingEdge/lib/mono/unityaot-linux/Facades/System.Runtime.InteropServices.WindowsRuntime.dll --allowed-assembly=/Applications/Unity/Hub/Editor/2022.3.62f1/Unity.app/Contents/MonoBleedingEdge/lib/mono/unityaot-linux/Facades/System.Runtime.InteropServices.dll --allowed-assembly=/Applications/Unity/Hub/Editor/2022.3.62f1/Unity.app/Contents/MonoBleedingEdge/lib/mono/unityaot-linux/Facades/System.Runtime.Loader.dll --allowed-assembly=/Applications/Unity/Hub/Editor/2022.3.62f1/Unity.app/Contents/MonoBleedingEdge/lib/mono/unityaot-linux/Facades/System.Runtime.Numerics.dll --allowed-assembly=/Applications/Unity/Hub/Editor/2022.3.62f1/Unity.app/Contents/MonoBleedingEdge/lib/mono/unityaot-linux/Facades/System.Runtime.Serialization.Formatters.dll --allowed-assembly=/Applications/Unity/Hub/Editor/2022.3.62f1/Unity.app/Contents/MonoBleedingEdge/lib/mono/unityaot-linux/Facades/System.Runtime.Serialization.Json.dll --allowed-assembly=/Applications/Unity/Hub/Editor/2022.3.62f1/Unity.app/Contents/MonoBleedingEdge/lib/mono/unityaot-linux/Facades/System.Runtime.Serialization.Primitives.dll --allowed-assembly=/Applications/Unity/Hub/Editor/2022.3.62f1/Unity.app/Contents/MonoBleedingEdge/lib/mono/unityaot-linux/Facades/System.Runtime.Serialization.Xml.dll --allowed-assembly=/Applications/Unity/Hub/Editor/2022.3.62f1/Unity.app/Contents/MonoBleedingEdge/lib/mono/unityaot-linux/Facades/System.Runtime.dll --allowed-assembly=/Applications/Unity/Hub/Editor/2022.3.62f1/Unity.app/Contents/MonoBleedingEdge/lib/mono/unityaot-linux/Facades/System.Security.AccessControl.dll --allowed-assembly=/Applications/Unity/Hub/Editor/2022.3.62f1/Unity.app/Contents/MonoBleedingEdge/lib/mono/unityaot-linux/Facades/System.Security.Claims.dll --allowed-assembly=/Applications/Unity/Hub/Editor/2022.3.62f1/Unity.app/Contents/MonoBleedingEdge/lib/mono/unityaot-linux/Facades/System.Security.Cryptography.Algorithms.dll --allowed-assembly=/Applications/Unity/Hub/Editor/2022.3.62f1/Unity.app/Contents/MonoBleedingEdge/lib/mono/unityaot-linux/Facades/System.Security.Cryptography.Cng.dll --allowed-assembly=/Applications/Unity/Hub/Editor/2022.3.62f1/Unity.app/Contents/MonoBleedingEdge/lib/mono/unityaot-linux/Facades/System.Security.Cryptography.Csp.dll --allowed-assembly=/Applications/Unity/Hub/Editor/2022.3.62f1/Unity.app/Contents/MonoBleedingEdge/lib/mono/unityaot-linux/Facades/System.Security.Cryptography.DeriveBytes.dll --allowed-assembly=/Applications/Unity/Hub/Editor/2022.3.62f1/Unity.app/Contents/MonoBleedingEdge/lib/mono/unityaot-linux/Facades/System.Security.Cryptography.Encoding.dll --allowed-assembly=/Applications/Unity/Hub/Editor/2022.3.62f1/Unity.app/Contents/MonoBleedingEdge/lib/mono/unityaot-linux/Facades/System.Security.Cryptography.Encryption.Aes.dll --allowed-assembly=/Applications/Unity/Hub/Editor/2022.3.62f1/Unity.app/Contents/MonoBleedingEdge/lib/mono/unityaot-linux/Facades/System.Security.Cryptography.Encryption.ECDiffieHellman.dll --allowed-assembly=/Applications/Unity/Hub/Editor/2022.3.62f1/Unity.app/Contents/MonoBleedingEdge/lib/mono/unityaot-linux/Facades/System.Security.Cryptography.Encryption.ECDsa.dll --allowed-assembly=/Applications/Unity/Hub/Editor/2022.3.62f1/Unity.app/Contents/MonoBleedingEdge/lib/mono/unityaot-linux/Facades/System.Security.Cryptography.Encryption.dll --allowed-assembly=/Applications/Unity/Hub/Editor/2022.3.62f1/Unity.app/Contents/MonoBleedingEdge/lib/mono/unityaot-linux/Facades/System.Security.Cryptography.Hashing.Algorithms.dll --allowed-assembly=/Applications/Unity/Hub/Editor/2022.3.62f1/Unity.app/Contents/MonoBleedingEdge/lib/mono/unityaot-linux/Facades/System.Security.Cryptography.Hashing.dll --allowed-assembly=/Applications/Unity/Hub/Editor/2022.3.62f1/Unity.app/Contents/MonoBleedingEdge/lib/mono/unityaot-linux/Facades/System.Security.Cryptography.OpenSsl.dll --allowed-assembly=/Applications/Unity/Hub/Editor/2022.3.62f1/Unity.app/Contents/MonoBleedingEdge/lib/mono/unityaot-linux/Facades/System.Security.Cryptography.Pkcs.dll --allowed-assembly=/Applications/Unity/Hub/Editor/2022.3.62f1/Unity.app/Contents/MonoBleedingEdge/lib/mono/unityaot-linux/Facades/System.Security.Cryptography.Primitives.dll --allowed-assembly=/Applications/Unity/Hub/Editor/2022.3.62f1/Unity.app/Contents/MonoBleedingEdge/lib/mono/unityaot-linux/Facades/System.Security.Cryptography.ProtectedData.dll --allowed-assembly=/Applications/Unity/Hub/Editor/2022.3.62f1/Unity.app/Contents/MonoBleedingEdge/lib/mono/unityaot-linux/Facades/System.Security.Cryptography.RSA.dll --allowed-assembly=/Applications/Unity/Hub/Editor/2022.3.62f1/Unity.app/Contents/MonoBleedingEdge/lib/mono/unityaot-linux/Facades/System.Security.Cryptography.RandomNumberGenerator.dll --allowed-assembly=/Applications/Unity/Hub/Editor/2022.3.62f1/Unity.app/Contents/MonoBleedingEdge/lib/mono/unityaot-linux/Facades/System.Security.Cryptography.X509Certificates.dll --allowed-assembly=/Applications/Unity/Hub/Editor/2022.3.62f1/Unity.app/Contents/MonoBleedingEdge/lib/mono/unityaot-linux/Facades/System.Security.Principal.Windows.dll --allowed-assembly=/Applications/Unity/Hub/Editor/2022.3.62f1/Unity.app/Contents/MonoBleedingEdge/lib/mono/unityaot-linux/Facades/System.Security.Principal.dll --allowed-assembly=/Applications/Unity/Hub/Editor/2022.3.62f1/Unity.app/Contents/MonoBleedingEdge/lib/mono/unityaot-linux/Facades/System.Security.SecureString.dll --allowed-assembly=/Applications/Unity/Hub/Editor/2022.3.62f1/Unity.app/Contents/MonoBleedingEdge/lib/mono/unityaot-linux/Facades/System.ServiceModel.Duplex.dll --allowed-assembly=/Applications/Unity/Hub/Editor/2022.3.62f1/Unity.app/Contents/MonoBleedingEdge/lib/mono/unityaot-linux/Facades/System.ServiceModel.Http.dll --allowed-assembly=/Applications/Unity/Hub/Editor/2022.3.62f1/Unity.app/Contents/MonoBleedingEdge/lib/mono/unityaot-linux/Facades/System.ServiceModel.NetTcp.dll --allowed-assembly=/Applications/Unity/Hub/Editor/2022.3.62f1/Unity.app/Contents/MonoBleedingEdge/lib/mono/unityaot-linux/Facades/System.ServiceModel.Primitives.dll --allowed-assembly=/Applications/Unity/Hub/Editor/2022.3.62f1/Unity.app/Contents/MonoBleedingEdge/lib/mono/unityaot-linux/Facades/System.ServiceModel.Security.dll --allowed-assembly=/Applications/Unity/Hub/Editor/2022.3.62f1/Unity.app/Contents/MonoBleedingEdge/lib/mono/unityaot-linux/Facades/System.ServiceProcess.ServiceController.dll --allowed-assembly=/Applications/Unity/Hub/Editor/2022.3.62f1/Unity.app/Contents/MonoBleedingEdge/lib/mono/unityaot-linux/Facades/System.Text.Encoding.CodePages.dll --allowed-assembly=/Applications/Unity/Hub/Editor/2022.3.62f1/Unity.app/Contents/MonoBleedingEdge/lib/mono/unityaot-linux/Facades/System.Text.Encoding.Extensions.dll --allowed-assembly=/Applications/Unity/Hub/Editor/2022.3.62f1/Unity.app/Contents/MonoBleedingEdge/lib/mono/unityaot-linux/Facades/System.Text.Encoding.dll --allowed-assembly=/Applications/Unity/Hub/Editor/2022.3.62f1/Unity.app/Contents/MonoBleedingEdge/lib/mono/unityaot-linux/Facades/System.Text.RegularExpressions.dll --allowed-assembly=/Applications/Unity/Hub/Editor/2022.3.62f1/Unity.app/Contents/MonoBleedingEdge/lib/mono/unityaot-linux/Facades/System.Threading.AccessControl.dll --allowed-assembly=/Applications/Unity/Hub/Editor/2022.3.62f1/Unity.app/Contents/MonoBleedingEdge/lib/mono/unityaot-linux/Facades/System.Threading.Overlapped.dll --allowed-assembly=/Applications/Unity/Hub/Editor/2022.3.62f1/Unity.app/Contents/MonoBleedingEdge/lib/mono/unityaot-linux/Facades/System.Threading.Tasks.Extensions.dll --allowed-assembly=/Applications/Unity/Hub/Editor/2022.3.62f1/Unity.app/Contents/MonoBleedingEdge/lib/mono/unityaot-linux/Facades/System.Threading.Tasks.Parallel.dll --allowed-assembly=/Applications/Unity/Hub/Editor/2022.3.62f1/Unity.app/Contents/MonoBleedingEdge/lib/mono/unityaot-linux/Facades/System.Threading.Tasks.dll --allowed-assembly=/Applications/Unity/Hub/Editor/2022.3.62f1/Unity.app/Contents/MonoBleedingEdge/lib/mono/unityaot-linux/Facades/System.Threading.Thread.dll --allowed-assembly=/Applications/Unity/Hub/Editor/2022.3.62f1/Unity.app/Contents/MonoBleedingEdge/lib/mono/unityaot-linux/Facades/System.Threading.ThreadPool.dll --allowed-assembly=/Applications/Unity/Hub/Editor/2022.3.62f1/Unity.app/Contents/MonoBleedingEdge/lib/mono/unityaot-linux/Facades/System.Threading.Timer.dll --allowed-assembly=/Applications/Unity/Hub/Editor/2022.3.62f1/Unity.app/Contents/MonoBleedingEdge/lib/mono/unityaot-linux/Facades/System.Threading.dll --allowed-assembly=/Applications/Unity/Hub/Editor/2022.3.62f1/Unity.app/Contents/MonoBleedingEdge/lib/mono/unityaot-linux/Facades/System.ValueTuple.dll --allowed-assembly=/Applications/Unity/Hub/Editor/2022.3.62f1/Unity.app/Contents/MonoBleedingEdge/lib/mono/unityaot-linux/Facades/System.Xml.ReaderWriter.dll --allowed-assembly=/Applications/Unity/Hub/Editor/2022.3.62f1/Unity.app/Contents/MonoBleedingEdge/lib/mono/unityaot-linux/Facades/System.Xml.XDocument.dll --allowed-assembly=/Applications/Unity/Hub/Editor/2022.3.62f1/Unity.app/Contents/MonoBleedingEdge/lib/mono/unityaot-linux/Facades/System.Xml.XPath.XDocument.dll --allowed-assembly=/Applications/Unity/Hub/Editor/2022.3.62f1/Unity.app/Contents/MonoBleedingEdge/lib/mono/unityaot-linux/Facades/System.Xml.XPath.XmlDocument.dll --allowed-assembly=/Applications/Unity/Hub/Editor/2022.3.62f1/Unity.app/Contents/MonoBleedingEdge/lib/mono/unityaot-linux/Facades/System.Xml.XPath.dll --allowed-assembly=/Applications/Unity/Hub/Editor/2022.3.62f1/Unity.app/Contents/MonoBleedingEdge/lib/mono/unityaot-linux/Facades/System.Xml.XmlDocument.dll --allowed-assembly=/Applications/Unity/Hub/Editor/2022.3.62f1/Unity.app/Contents/MonoBleedingEdge/lib/mono/unityaot-linux/Facades/System.Xml.XmlSerializer.dll --allowed-assembly=/Applications/Unity/Hub/Editor/2022.3.62f1/Unity.app/Contents/MonoBleedingEdge/lib/mono/unityaot-linux/Facades/System.Xml.Xsl.Primitives.dll --allowed-assembly=/Applications/Unity/Hub/Editor/2022.3.62f1/Unity.app/Contents/MonoBleedingEdge/lib/mono/unityaot-linux/Facades/netstandard.dll --allowed-assembly=/Applications/Unity/Hub/Editor/2022.3.62f1/Unity.app/Contents/MonoBleedingEdge/lib/mono/unityaot-linux/Microsoft.CSharp.dll --allowed-assembly=/Applications/Unity/Hub/Editor/2022.3.62f1/Unity.app/Contents/MonoBleedingEdge/lib/mono/unityaot-linux/Mono.Data.Sqlite.dll --allowed-assembly=/Applications/Unity/Hub/Editor/2022.3.62f1/Unity.app/Contents/MonoBleedingEdge/lib/mono/unityaot-linux/Mono.Data.Tds.dll --allowed-assembly=/Applications/Unity/Hub/Editor/2022.3.62f1/Unity.app/Contents/MonoBleedingEdge/lib/mono/unityaot-linux/Mono.Security.dll --allowed-assembly=/Applications/Unity/Hub/Editor/2022.3.62f1/Unity.app/Contents/MonoBleedingEdge/lib/mono/unityaot-linux/System.ComponentModel.Composition.dll --allowed-assembly=/Applications/Unity/Hub/Editor/2022.3.62f1/Unity.app/Contents/MonoBleedingEdge/lib/mono/unityaot-linux/System.ComponentModel.DataAnnotations.dll --allowed-assembly=/Applications/Unity/Hub/Editor/2022.3.62f1/Unity.app/Contents/MonoBleedingEdge/lib/mono/unityaot-linux/System.Configuration.Install.dll --allowed-assembly=/Applications/Unity/Hub/Editor/2022.3.62f1/Unity.app/Contents/MonoBleedingEdge/lib/mono/unityaot-linux/System.Configuration.dll --allowed-assembly=/Applications/Unity/Hub/Editor/2022.3.62f1/Unity.app/Contents/MonoBleedingEdge/lib/mono/unityaot-linux/System.Core.dll --allowed-assembly=/Applications/Unity/Hub/Editor/2022.3.62f1/Unity.app/Contents/MonoBleedingEdge/lib/mono/unityaot-linux/System.Data.DataSetExtensions.dll --allowed-assembly=/Applications/Unity/Hub/Editor/2022.3.62f1/Unity.app/Contents/MonoBleedingEdge/lib/mono/unityaot-linux/System.Data.Entity.dll --allowed-assembly=/Applications/Unity/Hub/Editor/2022.3.62f1/Unity.app/Contents/MonoBleedingEdge/lib/mono/unityaot-linux/System.Data.Linq.dll --allowed-assembly=/Applications/Unity/Hub/Editor/2022.3.62f1/Unity.app/Contents/MonoBleedingEdge/lib/mono/unityaot-linux/System.Data.OracleClient.dll --allowed-assembly=/Applications/Unity/Hub/Editor/2022.3.62f1/Unity.app/Contents/MonoBleedingEdge/lib/mono/unityaot-linux/System.Data.Services.Client.dll --allowed-assembly=/Applications/Unity/Hub/Editor/2022.3.62f1/Unity.app/Contents/MonoBleedingEdge/lib/mono/unityaot-linux/System.Data.Services.dll --allowed-assembly=/Applications/Unity/Hub/Editor/2022.3.62f1/Unity.app/Contents/MonoBleedingEdge/lib/mono/unityaot-linux/System.Data.dll --allowed-assembly=/Applications/Unity/Hub/Editor/2022.3.62f1/Unity.app/Contents/MonoBleedingEdge/lib/mono/unityaot-linux/System.Design.dll --allowed-assembly=/Applications/Unity/Hub/Editor/2022.3.62f1/Unity.app/Contents/MonoBleedingEdge/lib/mono/unityaot-linux/System.DirectoryServices.Protocols.dll --allowed-assembly=/Applications/Unity/Hub/Editor/2022.3.62f1/Unity.app/Contents/MonoBleedingEdge/lib/mono/unityaot-linux/System.DirectoryServices.dll --allowed-assembly=/Applications/Unity/Hub/Editor/2022.3.62f1/Unity.app/Contents/MonoBleedingEdge/lib/mono/unityaot-linux/System.Drawing.Design.dll --allowed-assembly=/Applications/Unity/Hub/Editor/2022.3.62f1/Unity.app/Contents/MonoBleedingEdge/lib/mono/unityaot-linux/System.Drawing.dll --allowed-assembly=/Applications/Unity/Hub/Editor/2022.3.62f1/Unity.app/Contents/MonoBleedingEdge/lib/mono/unityaot-linux/System.EnterpriseServices.dll --allowed-assembly=/Applications/Unity/Hub/Editor/2022.3.62f1/Unity.app/Contents/MonoBleedingEdge/lib/mono/unityaot-linux/System.IO.Compression.FileSystem.dll --allowed-assembly=/Applications/Unity/Hub/Editor/2022.3.62f1/Unity.app/Contents/MonoBleedingEdge/lib/mono/unityaot-linux/System.IO.Compression.dll --allowed-assembly=/Applications/Unity/Hub/Editor/2022.3.62f1/Unity.app/Contents/MonoBleedingEdge/lib/mono/unityaot-linux/System.IdentityModel.Selectors.dll --allowed-assembly=/Applications/Unity/Hub/Editor/2022.3.62f1/Unity.app/Contents/MonoBleedingEdge/lib/mono/unityaot-linux/System.IdentityModel.dll --allowed-assembly=/Applications/Unity/Hub/Editor/2022.3.62f1/Unity.app/Contents/MonoBleedingEdge/lib/mono/unityaot-linux/System.Json.dll --allowed-assembly=/Applications/Unity/Hub/Editor/2022.3.62f1/Unity.app/Contents/MonoBleedingEdge/lib/mono/unityaot-linux/System.Management.dll --allowed-assembly=/Applications/Unity/Hub/Editor/2022.3.62f1/Unity.app/Contents/MonoBleedingEdge/lib/mono/unityaot-linux/System.Messaging.dll --allowed-assembly=/Applications/Unity/Hub/Editor/2022.3.62f1/Unity.app/Contents/MonoBleedingEdge/lib/mono/unityaot-linux/System.Net.Http.WebRequest.dll --allowed-assembly=/Applications/Unity/Hub/Editor/2022.3.62f1/Unity.app/Contents/MonoBleedingEdge/lib/mono/unityaot-linux/System.Net.Http.dll --allowed-assembly=/Applications/Unity/Hub/Editor/2022.3.62f1/Unity.app/Contents/MonoBleedingEdge/lib/mono/unityaot-linux/System.Net.dll --allowed-assembly=/Applications/Unity/Hub/Editor/2022.3.62f1/Unity.app/Contents/MonoBleedingEdge/lib/mono/unityaot-linux/System.Numerics.Vectors.dll --allowed-assembly=/Applications/Unity/Hub/Editor/2022.3.62f1/Unity.app/Contents/MonoBleedingEdge/lib/mono/unityaot-linux/System.Numerics.dll --allowed-assembly=/Applications/Unity/Hub/Editor/2022.3.62f1/Unity.app/Contents/MonoBleedingEdge/lib/mono/unityaot-linux/System.Reflection.Context.dll --allowed-assembly=/Applications/Unity/Hub/Editor/2022.3.62f1/Unity.app/Contents/MonoBleedingEdge/lib/mono/unityaot-linux/System.Runtime.Caching.dll --allowed-assembly=/Applications/Unity/Hub/Editor/2022.3.62f1/Unity.app/Contents/MonoBleedingEdge/lib/mono/unityaot-linux/System.Runtime.DurableInstancing.dll --allowed-assembly=/Applications/Unity/Hub/Editor/2022.3.62f1/Unity.app/Contents/MonoBleedingEdge/lib/mono/unityaot-linux/System.Runtime.Remoting.dll --allowed-assembly=/Applications/Unity/Hub/Editor/2022.3.62f1/Unity.app/Contents/MonoBleedingEdge/lib/mono/unityaot-linux/System.Runtime.Serialization.Formatters.Soap.dll --allowed-assembly=/Applications/Unity/Hub/Editor/2022.3.62f1/Unity.app/Contents/MonoBleedingEdge/lib/mono/unityaot-linux/System.Runtime.Serialization.dll --allowed-assembly=/Applications/Unity/Hub/Editor/2022.3.62f1/Unity.app/Contents/MonoBleedingEdge/lib/mono/unityaot-linux/System.Security.dll --allowed-assembly=/Applications/Unity/Hub/Editor/2022.3.62f1/Unity.app/Contents/MonoBleedingEdge/lib/mono/unityaot-linux/System.ServiceModel.Activation.dll --allowed-assembly=/Applications/Unity/Hub/Editor/2022.3.62f1/Unity.app/Contents/MonoBleedingEdge/lib/mono/unityaot-linux/System.ServiceModel.Discovery.dll --allowed-assembly=/Applications/Unity/Hub/Editor/2022.3.62f1/Unity.app/Contents/MonoBleedingEdge/lib/mono/unityaot-linux/System.ServiceModel.Internals.dll --allowed-assembly=/Applications/Unity/Hub/Editor/2022.3.62f1/Unity.app/Contents/MonoBleedingEdge/lib/mono/unityaot-linux/System.ServiceModel.Routing.dll --allowed-assembly=/Applications/Unity/Hub/Editor/2022.3.62f1/Unity.app/Contents/MonoBleedingEdge/lib/mono/unityaot-linux/System.ServiceModel.Web.dll --allowed-assembly=/Applications/Unity/Hub/Editor/2022.3.62f1/Unity.app/Contents/MonoBleedingEdge/lib/mono/unityaot-linux/System.ServiceModel.dll --allowed-assembly=/Applications/Unity/Hub/Editor/2022.3.62f1/Unity.app/Contents/MonoBleedingEdge/lib/mono/unityaot-linux/System.ServiceProcess.dll --allowed-assembly=/Applications/Unity/Hub/Editor/2022.3.62f1/Unity.app/Contents/MonoBleedingEdge/lib/mono/unityaot-linux/System.Transactions.dll --allowed-assembly=/Applications/Unity/Hub/Editor/2022.3.62f1/Unity.app/Contents/MonoBleedingEdge/lib/mono/unityaot-linux/System.Web.ApplicationServices.dll --allowed-assembly=/Applications/Unity/Hub/Editor/2022.3.62f1/Unity.app/Contents/MonoBleedingEdge/lib/mono/unityaot-linux/System.Web.DynamicData.dll --allowed-assembly=/Applications/Unity/Hub/Editor/2022.3.62f1/Unity.app/Contents/MonoBleedingEdge/lib/mono/unityaot-linux/System.Web.Extensions.Design.dll --allowed-assembly=/Applications/Unity/Hub/Editor/2022.3.62f1/Unity.app/Contents/MonoBleedingEdge/lib/mono/unityaot-linux/System.Web.Extensions.dll --allowed-assembly=/Applications/Unity/Hub/Editor/2022.3.62f1/Unity.app/Contents/MonoBleedingEdge/lib/mono/unityaot-linux/System.Web.RegularExpressions.dll --allowed-assembly=/Applications/Unity/Hub/Editor/2022.3.62f1/Unity.app/Contents/MonoBleedingEdge/lib/mono/unityaot-linux/System.Web.Services.dll --allowed-assembly=/Applications/Unity/Hub/Editor/2022.3.62f1/Unity.app/Contents/MonoBleedingEdge/lib/mono/unityaot-linux/System.Web.dll --allowed-assembly=/Applications/Unity/Hub/Editor/2022.3.62f1/Unity.app/Contents/MonoBleedingEdge/lib/mono/unityaot-linux/System.Windows.Forms.DataVisualization.dll --allowed-assembly=/Applications/Unity/Hub/Editor/2022.3.62f1/Unity.app/Contents/MonoBleedingEdge/lib/mono/unityaot-linux/System.Windows.Forms.dll --allowed-assembly=/Applications/Unity/Hub/Editor/2022.3.62f1/Unity.app/Contents/MonoBleedingEdge/lib/mono/unityaot-linux/System.Windows.dll --allowed-assembly=/Applications/Unity/Hub/Editor/2022.3.62f1/Unity.app/Contents/MonoBleedingEdge/lib/mono/unityaot-linux/System.Xaml.dll --allowed-assembly=/Applications/Unity/Hub/Editor/2022.3.62f1/Unity.app/Contents/MonoBleedingEdge/lib/mono/unityaot-linux/System.Xml.Linq.dll --allowed-assembly=/Applications/Unity/Hub/Editor/2022.3.62f1/Unity.app/Contents/MonoBleedingEdge/lib/mono/unityaot-linux/System.Xml.Serialization.dll --allowed-assembly=/Applications/Unity/Hub/Editor/2022.3.62f1/Unity.app/Contents/MonoBleedingEdge/lib/mono/unityaot-linux/System.Xml.dll --allowed-assembly=/Applications/Unity/Hub/Editor/2022.3.62f1/Unity.app/Contents/MonoBleedingEdge/lib/mono/unityaot-linux/System.dll --allowed-assembly=/Applications/Unity/Hub/Editor/2022.3.62f1/Unity.app/Contents/MonoBleedingEdge/lib/mono/unityaot-linux/mscorlib.dll --out=Library/Bee/artifacts/Android/ManagedStripped --include-link-xml=/Users/<USER>/Documents/GitHub/PerfumerProject/Temp/StagingArea/Data/Managed/MethodsToPreserve.xml --include-link-xml=/Users/<USER>/Documents/GitHub/PerfumerProject/Temp/StagingArea/Data/Managed/TypesInScenes.xml --include-link-xml=/Users/<USER>/Documents/GitHub/PerfumerProject/Temp/StagingArea/Data/Managed/SerializedTypes.xml --include-link-xml=/Users/<USER>/Documents/GitHub/PerfumerProject/Assets/../Library/InputSystem/AndroidLink.xml --include-link-xml=/Users/<USER>/Documents/GitHub/PerfumerProject/Assets/Fungus/Scripts/link.xml --include-link-xml=/Applications/Unity/Hub/Editor/2022.3.62f1/PlaybackEngines/AndroidPlayer/Tools/AndroidNativeLink.xml --include-directory=/Users/<USER>/Documents/GitHub/PerfumerProject/Library/Bee/PlayerScriptAssemblies --include-directory=/Users/<USER>/Documents/GitHub/PerfumerProject/Assets/Plugins/Demigiant/DOTween --include-directory=/Applications/Unity/Hub/Editor/2022.3.62f1/PlaybackEngines/AndroidPlayer/Variations/il2cpp/Managed --include-directory=/Users/<USER>/Documents/GitHub/PerfumerProject/Assets/Plugins/Demigiant/DemiLib/Core --include-directory=/Users/<USER>/Documents/GitHub/PerfumerProject/Assets/FlexReader --include-directory=/Users/<USER>/Documents/GitHub/PerfumerProject/Library/PackageCache/com.unity.visualscripting@1.9.4/Runtime/VisualScripting.Flow/Dependencies/NCalc --include-directory=/Users/<USER>/Documents/GitHub/PerfumerProject/Assets/Plugins/Demigiant/DOTweenPro --include-directory=/Users/<USER>/Documents/GitHub/PerfumerProject/Library/PackageCache/com.unity.nuget.newtonsoft-json@3.2.1/Runtime/AOT --include-directory=/Applications/Unity/Hub/Editor/2022.3.62f1/Unity.app/Contents/MonoBleedingEdge/lib/mono/unityaot-linux --include-directory=/Applications/Unity/Hub/Editor/2022.3.62f1/Unity.app/Contents/MonoBleedingEdge/lib/mono/unityaot-linux/Facades --profiler-report --profiler-output-file=/Users/<USER>/Documents/GitHub/PerfumerProject/Library/Bee/artifacts/unitylinker_xy1a.traceevents --dotnetprofile=unityaot-linux --dotnetruntime=Il2Cpp --platform=Android --use-editor-options --enable-engine-module-stripping --engine-modules-asset-file=/Applications/Unity/Hub/Editor/2022.3.62f1/PlaybackEngines/AndroidPlayer/modules.asset --editor-data-file=/Users/<USER>/Documents/GitHub/PerfumerProject/Temp/StagingArea/Data/Managed/EditorToUnityLinkerData.json --include-unity-root-assembly=/Users/<USER>/Documents/GitHub/PerfumerProject/Library/Bee/PlayerScriptAssemblies/Assembly-CSharp-firstpass.dll --include-unity-root-assembly=/Users/<USER>/Documents/GitHub/PerfumerProject/Library/Bee/PlayerScriptAssemblies/Assembly-CSharp.dll --include-unity-root-assembly=/Users/<USER>/Documents/GitHub/PerfumerProject/Library/Bee/PlayerScriptAssemblies/UnityEngine.UI.dll --include-unity-root-assembly=/Users/<USER>/Documents/GitHub/PerfumerProject/Library/Bee/PlayerScriptAssemblies/Unity.InputSystem.dll --include-unity-root-assembly=/Users/<USER>/Documents/GitHub/PerfumerProject/Library/Bee/PlayerScriptAssemblies/NavMeshPlus.dll --include-unity-root-assembly=/Users/<USER>/Documents/GitHub/PerfumerProject/Library/Bee/PlayerScriptAssemblies/Unity.TextMeshPro.dll --include-unity-root-assembly=/Users/<USER>/Documents/GitHub/PerfumerProject/Library/Bee/PlayerScriptAssemblies/Fungus.dll --include-unity-root-assembly=/Users/<USER>/Documents/GitHub/PerfumerProject/Assets/Plugins/Demigiant/DOTween/DOTween.dll --print-command-line --enable-analytics
[ 525/1311  0s] CopyFiles /Users/<USER>/Documents/GitHub/PerfumerProject/Library/Bee/Android/Prj/IL2CPP/Il2CppBackup/Managed/UnityEngine.TextCoreTextEngineModule.pdb
[ 526/1311  0s] CopyFiles /Users/<USER>/Documents/GitHub/PerfumerProject/Library/Bee/Android/Prj/IL2CPP/Il2CppBackup/Managed/UnityEngine.TextCoreTextEngineModule.dll
[ 527/1311  0s] CopyFiles /Users/<USER>/Documents/GitHub/PerfumerProject/Library/Bee/Android/Prj/IL2CPP/Il2CppBackup/Managed/UnityEngine.TextCoreFontEngineModule.pdb
[ 528/1311  0s] CopyFiles /Users/<USER>/Documents/GitHub/PerfumerProject/Library/Bee/Android/Prj/IL2CPP/Il2CppBackup/Managed/UnityEngine.UnityWebRequestModule.pdb
[ 529/1311  0s] CopyFiles /Users/<USER>/Documents/GitHub/PerfumerProject/Library/Bee/Android/Prj/IL2CPP/Il2CppBackup/Managed/UnityEngine.TextCoreFontEngineModule.dll
[ 530/1311  0s] CopyFiles /Users/<USER>/Documents/GitHub/PerfumerProject/Library/Bee/Android/Prj/IL2CPP/Il2CppBackup/Managed/UnityEngine.TextRenderingModule.dll
[ 531/1311  0s] CopyFiles /Users/<USER>/Documents/GitHub/PerfumerProject/Library/Bee/Android/Prj/IL2CPP/Il2CppBackup/Managed/UnityEngine.JSONSerializeModule.dll
[ 532/1311  0s] CopyFiles /Users/<USER>/Documents/GitHub/PerfumerProject/Library/Bee/Android/Prj/IL2CPP/Il2CppBackup/Managed/UnityEngine.JSONSerializeModule.pdb
[ 533/1311  0s] CopyFiles /Users/<USER>/Documents/GitHub/PerfumerProject/Library/Bee/Android/Prj/IL2CPP/Il2CppBackup/Managed/UnityEngine.SharedInternalsModule.pdb
[ 534/1311  0s] CopyFiles /Users/<USER>/Documents/GitHub/PerfumerProject/Library/Bee/Android/Prj/IL2CPP/Il2CppBackup/Managed/UnityEngine.TextRenderingModule.pdb
[ 535/1311  0s] CopyFiles /Users/<USER>/Documents/GitHub/PerfumerProject/Library/Bee/Android/Prj/IL2CPP/Il2CppBackup/Managed/UnityEngine.SubsystemsModule.pdb
[ 536/1311  0s] CopyFiles /Users/<USER>/Documents/GitHub/PerfumerProject/Library/Bee/Android/Prj/IL2CPP/Il2CppBackup/Managed/UnityEngine.UIElementsModule.pdb
[ 537/1311  0s] CopyFiles /Users/<USER>/Documents/GitHub/PerfumerProject/Library/Bee/Android/Prj/IL2CPP/Il2CppBackup/Managed/UnityEngine.UnityWebRequestModule.dll
[ 538/1311  0s] CopyFiles /Users/<USER>/Documents/GitHub/PerfumerProject/Library/Bee/Android/Prj/IL2CPP/Il2CppBackup/Managed/UnityEngine.UIElementsModule.dll
[ 539/1311  0s] CopyFiles /Users/<USER>/Documents/GitHub/PerfumerProject/Library/Bee/Android/Prj/IL2CPP/Il2CppBackup/Managed/UnityEngine.AndroidJNIModule.pdb
[ 540/1311  0s] CopyFiles /Users/<USER>/Documents/GitHub/PerfumerProject/Library/Bee/Android/Prj/IL2CPP/Il2CppBackup/Managed/UnityEngine.SharedInternalsModule.dll
[ 541/1311  0s] CopyFiles /Users/<USER>/Documents/GitHub/PerfumerProject/Library/Bee/Android/Prj/IL2CPP/Il2CppBackup/Managed/UnityEngine.PropertiesModule.pdb
[ 542/1311  0s] CopyFiles /Users/<USER>/Documents/GitHub/PerfumerProject/Library/Bee/Android/Prj/IL2CPP/Il2CppBackup/Managed/UnityEngine.SubsystemsModule.dll
[ 543/1311  0s] CopyFiles /Users/<USER>/Documents/GitHub/PerfumerProject/Library/Bee/Android/Prj/IL2CPP/Il2CppBackup/Managed/UnityEngine.PropertiesModule.dll
[ 544/1311  0s] CopyFiles /Users/<USER>/Documents/GitHub/PerfumerProject/Library/Bee/Android/Prj/IL2CPP/Il2CppBackup/Managed/UnityEngine.InputLegacyModule.dll
[ 545/1311  0s] CopyFiles /Users/<USER>/Documents/GitHub/PerfumerProject/Library/Bee/Android/Prj/IL2CPP/Il2CppBackup/Managed/UnityEngine.ParticleSystemModule.dll
[ 546/1311  0s] CopyFiles /Users/<USER>/Documents/GitHub/PerfumerProject/Library/Bee/Android/Prj/IL2CPP/Il2CppBackup/Managed/UnityEngine.Physics2DModule.dll
[ 547/1311  0s] CopyFiles /Users/<USER>/Documents/GitHub/PerfumerProject/Library/Bee/Android/Prj/IL2CPP/Il2CppBackup/Managed/UnityEngine.AnimationModule.dll
[ 548/1311  0s] CopyFiles /Users/<USER>/Documents/GitHub/PerfumerProject/Library/Bee/Android/Prj/IL2CPP/Il2CppBackup/Managed/UnityEngine.ParticleSystemModule.pdb
[ 549/1311  0s] CopyFiles /Users/<USER>/Documents/GitHub/PerfumerProject/Library/Bee/Android/Prj/IL2CPP/Il2CppBackup/Managed/UnityLinkerToEditorData.json
[ 550/1311  0s] CopyFiles /Users/<USER>/Documents/GitHub/PerfumerProject/Library/Bee/Android/Prj/IL2CPP/Il2CppBackup/Managed/mscorlib.dll
[ 551/1311  0s] CopyFiles /Users/<USER>/Documents/GitHub/PerfumerProject/Library/Bee/Android/Prj/IL2CPP/Il2CppBackup/Managed/UnityEngine.AnimationModule.pdb
[ 552/1311  0s] CopyFiles /Users/<USER>/Documents/GitHub/PerfumerProject/Library/Bee/Android/Prj/IL2CPP/Il2CppBackup/Managed/UnityEngine.Physics2DModule.pdb
[ 553/1311  0s] CopyFiles /Users/<USER>/Documents/GitHub/PerfumerProject/Library/Bee/Android/Prj/IL2CPP/Il2CppBackup/Managed/Fungus.pdb
[ 554/1311  0s] CopyFiles /Users/<USER>/Documents/GitHub/PerfumerProject/Library/Bee/Android/Prj/IL2CPP/Il2CppBackup/Managed/System.dll
[ 555/1311  0s] CopyFiles /Users/<USER>/Documents/GitHub/PerfumerProject/Library/Bee/Android/Prj/IL2CPP/Il2CppBackup/Managed/DOTweenPro.dll
[ 556/1311  0s] CopyFiles /Users/<USER>/Documents/GitHub/PerfumerProject/Library/Bee/Android/Prj/IL2CPP/Il2CppBackup/Managed/analytics.json
[ 557/1311  0s] CopyFiles /Users/<USER>/Documents/GitHub/PerfumerProject/Library/Bee/Android/Prj/IL2CPP/Il2CppBackup/Managed/Fungus.dll
[ 558/1311  0s] CopyFiles /Users/<USER>/Documents/GitHub/PerfumerProject/Library/Bee/Android/Prj/IL2CPP/Il2CppBackup/Managed/System.Xml.dll
[ 559/1311  0s] CopyFiles /Users/<USER>/Documents/GitHub/PerfumerProject/Library/Bee/Android/Prj/IL2CPP/Il2CppBackup/Managed/Unity.InputSystem.pdb
[ 560/1311  0s] CopyFiles /Users/<USER>/Documents/GitHub/PerfumerProject/Library/Bee/Android/Prj/IL2CPP/Il2CppBackup/Managed/Unity.TextMeshPro.pdb
[ 561/1311  0s] CopyFiles /Users/<USER>/Documents/GitHub/PerfumerProject/Library/Bee/Android/Prj/IL2CPP/Il2CppBackup/Managed/Unity.TextMeshPro.dll
[ 562/1311  0s] CopyFiles /Users/<USER>/Documents/GitHub/PerfumerProject/Library/Bee/Android/Prj/IL2CPP/Il2CppBackup/Managed/DOTween.dll
[ 563/1311  0s] CopyFiles /Users/<USER>/Documents/GitHub/PerfumerProject/Library/Bee/Android/Prj/IL2CPP/Il2CppBackup/Managed/Unity.InputSystem.dll
[ 564/1311  0s] CopyFiles /Users/<USER>/Documents/GitHub/PerfumerProject/Library/Bee/Android/Prj/IL2CPP/Il2CppBackup/Managed/System.Core.dll
[ 565/1311  0s] CopyFiles /Users/<USER>/Documents/GitHub/PerfumerProject/Library/Bee/Android/Prj/IL2CPP/Il2CppBackup/Managed/NavMeshPlus.dll
[ 566/1311  0s] CopyFiles /Users/<USER>/Documents/GitHub/PerfumerProject/Library/Bee/Android/Prj/IL2CPP/Il2CppBackup/Managed/UnityEngine.TilemapModule.pdb
[ 567/1311  0s] CopyFiles /Users/<USER>/Documents/GitHub/PerfumerProject/Library/Bee/Android/Prj/IL2CPP/Il2CppBackup/Managed/UnityEngine.pdb
[ 568/1311  0s] CopyFiles /Users/<USER>/Documents/GitHub/PerfumerProject/Library/Bee/Android/Prj/IL2CPP/Il2CppBackup/Managed/UnityEngine.TerrainModule.pdb
[ 569/1311  0s] CopyFiles /Users/<USER>/Documents/GitHub/PerfumerProject/Library/Bee/Android/Prj/IL2CPP/Il2CppBackup/Managed/NavMeshPlus.pdb
[ 570/1311  0s] CopyFiles /Users/<USER>/Documents/GitHub/PerfumerProject/Library/Bee/Android/Prj/IL2CPP/Il2CppBackup/Managed/UnityEngine.dll
[ 571/1311  0s] CopyFiles /Users/<USER>/Documents/GitHub/PerfumerProject/Library/Bee/Android/Prj/IL2CPP/Il2CppBackup/Managed/UnityEngine.PhysicsModule.dll
[ 572/1311  0s] CopyFiles /Users/<USER>/Documents/GitHub/PerfumerProject/Library/Bee/Android/Prj/IL2CPP/Il2CppBackup/Managed/Assembly-CSharp-firstpass.pdb
[ 573/1311  0s] CopyFiles /Users/<USER>/Documents/GitHub/PerfumerProject/Library/Bee/Android/Prj/IL2CPP/Il2CppBackup/Managed/Assembly-CSharp-firstpass.dll
[ 574/1311  0s] CopyFiles /Users/<USER>/Documents/GitHub/PerfumerProject/Library/Bee/Android/Prj/IL2CPP/Il2CppBackup/Managed/UnityEngine.PhysicsModule.pdb
[ 575/1311  0s] CopyFiles /Users/<USER>/Documents/GitHub/PerfumerProject/Library/Bee/Android/Prj/IL2CPP/Il2CppBackup/Managed/UnityEngine.TilemapModule.dll
[ 576/1311  0s] CopyFiles /Users/<USER>/Documents/GitHub/PerfumerProject/Library/Bee/Android/Prj/IL2CPP/Il2CppBackup/Managed/UnityEngine.CoreModule.dll
[ 577/1311  0s] CopyFiles /Users/<USER>/Documents/GitHub/PerfumerProject/Library/Bee/Android/Prj/IL2CPP/Il2CppBackup/Managed/UnityEngine.TerrainModule.dll
[ 578/1311  0s] CopyFiles /Users/<USER>/Documents/GitHub/PerfumerProject/Library/Bee/Android/Prj/IL2CPP/Il2CppBackup/Managed/UnityEngine.CoreModule.pdb
[ 579/1311  0s] CopyFiles /Users/<USER>/Documents/GitHub/PerfumerProject/Library/Bee/Android/Prj/IL2CPP/Il2CppBackup/Managed/UnityEngine.GridModule.pdb
[ 580/1311  0s] CopyFiles /Users/<USER>/Documents/GitHub/PerfumerProject/Library/Bee/Android/Prj/IL2CPP/Il2CppBackup/Managed/UnityEngine.UI.dll
[ 581/1311  0s] CopyFiles /Users/<USER>/Documents/GitHub/PerfumerProject/Library/Bee/Android/Prj/IL2CPP/Il2CppBackup/Managed/Assembly-CSharp.pdb
[ 582/1311  0s] CopyFiles /Users/<USER>/Documents/GitHub/PerfumerProject/Library/Bee/Android/Prj/IL2CPP/Il2CppBackup/Managed/UnityEngine.UI.pdb
[ 583/1311  0s] CopyFiles /Users/<USER>/Documents/GitHub/PerfumerProject/Library/Bee/Android/Prj/IL2CPP/Il2CppBackup/Managed/Assembly-CSharp.dll
[ 584/1311  0s] CopyFiles /Users/<USER>/Documents/GitHub/PerfumerProject/Library/Bee/Android/Prj/IL2CPP/Il2CppBackup/Managed/System.Numerics.dll
[ 585/1311  0s] CopyFiles /Users/<USER>/Documents/GitHub/PerfumerProject/Library/Bee/Android/Prj/IL2CPP/Il2CppBackup/Managed/UnityEngine.GridModule.dll
[ 586/1311  0s] CopyFiles /Users/<USER>/Documents/GitHub/PerfumerProject/Library/Bee/Android/Prj/IL2CPP/Il2CppBackup/Managed/Unity.VisualScripting.Core.dll
[ 587/1311  0s] CopyFiles /Users/<USER>/Documents/GitHub/PerfumerProject/Library/Bee/Android/Prj/IL2CPP/Il2CppBackup/Managed/UnityEngine.UIModule.dll
[ 588/1311  0s] CopyFiles /Users/<USER>/Documents/GitHub/PerfumerProject/Library/Bee/Android/Prj/IL2CPP/Il2CppBackup/Managed/Unity.VisualScripting.Core.pdb
[ 589/1311  0s] CopyFiles /Users/<USER>/Documents/GitHub/PerfumerProject/Library/Bee/Android/Prj/IL2CPP/Il2CppBackup/Managed/UnityEngine.AIModule.dll
[ 590/1311  0s] CopyFiles /Users/<USER>/Documents/GitHub/PerfumerProject/Library/Bee/Android/Prj/IL2CPP/Il2CppBackup/Managed/UnityEngine.VRModule.dll
[ 591/1311  0s] CopyFiles /Users/<USER>/Documents/GitHub/PerfumerProject/Library/Bee/Android/Prj/IL2CPP/Il2CppBackup/Managed/UnityEngine.UIModule.pdb
[ 592/1311  0s] CopyFiles /Users/<USER>/Documents/GitHub/PerfumerProject/Library/Bee/Android/Prj/IL2CPP/Il2CppBackup/Managed/UnityEngine.AIModule.pdb
[ 593/1311  0s] CopyFiles /Users/<USER>/Documents/GitHub/PerfumerProject/Library/Bee/Android/Prj/IL2CPP/Il2CppBackup/Managed/UnityEngine.XRModule.dll
[ 594/1311  0s] CopyFiles /Users/<USER>/Documents/GitHub/PerfumerProject/Library/Bee/Android/Prj/IL2CPP/Il2CppBackup/Managed/System.Configuration.dll
[ 595/1311  0s] CopyFiles /Users/<USER>/Documents/GitHub/PerfumerProject/Library/Bee/Android/Prj/IL2CPP/Il2CppBackup/Managed/UnityEngine.XRModule.pdb
[ 596/1311  0s] CopyFiles /Users/<USER>/Documents/GitHub/PerfumerProject/Library/Bee/Android/Prj/IL2CPP/Il2CppBackup/Managed/Unity.InputSystem.ForUI.pdb
[ 597/1311  0s] CopyFiles /Users/<USER>/Documents/GitHub/PerfumerProject/Library/Bee/Android/Prj/IL2CPP/Il2CppBackup/Managed/UnityEngine.VRModule.pdb
[ 598/1311  0s] CopyFiles /Users/<USER>/Documents/GitHub/PerfumerProject/Library/Bee/Android/Prj/IL2CPP/Il2CppBackup/Managed/ICSharpCode.SharpZipLib.dll
[ 599/1311  0s] CopyFiles /Users/<USER>/Documents/GitHub/PerfumerProject/Library/Bee/Android/Prj/IL2CPP/Il2CppBackup/Managed/UnityEngine.AudioModule.dll
[ 600/1311  0s] CopyFiles /Users/<USER>/Documents/GitHub/PerfumerProject/Library/Bee/Android/Prj/IL2CPP/Il2CppBackup/Managed/UnityEngine.IMGUIModule.dll
[ 601/1311  0s] CopyFiles /Users/<USER>/Documents/GitHub/PerfumerProject/Library/Bee/Android/Prj/IL2CPP/Il2CppBackup/Managed/Unity.InputSystem.ForUI.dll
[ 602/1311  0s] CopyFiles /Users/<USER>/Documents/GitHub/PerfumerProject/Library/Bee/Android/Prj/IL2CPP/Il2CppBackup/Managed/UnityEngine.AudioModule.pdb
[ 603/1311  0s] CopyFiles /Users/<USER>/Documents/GitHub/PerfumerProject/Library/Bee/Android/Prj/IL2CPP/Il2CppBackup/Managed/UnityEngine.IMGUIModule.pdb
[ 604/1311  0s] CopyFiles /Users/<USER>/Documents/GitHub/PerfumerProject/Library/Bee/Android/Prj/IL2CPP/Il2CppBackup/Managed/UnityEngine.InputModule.dll
[ 605/1311  0s] CopyFiles /Users/<USER>/Documents/GitHub/PerfumerProject/Library/Bee/Android/Prj/IL2CPP/Il2CppBackup/Managed/Mono.Security.dll
[ 606/1311  0s] CopyFiles /Users/<USER>/Documents/GitHub/PerfumerProject/Library/Bee/Android/Prj/IL2CPP/Il2CppBackup/Managed/UnityEngine.InputModule.pdb
[ 607/1311  0s] CopyFiles /Users/<USER>/Documents/GitHub/PerfumerProject/Library/Bee/Android/Prj/IL2CPP/Il2CppBackup/Managed/UnityEngine.SpriteShapeModule.pdb
[ 608/1311  0s] CopyFiles /Users/<USER>/Documents/GitHub/PerfumerProject/Library/Bee/Android/Prj/IL2CPP/Il2CppBackup/Managed/UnityEngine.SpriteShapeModule.dll
[ 609/1311  0s] CopyFiles /Users/<USER>/Documents/GitHub/PerfumerProject/Library/Bee/Android/Prj/IL2CPP/Il2CppBackup/Managed/UnityEngine.AndroidJNIModule.dll
[ 610/1311  0s] CopyFiles /Users/<USER>/Documents/GitHub/PerfumerProject/Library/Bee/Android/Prj/IL2CPP/Il2CppBackup/Managed/UnityEngine.InputLegacyModule.pdb
[ 611/1311  0s] ExtractUsedFeatures Library/Bee/artifacts/Android/Features/Assembly-CSharp-FeaturesChecked.txt
[ 612/1311  0s] ExtractUsedFeatures Library/Bee/artifacts/Android/Features/UnityEngine.UnityWebRequestModule-FeaturesChecked.txt
[ 613/1311  0s] ICallRegistrationGenerator /Users/<USER>/Documents/GitHub/PerfumerProject/Library/Bee/artifacts/Android/il2cppOutput/UnityICallRegistration.cpp
[ 619/1311  0s] Adding note Library/Bee/artifacts/Android/libunity/arm64-v8a/stripped/libunity.so -> /Users/<USER>/Documents/GitHub/PerfumerProject/Library/Bee/Android/Prj/IL2CPP/Gradle/unityLibrary/src/main/jniLibs/arm64-v8a/libunity.so /Users/<USER>/Documents/GitHub/PerfumerProject/Library/Bee/Android/Prj/IL2CPP/Gradle/unityLibrary/src/main/jniLibs/arm64-v8a/libunity.so
[ 620/1311  0s] Generate Manifests Library/Bee/artifacts/Android/Manifest/LauncherManifestDiag.txt (+3 others)
[BUSY       6s] IL2CPP_CodeGen /Users/<USER>/Documents/GitHub/PerfumerProject/Library/Bee/artifacts/il2cpp_conv_jdy3.traceevents
[W] opendir() failed: /Users/<USER>/Documents/GitHub/PerfumerProject/Library/Bee/artifacts/Android/il2cppOutput/cpp/Symbols
[ 621/1311  6s] IL2CPP_CodeGen /Users/<USER>/Documents/GitHub/PerfumerProject/Library/Bee/artifacts/il2cpp_conv_jdy3.traceevents
/Applications/Unity/Hub/Editor/2022.3.62f1/Unity.app/Contents/il2cpp/build/deploy/il2cpp --convert-to-cpp --assembly=Library/Bee/artifacts/Android/ManagedStripped/Assembly-CSharp-firstpass.dll --assembly=Library/Bee/artifacts/Android/ManagedStripped/Assembly-CSharp.dll --assembly=Library/Bee/artifacts/Android/ManagedStripped/DOTween.dll --assembly=Library/Bee/artifacts/Android/ManagedStripped/DOTweenPro.dll --assembly=Library/Bee/artifacts/Android/ManagedStripped/Fungus.dll --assembly=Library/Bee/artifacts/Android/ManagedStripped/ICSharpCode.SharpZipLib.dll --assembly=Library/Bee/artifacts/Android/ManagedStripped/Mono.Security.dll --assembly=Library/Bee/artifacts/Android/ManagedStripped/mscorlib.dll --assembly=Library/Bee/artifacts/Android/ManagedStripped/NavMeshPlus.dll --assembly=Library/Bee/artifacts/Android/ManagedStripped/System.Configuration.dll --assembly=Library/Bee/artifacts/Android/ManagedStripped/System.Core.dll --assembly=Library/Bee/artifacts/Android/ManagedStripped/System.dll --assembly=Library/Bee/artifacts/Android/ManagedStripped/System.Numerics.dll --assembly=Library/Bee/artifacts/Android/ManagedStripped/System.Xml.dll --assembly=Library/Bee/artifacts/Android/ManagedStripped/Unity.InputSystem.dll --assembly=Library/Bee/artifacts/Android/ManagedStripped/Unity.InputSystem.ForUI.dll --assembly=Library/Bee/artifacts/Android/ManagedStripped/Unity.TextMeshPro.dll --assembly=Library/Bee/artifacts/Android/ManagedStripped/Unity.VisualScripting.Core.dll --assembly=Library/Bee/artifacts/Android/ManagedStripped/UnityEngine.AIModule.dll --assembly=Library/Bee/artifacts/Android/ManagedStripped/UnityEngine.AndroidJNIModule.dll --assembly=Library/Bee/artifacts/Android/ManagedStripped/UnityEngine.AnimationModule.dll --assembly=Library/Bee/artifacts/Android/ManagedStripped/UnityEngine.AudioModule.dll --assembly=Library/Bee/artifacts/Android/ManagedStripped/UnityEngine.CoreModule.dll --assembly=Library/Bee/artifacts/Android/ManagedStripped/UnityEngine.dll --assembly=Library/Bee/artifacts/Android/ManagedStripped/UnityEngine.GridModule.dll --assembly=Library/Bee/artifacts/Android/ManagedStripped/UnityEngine.IMGUIModule.dll --assembly=Library/Bee/artifacts/Android/ManagedStripped/UnityEngine.InputLegacyModule.dll --assembly=Library/Bee/artifacts/Android/ManagedStripped/UnityEngine.InputModule.dll --assembly=Library/Bee/artifacts/Android/ManagedStripped/UnityEngine.JSONSerializeModule.dll --assembly=Library/Bee/artifacts/Android/ManagedStripped/UnityEngine.ParticleSystemModule.dll --assembly=Library/Bee/artifacts/Android/ManagedStripped/UnityEngine.Physics2DModule.dll --assembly=Library/Bee/artifacts/Android/ManagedStripped/UnityEngine.PhysicsModule.dll --assembly=Library/Bee/artifacts/Android/ManagedStripped/UnityEngine.PropertiesModule.dll --assembly=Library/Bee/artifacts/Android/ManagedStripped/UnityEngine.SharedInternalsModule.dll --assembly=Library/Bee/artifacts/Android/ManagedStripped/UnityEngine.SpriteShapeModule.dll --assembly=Library/Bee/artifacts/Android/ManagedStripped/UnityEngine.SubsystemsModule.dll --assembly=Library/Bee/artifacts/Android/ManagedStripped/UnityEngine.TerrainModule.dll --assembly=Library/Bee/artifacts/Android/ManagedStripped/UnityEngine.TextCoreFontEngineModule.dll --assembly=Library/Bee/artifacts/Android/ManagedStripped/UnityEngine.TextCoreTextEngineModule.dll --assembly=Library/Bee/artifacts/Android/ManagedStripped/UnityEngine.TextRenderingModule.dll --assembly=Library/Bee/artifacts/Android/ManagedStripped/UnityEngine.TilemapModule.dll --assembly=Library/Bee/artifacts/Android/ManagedStripped/UnityEngine.UI.dll --assembly=Library/Bee/artifacts/Android/ManagedStripped/UnityEngine.UIElementsModule.dll --assembly=Library/Bee/artifacts/Android/ManagedStripped/UnityEngine.UIModule.dll --assembly=Library/Bee/artifacts/Android/ManagedStripped/UnityEngine.UnityWebRequestModule.dll --assembly=Library/Bee/artifacts/Android/ManagedStripped/UnityEngine.VRModule.dll --assembly=Library/Bee/artifacts/Android/ManagedStripped/UnityEngine.XRModule.dll --generatedcppdir=/Users/<USER>/Documents/GitHub/PerfumerProject/Library/Bee/artifacts/Android/il2cppOutput/cpp --symbols-folder=/Users/<USER>/Documents/GitHub/PerfumerProject/Library/Bee/artifacts/Android/il2cppOutput/cpp/Symbols --enable-analytics --emit-null-checks --enable-array-bounds-check --dotnetprofile=unityaot-linux --profiler-report --profiler-output-file=/Users/<USER>/Documents/GitHub/PerfumerProject/Library/Bee/artifacts/il2cpp_conv_jdy3.traceevents --print-command-line --data-folder=/Users/<USER>/Documents/GitHub/PerfumerProject/Library/Bee/artifacts/Android/il2cppOutput/data
[ 952/1311  0s] CopyFiles /Users/<USER>/Documents/GitHub/PerfumerProject/Library/Bee/artifacts/Android/il2cppOutput/build/arm64-v8a/Data/Resources/mscorlib.dll-resources.dat
[ 953/1311  0s] CopyFiles /Users/<USER>/Documents/GitHub/PerfumerProject/Library/Bee/artifacts/Android/il2cppOutput/build/arm64-v8a/Data/Metadata/global-metadata.dat
[ 954/1311  0s] CopyFiles /Users/<USER>/Documents/GitHub/PerfumerProject/Library/Bee/Android/Prj/IL2CPP/Il2CppBackup/il2cppOutput/UnityEngine.JSONSerializeModule_CodeGen.c
[ 955/1311  0s] CopyFiles /Users/<USER>/Documents/GitHub/PerfumerProject/Library/Bee/Android/Prj/IL2CPP/Il2CppBackup/il2cppOutput/Il2CppCCFieldValuesTable.cpp
[ 956/1311  0s] CopyFiles /Users/<USER>/Documents/GitHub/PerfumerProject/Library/Bee/Android/Prj/IL2CPP/Il2CppBackup/il2cppOutput/UnityEngine.TextRenderingModule_CodeGen.c
[ 957/1311  0s] CopyFiles /Users/<USER>/Documents/GitHub/PerfumerProject/Library/Bee/Android/Prj/IL2CPP/Il2CppBackup/il2cppOutput/UnityEngine.GridModule_CodeGen.c
[ 958/1311  0s] CopyFiles /Users/<USER>/Documents/GitHub/PerfumerProject/Library/Bee/Android/Prj/IL2CPP/Il2CppBackup/il2cppOutput/UnityEngine.CoreModule_CodeGen.c
[ 959/1311  0s] CopyFiles /Users/<USER>/Documents/GitHub/PerfumerProject/Library/Bee/Android/Prj/IL2CPP/Il2CppBackup/il2cppOutput/Il2CppGenericMethodDefinitions.c
[ 960/1311  0s] CopyFiles /Users/<USER>/Documents/GitHub/PerfumerProject/Library/Bee/Android/Prj/IL2CPP/Il2CppBackup/il2cppOutput/Il2CppCCalculateFieldValues2.cpp
[ 961/1311  0s] CopyFiles /Users/<USER>/Documents/GitHub/PerfumerProject/Library/Bee/Android/Prj/IL2CPP/Il2CppBackup/il2cppOutput/UnityEngine.UIElementsModule.cpp
[ 963/1311  0s] CopyFiles /Users/<USER>/Documents/GitHub/PerfumerProject/Library/Bee/Android/Prj/IL2CPP/Il2CppBackup/il2cppOutput/UnityEngine.SubsystemsModule.cpp
[ 964/1311  0s] CopyFiles /Users/<USER>/Documents/GitHub/PerfumerProject/Library/Bee/Android/Prj/IL2CPP/Il2CppBackup/il2cppOutput/UnityEngine.AndroidJNIModule.cpp
[ 965/1311  0s] CopyFiles /Users/<USER>/Documents/GitHub/PerfumerProject/Library/Bee/Android/Prj/IL2CPP/Il2CppBackup/il2cppOutput/UnityEngine.PropertiesModule.cpp
[ 966/1311  0s] CopyFiles /Users/<USER>/Documents/GitHub/PerfumerProject/Library/Bee/Android/Prj/IL2CPP/Il2CppBackup/il2cppOutput/Il2CppCCalculateFieldValues1.cpp
[ 967/1311  0s] CopyFiles /Users/<USER>/Documents/GitHub/PerfumerProject/Library/Bee/Android/Prj/IL2CPP/Il2CppBackup/il2cppOutput/Il2CppCCalculateFieldValues3.cpp
[ 968/1311  0s] CopyFiles /Users/<USER>/Documents/GitHub/PerfumerProject/Library/Bee/Android/Prj/IL2CPP/Il2CppBackup/il2cppOutput/UnityEngine.UIElementsModule_CodeGen.c
[ 969/1311  0s] CopyFiles /Users/<USER>/Documents/GitHub/PerfumerProject/Library/Bee/Android/Prj/IL2CPP/Il2CppBackup/il2cppOutput/UnityEngine.PropertiesModule_CodeGen.c
[ 970/1311  0s] CopyFiles /Users/<USER>/Documents/GitHub/PerfumerProject/Library/Bee/Android/Prj/IL2CPP/Il2CppBackup/il2cppOutput/UnityEngine.SubsystemsModule_CodeGen.c
[ 971/1311  0s] CopyFiles /Users/<USER>/Documents/GitHub/PerfumerProject/Library/Bee/Android/Prj/IL2CPP/Il2CppBackup/il2cppOutput/UnityEngine.AndroidJNIModule_CodeGen.c
[ 972/1311  0s] CopyFiles /Users/<USER>/Documents/GitHub/PerfumerProject/Library/Bee/Android/Prj/IL2CPP/Il2CppBackup/il2cppOutput/Il2CppCodeRegistration.cpp
[ 973/1311  0s] CopyFiles /Users/<USER>/Documents/GitHub/PerfumerProject/Library/Bee/Android/Prj/IL2CPP/Il2CppBackup/il2cppOutput/UnityEngine.GridModule.cpp
[ 974/1311  0s] CopyFiles /Users/<USER>/Documents/GitHub/PerfumerProject/Library/Bee/Android/Prj/IL2CPP/Il2CppBackup/il2cppOutput/UnityEngine.CoreModule.cpp
[ 975/1311  0s] CopyFiles /Users/<USER>/Documents/GitHub/PerfumerProject/Library/Bee/Android/Prj/IL2CPP/Il2CppBackup/il2cppOutput/Il2CppGenericMethodTable.c
[ 976/1311  0s] CopyFiles /Users/<USER>/Documents/GitHub/PerfumerProject/Library/Bee/Android/Prj/IL2CPP/Il2CppBackup/il2cppOutput/Il2CppInteropDataTable.cpp
[ 977/1311  0s] CopyFiles /Users/<USER>/Documents/GitHub/PerfumerProject/Library/Bee/Android/Prj/IL2CPP/Il2CppBackup/il2cppOutput/UnityEngine.VRModule_CodeGen.c
[ 978/1311  0s] CopyFiles /Users/<USER>/Documents/GitHub/PerfumerProject/Library/Bee/Android/Prj/IL2CPP/Gradle/unityLibrary/src/main/assets/bin/Data/Managed/Metadata/global-metadata.dat
[ 979/1311  0s] CopyFiles /Users/<USER>/Documents/GitHub/PerfumerProject/Library/Bee/Android/Prj/IL2CPP/Il2CppBackup/il2cppOutput/UnityEngine.UIModule_CodeGen.c
[ 980/1311  0s] CopyFiles /Users/<USER>/Documents/GitHub/PerfumerProject/Library/Bee/Android/Prj/IL2CPP/Il2CppBackup/il2cppOutput/UnityEngine.AIModule_CodeGen.c
[ 981/1311  0s] CopyFiles /Users/<USER>/Documents/GitHub/PerfumerProject/Library/Bee/Android/Prj/IL2CPP/Il2CppBackup/il2cppOutput/System.Configuration_CodeGen.c
[ 982/1311  0s] CopyFiles /Users/<USER>/Documents/GitHub/PerfumerProject/Library/Bee/Android/Prj/IL2CPP/Il2CppBackup/il2cppOutput/UnityEngine.XRModule_CodeGen.c
[ 983/1311  0s] CopyFiles /Users/<USER>/Documents/GitHub/PerfumerProject/Library/Bee/Android/Prj/IL2CPP/Il2CppBackup/il2cppOutput/Unity.VisualScripting.Core.cpp
[ 984/1311  0s] CopyFiles /Users/<USER>/Documents/GitHub/PerfumerProject/Library/Bee/Android/Prj/IL2CPP/Il2CppBackup/il2cppOutput/Il2CppCCalculateTypeValues.cpp
[ 985/1311  0s] CopyFiles /Users/<USER>/Documents/GitHub/PerfumerProject/Library/Bee/Android/Prj/IL2CPP/Il2CppBackup/il2cppOutput/ICSharpCode.SharpZipLib__2.cpp
[ 986/1311  0s] CopyFiles /Users/<USER>/Documents/GitHub/PerfumerProject/Library/Bee/Android/Prj/IL2CPP/Il2CppBackup/il2cppOutput/ICSharpCode.SharpZipLib__1.cpp
[ 987/1311  0s] CopyFiles /Users/<USER>/Documents/GitHub/PerfumerProject/Library/Bee/Android/Prj/IL2CPP/Il2CppBackup/il2cppOutput/UnityEngine.IMGUIModule__1.cpp
[ 988/1311  0s] CopyFiles /Users/<USER>/Documents/GitHub/PerfumerProject/Library/Bee/Android/Prj/IL2CPP/Il2CppBackup/il2cppOutput/Il2CppGenericInstDefinitions.c
[ 989/1311  0s] CopyFiles /Users/<USER>/Documents/GitHub/PerfumerProject/Library/Bee/Android/Prj/IL2CPP/Il2CppBackup/il2cppOutput/UnityEngine.AnimationModule_CodeGen.c
[ 990/1311  0s] CopyFiles /Users/<USER>/Documents/GitHub/PerfumerProject/Library/Bee/Android/Prj/IL2CPP/Il2CppBackup/il2cppOutput/UnityEngine.UnityWebRequestModule.cpp
[ 991/1311  0s] CopyFiles /Users/<USER>/Documents/GitHub/PerfumerProject/Library/Bee/Android/Prj/IL2CPP/Il2CppBackup/il2cppOutput/UnityEngine.Physics2DModule_CodeGen.c
[ 992/1311  0s] CopyFiles /Users/<USER>/Documents/GitHub/PerfumerProject/Library/Bee/Android/Prj/IL2CPP/Il2CppBackup/il2cppOutput/UnityEngine.SharedInternalsModule.cpp
[ 993/1311  0s] CopyFiles /Users/<USER>/Documents/GitHub/PerfumerProject/Library/Bee/Android/Prj/IL2CPP/Il2CppBackup/il2cppOutput/Il2CppUnresolvedIndirectCallStubs.cpp
[ 994/1311  0s] CopyFiles /Users/<USER>/Documents/GitHub/PerfumerProject/Library/Bee/Android/Prj/IL2CPP/Il2CppBackup/il2cppOutput/UnityEngine.TextCoreTextEngineModule_CodeGen.c
[ 995/1311  0s] CopyFiles /Users/<USER>/Documents/GitHub/PerfumerProject/Library/Bee/Android/Prj/IL2CPP/Il2CppBackup/il2cppOutput/UnityEngine.TextCoreFontEngineModule_CodeGen.c
[ 996/1311  0s] CopyFiles /Users/<USER>/Documents/GitHub/PerfumerProject/Library/Bee/Android/Prj/IL2CPP/Il2CppBackup/il2cppOutput/UnityEngine.TextCoreTextEngineModule__2.cpp
[ 997/1311  0s] CopyFiles /Users/<USER>/Documents/GitHub/PerfumerProject/Library/Bee/Android/Prj/IL2CPP/Il2CppBackup/il2cppOutput/UnityEngine.TextCoreTextEngineModule__1.cpp
[ 998/1311  0s] CopyFiles /Users/<USER>/Documents/GitHub/PerfumerProject/Library/Bee/Android/Prj/IL2CPP/Il2CppBackup/il2cppOutput/UnityEngine.TextCoreTextEngineModule__3.cpp
[ 999/1311  0s] CopyFiles /Users/<USER>/Documents/GitHub/PerfumerProject/Library/Bee/Android/Prj/IL2CPP/Il2CppBackup/il2cppOutput/Unity.VisualScripting.Core__6.cpp
[1000/1311  0s] CopyFiles /Users/<USER>/Documents/GitHub/PerfumerProject/Library/Bee/Android/Prj/IL2CPP/Il2CppBackup/il2cppOutput/Unity.VisualScripting.Core__5.cpp
[1001/1311  0s] CopyFiles /Users/<USER>/Documents/GitHub/PerfumerProject/Library/Bee/Android/Prj/IL2CPP/Il2CppBackup/il2cppOutput/Unity.VisualScripting.Core__1.cpp
[1002/1311  0s] CopyFiles /Users/<USER>/Documents/GitHub/PerfumerProject/Library/Bee/Android/Prj/IL2CPP/Il2CppBackup/il2cppOutput/Unity.VisualScripting.Core__4.cpp
[1003/1311  0s] CopyFiles /Users/<USER>/Documents/GitHub/PerfumerProject/Library/Bee/Android/Prj/IL2CPP/Il2CppBackup/il2cppOutput/Il2CppGenericAdjustorThunkTable.c
[1004/1311  0s] CopyFiles /Users/<USER>/Documents/GitHub/PerfumerProject/Library/Bee/Android/Prj/IL2CPP/Il2CppBackup/il2cppOutput/Il2CppGenericMethodPointerTable.c
[1005/1311  0s] CopyFiles /Users/<USER>/Documents/GitHub/PerfumerProject/Library/Bee/Android/Prj/IL2CPP/Il2CppBackup/il2cppOutput/Unity.VisualScripting.Core__2.cpp
[1006/1311  0s] CopyFiles /Users/<USER>/Documents/GitHub/PerfumerProject/Library/Bee/Android/Prj/IL2CPP/Il2CppBackup/il2cppOutput/Unity.VisualScripting.Core__3.cpp
[1007/1311  0s] CopyFiles /Users/<USER>/Documents/GitHub/PerfumerProject/Library/Bee/Android/Prj/IL2CPP/Il2CppBackup/il2cppOutput/UnityEngine.IMGUIModule_CodeGen.c
[1008/1311  0s] CopyFiles /Users/<USER>/Documents/GitHub/PerfumerProject/Library/Bee/Android/Prj/IL2CPP/Il2CppBackup/il2cppOutput/UnityEngine.AudioModule_CodeGen.c
[1009/1311  0s] CopyFiles /Users/<USER>/Documents/GitHub/PerfumerProject/Library/Bee/Android/Prj/IL2CPP/Il2CppBackup/il2cppOutput/UnityEngine.InputModule_CodeGen.c
[1010/1311  0s] CopyFiles /Users/<USER>/Documents/GitHub/PerfumerProject/Library/Bee/Android/Prj/IL2CPP/Il2CppBackup/il2cppOutput/ICSharpCode.SharpZipLib_CodeGen.c
[1011/1311  0s] CopyFiles /Users/<USER>/Documents/GitHub/PerfumerProject/Library/Bee/Android/Prj/IL2CPP/Il2CppBackup/il2cppOutput/Unity.InputSystem.ForUI_CodeGen.c
[1012/1311  0s] CopyFiles /Users/<USER>/Documents/GitHub/PerfumerProject/Library/Bee/Android/Prj/IL2CPP/Il2CppBackup/il2cppOutput/UnityEngine.InputLegacyModule.cpp
[1013/1311  0s] CopyFiles /Users/<USER>/Documents/GitHub/PerfumerProject/Library/Bee/Android/Prj/IL2CPP/Il2CppBackup/il2cppOutput/UnityEngine.SpriteShapeModule.cpp
[1014/1311  0s] CopyFiles /Users/<USER>/Documents/GitHub/PerfumerProject/Library/Bee/Android/Prj/IL2CPP/Il2CppBackup/il2cppOutput/Il2CppCCalculateTypeValues1.cpp
[1015/1311  0s] CopyFiles /Users/<USER>/Documents/GitHub/PerfumerProject/Library/Bee/Android/Prj/IL2CPP/Il2CppBackup/il2cppOutput/UnityEngine.Physics2DModule.cpp
[1016/1311  0s] CopyFiles /Users/<USER>/Documents/GitHub/PerfumerProject/Library/Bee/Android/Prj/IL2CPP/Il2CppBackup/il2cppOutput/UnityEngine.AnimationModule.cpp
[1017/1311  0s] CopyFiles /Users/<USER>/Documents/GitHub/PerfumerProject/Library/Bee/Android/Prj/IL2CPP/Il2CppBackup/il2cppOutput/Il2CppCCalculateFieldValues.cpp
[1018/1311  0s] CopyFiles /Users/<USER>/Documents/GitHub/PerfumerProject/Library/Bee/Android/Prj/IL2CPP/Il2CppBackup/il2cppOutput/UnityEngine.SpriteShapeModule_CodeGen.c
[1019/1311  0s] CopyFiles /Users/<USER>/Documents/GitHub/PerfumerProject/Library/Bee/Android/Prj/IL2CPP/Il2CppBackup/il2cppOutput/UnityEngine.InputLegacyModule_CodeGen.c
[1020/1311  0s] CopyFiles /Users/<USER>/Documents/GitHub/PerfumerProject/Library/Bee/Android/Prj/IL2CPP/Il2CppBackup/il2cppOutput/UnityEngine.UIElementsModule__10.cpp
[1021/1311  0s] CopyFiles /Users/<USER>/Documents/GitHub/PerfumerProject/Library/Bee/Android/Prj/IL2CPP/Il2CppBackup/il2cppOutput/UnityEngine.UIElementsModule__13.cpp
[1022/1311  0s] CopyFiles /Users/<USER>/Documents/GitHub/PerfumerProject/Library/Bee/Android/Prj/IL2CPP/Il2CppBackup/il2cppOutput/UnityEngine.UIElementsModule__14.cpp
[1023/1311  0s] CopyFiles /Users/<USER>/Documents/GitHub/PerfumerProject/Library/Bee/Android/Prj/IL2CPP/Il2CppBackup/il2cppOutput/UnityEngine.UIElementsModule__15.cpp
[1024/1311  0s] CopyFiles /Users/<USER>/Documents/GitHub/PerfumerProject/Library/Bee/Android/Prj/IL2CPP/Il2CppBackup/il2cppOutput/UnityEngine.ParticleSystemModule.cpp
[1025/1311  0s] CopyFiles /Users/<USER>/Documents/GitHub/PerfumerProject/Library/Bee/Android/Prj/IL2CPP/Il2CppBackup/il2cppOutput/Unity.VisualScripting.Core_CodeGen.c
[1026/1311  0s] CopyFiles /Users/<USER>/Documents/GitHub/PerfumerProject/Library/Bee/Android/Prj/IL2CPP/Il2CppBackup/il2cppOutput/UnityEngine.UIElementsModule__12.cpp
[1027/1311  0s] CopyFiles /Users/<USER>/Documents/GitHub/PerfumerProject/Library/Bee/Android/Prj/IL2CPP/Il2CppBackup/il2cppOutput/Il2CppReversePInvokeWrapperTable.cpp
[1028/1311  0s] CopyFiles /Users/<USER>/Documents/GitHub/PerfumerProject/Library/Bee/Android/Prj/IL2CPP/Il2CppBackup/il2cppOutput/UnityEngine.UIElementsModule__11.cpp
[1029/1311  0s] CopyFiles /Users/<USER>/Documents/GitHub/PerfumerProject/Library/Bee/Android/Prj/IL2CPP/Il2CppBackup/il2cppOutput/Il2CppTypeDefinitions.c
[1030/1311  0s] CopyFiles /Users/<USER>/Documents/GitHub/PerfumerProject/Library/Bee/Android/Prj/IL2CPP/Il2CppBackup/il2cppOutput/Mono.Security_CodeGen.c
[1031/1311  0s] CopyFiles /Users/<USER>/Documents/GitHub/PerfumerProject/Library/Bee/Android/Prj/IL2CPP/Il2CppBackup/il2cppOutput/Generics__65.cpp
[1032/1311  0s] CopyFiles /Users/<USER>/Documents/GitHub/PerfumerProject/Library/Bee/Android/Prj/IL2CPP/Il2CppBackup/il2cppOutput/Generics__68.cpp
[1033/1311  0s] CopyFiles /Users/<USER>/Documents/GitHub/PerfumerProject/Library/Bee/Android/Prj/IL2CPP/Il2CppBackup/il2cppOutput/Generics__81.cpp
[1034/1311  0s] CopyFiles /Users/<USER>/Documents/GitHub/PerfumerProject/Library/Bee/Android/Prj/IL2CPP/Il2CppBackup/il2cppOutput/Generics__44.cpp
[1035/1311  0s] CopyFiles /Users/<USER>/Documents/GitHub/PerfumerProject/Library/Bee/Android/Prj/IL2CPP/Il2CppBackup/il2cppOutput/Generics__76.cpp
[1036/1311  0s] CopyFiles /Users/<USER>/Documents/GitHub/PerfumerProject/Library/Bee/Android/Prj/IL2CPP/Il2CppBackup/il2cppOutput/Generics__64.cpp
[1037/1311  0s] CopyFiles /Users/<USER>/Documents/GitHub/PerfumerProject/Library/Bee/Android/Prj/IL2CPP/Il2CppBackup/il2cppOutput/Generics__30.cpp
[1038/1311  0s] CopyFiles /Users/<USER>/Documents/GitHub/PerfumerProject/Library/Bee/Android/Prj/IL2CPP/Il2CppBackup/il2cppOutput/mscorlib__18.cpp
[1039/1311  0s] CopyFiles /Users/<USER>/Documents/GitHub/PerfumerProject/Library/Bee/Android/Prj/IL2CPP/Il2CppBackup/il2cppOutput/Generics__18.cpp
[1040/1311  0s] CopyFiles /Users/<USER>/Documents/GitHub/PerfumerProject/Library/Bee/Android/Prj/IL2CPP/Il2CppBackup/il2cppOutput/Generics__32.cpp
[1041/1311  0s] CopyFiles /Users/<USER>/Documents/GitHub/PerfumerProject/Library/Bee/Android/Prj/IL2CPP/Il2CppBackup/il2cppOutput/Generics__24.cpp
[1042/1311  0s] CopyFiles /Users/<USER>/Documents/GitHub/PerfumerProject/Library/Bee/Android/Prj/IL2CPP/Il2CppBackup/il2cppOutput/Generics__75.cpp
[1043/1311  0s] CopyFiles /Users/<USER>/Documents/GitHub/PerfumerProject/Library/Bee/Android/Prj/IL2CPP/Il2CppBackup/il2cppOutput/Generics__63.cpp
[1044/1311  0s] CopyFiles /Users/<USER>/Documents/GitHub/PerfumerProject/Library/Bee/Android/Prj/IL2CPP/Il2CppBackup/il2cppOutput/Generics__59.cpp
[1045/1311  0s] CopyFiles /Users/<USER>/Documents/GitHub/PerfumerProject/Library/Bee/Android/Prj/IL2CPP/Il2CppBackup/il2cppOutput/mscorlib__22.cpp
[1046/1311  0s] CopyFiles /Users/<USER>/Documents/GitHub/PerfumerProject/Library/Bee/Android/Prj/IL2CPP/Il2CppBackup/il2cppOutput/Generics__22.cpp
[1047/1311  0s] CopyFiles /Users/<USER>/Documents/GitHub/PerfumerProject/Library/Bee/Android/Prj/IL2CPP/Il2CppBackup/il2cppOutput/Generics__39.cpp
[1048/1311  0s] CopyFiles /Users/<USER>/Documents/GitHub/PerfumerProject/Library/Bee/Android/Prj/IL2CPP/Il2CppBackup/il2cppOutput/Generics__52.cpp
[1049/1311  0s] CopyFiles /Users/<USER>/Documents/GitHub/PerfumerProject/Library/Bee/Android/Prj/IL2CPP/Il2CppBackup/il2cppOutput/Generics__31.cpp
[1050/1311  0s] CopyFiles /Users/<USER>/Documents/GitHub/PerfumerProject/Library/Bee/Android/Prj/IL2CPP/Il2CppBackup/il2cppOutput/Generics__42.cpp
[1051/1311  0s] CopyFiles /Users/<USER>/Documents/GitHub/PerfumerProject/Library/Bee/Android/Prj/IL2CPP/Il2CppBackup/il2cppOutput/Generics__53.cpp
[1052/1311  0s] CopyFiles /Users/<USER>/Documents/GitHub/PerfumerProject/Library/Bee/Android/Prj/IL2CPP/Il2CppBackup/il2cppOutput/Generics__62.cpp
[1053/1311  0s] CopyFiles /Users/<USER>/Documents/GitHub/PerfumerProject/Library/Bee/Android/Prj/IL2CPP/Il2CppBackup/il2cppOutput/Generics__56.cpp
[1054/1311  0s] CopyFiles /Users/<USER>/Documents/GitHub/PerfumerProject/Library/Bee/Android/Prj/IL2CPP/Il2CppBackup/il2cppOutput/Generics__36.cpp
[1055/1311  0s] CopyFiles /Users/<USER>/Documents/GitHub/PerfumerProject/Library/Bee/Android/Prj/IL2CPP/Il2CppBackup/il2cppOutput/Generics__27.cpp
[1056/1311  0s] CopyFiles /Users/<USER>/Documents/GitHub/PerfumerProject/Library/Bee/Android/Prj/IL2CPP/Il2CppBackup/il2cppOutput/Generics__28.cpp
[1057/1311  0s] CopyFiles /Users/<USER>/Documents/GitHub/PerfumerProject/Library/Bee/Android/Prj/IL2CPP/Il2CppBackup/il2cppOutput/Fungus_CodeGen.c
[1058/1311  0s] CopyFiles /Users/<USER>/Documents/GitHub/PerfumerProject/Library/Bee/Android/Prj/IL2CPP/Il2CppBackup/il2cppOutput/System_CodeGen.c
[1059/1311  0s] CopyFiles /Users/<USER>/Documents/GitHub/PerfumerProject/Library/Bee/Android/Prj/IL2CPP/Il2CppBackup/il2cppOutput/Generics__35.cpp
[1060/1311  0s] CopyFiles /Users/<USER>/Documents/GitHub/PerfumerProject/Library/Bee/Android/Prj/IL2CPP/Il2CppBackup/il2cppOutput/Generics__78.cpp
[1061/1311  0s] CopyFiles /Users/<USER>/Documents/GitHub/PerfumerProject/Library/Bee/Android/Prj/IL2CPP/Il2CppBackup/il2cppOutput/Generics__47.cpp
[1062/1311  0s] CopyFiles /Users/<USER>/Documents/GitHub/PerfumerProject/Library/Bee/Android/Prj/IL2CPP/Il2CppBackup/il2cppOutput/Generics__38.cpp
[1063/1311  0s] CopyFiles /Users/<USER>/Documents/GitHub/PerfumerProject/Library/Bee/Android/Prj/IL2CPP/Il2CppBackup/il2cppOutput/Generics__50.cpp
[1064/1311  0s] CopyFiles /Users/<USER>/Documents/GitHub/PerfumerProject/Library/Bee/Android/Prj/IL2CPP/Il2CppBackup/il2cppOutput/Generics__51.cpp
[1065/1311  0s] CopyFiles /Users/<USER>/Documents/GitHub/PerfumerProject/Library/Bee/Android/Prj/IL2CPP/Il2CppBackup/il2cppOutput/Generics__77.cpp
[1066/1311  0s] CopyFiles /Users/<USER>/Documents/GitHub/PerfumerProject/Library/Bee/Android/Prj/IL2CPP/Il2CppBackup/il2cppOutput/Generics__71.cpp
[1067/1311  0s] CopyFiles /Users/<USER>/Documents/GitHub/PerfumerProject/Library/Bee/Android/Prj/IL2CPP/Il2CppBackup/il2cppOutput/Generics__34.cpp
[1068/1311  0s] CopyFiles /Users/<USER>/Documents/GitHub/PerfumerProject/Library/Bee/Android/Prj/IL2CPP/Il2CppBackup/il2cppOutput/Generics__26.cpp
[1069/1311  0s] CopyFiles /Users/<USER>/Documents/GitHub/PerfumerProject/Library/Bee/Android/Prj/IL2CPP/Il2CppBackup/il2cppOutput/Generics__25.cpp
[1070/1311  0s] CopyFiles /Users/<USER>/Documents/GitHub/PerfumerProject/Library/Bee/Android/Prj/IL2CPP/Il2CppBackup/il2cppOutput/Generics__54.cpp
[1071/1311  0s] CopyFiles /Users/<USER>/Documents/GitHub/PerfumerProject/Library/Bee/Android/Prj/IL2CPP/Il2CppBackup/il2cppOutput/mscorlib__21.cpp
[1072/1311  0s] CopyFiles /Users/<USER>/Documents/GitHub/PerfumerProject/Library/Bee/Android/Prj/IL2CPP/Il2CppBackup/il2cppOutput/Generics__21.cpp
[1073/1311  0s] CopyFiles /Users/<USER>/Documents/GitHub/PerfumerProject/Library/Bee/Android/Prj/IL2CPP/Il2CppBackup/il2cppOutput/mscorlib__19.cpp
[1074/1311  0s] CopyFiles /Users/<USER>/Documents/GitHub/PerfumerProject/Library/Bee/Android/Prj/IL2CPP/Il2CppBackup/il2cppOutput/Generics__19.cpp
[1075/1311  0s] CopyFiles /Users/<USER>/Documents/GitHub/PerfumerProject/Library/Bee/Android/Prj/IL2CPP/Il2CppBackup/il2cppOutput/Generics__70.cpp
[1076/1311  0s] CopyFiles /Users/<USER>/Documents/GitHub/PerfumerProject/Library/Bee/Android/Prj/IL2CPP/Il2CppBackup/il2cppOutput/mscorlib__12.cpp
[1077/1311  0s] CopyFiles /Users/<USER>/Documents/GitHub/PerfumerProject/Library/Bee/Android/Prj/IL2CPP/Il2CppBackup/il2cppOutput/Generics__12.cpp
[1078/1311  0s] CopyFiles /Users/<USER>/Documents/GitHub/PerfumerProject/Library/Bee/Android/Prj/IL2CPP/Il2CppBackup/il2cppOutput/mscorlib__13.cpp
[1079/1311  0s] CopyFiles /Users/<USER>/Documents/GitHub/PerfumerProject/Library/Bee/Android/Prj/IL2CPP/Il2CppBackup/il2cppOutput/Generics__13.cpp
[1080/1311  0s] CopyFiles /Users/<USER>/Documents/GitHub/PerfumerProject/Library/Bee/Android/Prj/IL2CPP/Il2CppBackup/il2cppOutput/Generics__41.cpp
[1081/1311  0s] CopyFiles /Users/<USER>/Documents/GitHub/PerfumerProject/Library/Bee/Android/Prj/IL2CPP/Il2CppBackup/il2cppOutput/Generics__55.cpp
[1082/1311  0s] CopyFiles /Users/<USER>/Documents/GitHub/PerfumerProject/Library/Bee/Android/Prj/IL2CPP/Il2CppBackup/il2cppOutput/Generics__79.cpp
[1083/1311  0s] CopyFiles /Users/<USER>/Documents/GitHub/PerfumerProject/Library/Bee/Android/Prj/IL2CPP/Il2CppBackup/il2cppOutput/Generics__46.cpp
[1084/1311  0s] CopyFiles /Users/<USER>/Documents/GitHub/PerfumerProject/Library/Bee/Android/Prj/IL2CPP/Il2CppBackup/il2cppOutput/Generics__49.cpp
[1085/1311  0s] CopyFiles /Users/<USER>/Documents/GitHub/PerfumerProject/Library/Bee/Android/Prj/IL2CPP/Il2CppBackup/il2cppOutput/Generics__80.cpp
[1086/1311  0s] CopyFiles /Users/<USER>/Documents/GitHub/PerfumerProject/Library/Bee/Android/Prj/IL2CPP/Il2CppBackup/il2cppOutput/Generics__66.cpp
[1087/1311  0s] CopyFiles /Users/<USER>/Documents/GitHub/PerfumerProject/Library/Bee/Android/Prj/IL2CPP/Il2CppBackup/il2cppOutput/Generics__48.cpp
[1088/1311  0s] CopyFiles /Users/<USER>/Documents/GitHub/PerfumerProject/Library/Bee/Android/Prj/IL2CPP/Il2CppBackup/il2cppOutput/mscorlib__10.cpp
[1089/1311  0s] CopyFiles /Users/<USER>/Documents/GitHub/PerfumerProject/Library/Bee/Android/Prj/IL2CPP/Il2CppBackup/il2cppOutput/Generics__10.cpp
[1090/1311  0s] CopyFiles /Users/<USER>/Documents/GitHub/PerfumerProject/Library/Bee/Android/Prj/IL2CPP/Il2CppBackup/il2cppOutput/mscorlib__23.cpp
[1091/1311  0s] CopyFiles /Users/<USER>/Documents/GitHub/PerfumerProject/Library/Bee/Android/Prj/IL2CPP/Il2CppBackup/il2cppOutput/Generics__23.cpp
[1092/1311  0s] CopyFiles /Users/<USER>/Documents/GitHub/PerfumerProject/Library/Bee/Android/Prj/IL2CPP/Il2CppBackup/il2cppOutput/Generics__43.cpp
[1093/1311  0s] CopyFiles /Users/<USER>/Documents/GitHub/PerfumerProject/Library/Bee/Android/Prj/IL2CPP/Il2CppBackup/il2cppOutput/Generics__40.cpp
[1094/1311  0s] CopyFiles /Users/<USER>/Documents/GitHub/PerfumerProject/Library/Bee/Android/Prj/IL2CPP/Il2CppBackup/il2cppOutput/Generics__69.cpp
[1095/1311  0s] CopyFiles /Users/<USER>/Documents/GitHub/PerfumerProject/Library/Bee/Android/Prj/IL2CPP/Il2CppBackup/il2cppOutput/Generics__67.cpp
[1096/1311  0s] CopyFiles /Users/<USER>/Documents/GitHub/PerfumerProject/Library/Bee/Android/Prj/IL2CPP/Il2CppBackup/il2cppOutput/mscorlib__14.cpp
[1097/1311  0s] CopyFiles /Users/<USER>/Documents/GitHub/PerfumerProject/Library/Bee/Android/Prj/IL2CPP/Il2CppBackup/il2cppOutput/Generics__14.cpp
[1098/1311  0s] CopyFiles /Users/<USER>/Documents/GitHub/PerfumerProject/Library/Bee/Android/Prj/IL2CPP/Il2CppBackup/il2cppOutput/Generics__73.cpp
[1099/1311  0s] CopyFiles /Users/<USER>/Documents/GitHub/PerfumerProject/Library/Bee/Android/Prj/IL2CPP/Il2CppBackup/il2cppOutput/mscorlib__11.cpp
[1100/1311  0s] CopyFiles /Users/<USER>/Documents/GitHub/PerfumerProject/Library/Bee/Android/Prj/IL2CPP/Il2CppBackup/il2cppOutput/Generics__11.cpp
[1101/1311  0s] CopyFiles /Users/<USER>/Documents/GitHub/PerfumerProject/Library/Bee/Android/Prj/IL2CPP/Il2CppBackup/il2cppOutput/Generics__29.cpp
[1102/1311  0s] CopyFiles /Users/<USER>/Documents/GitHub/PerfumerProject/Library/Bee/Android/Prj/IL2CPP/Il2CppBackup/il2cppOutput/Generics__58.cpp
[1103/1311  0s] CopyFiles /Users/<USER>/Documents/GitHub/PerfumerProject/Library/Bee/Android/Prj/IL2CPP/Il2CppBackup/il2cppOutput/Generics__57.cpp
[1104/1311  0s] CopyFiles /Users/<USER>/Documents/GitHub/PerfumerProject/Library/Bee/Android/Prj/IL2CPP/Il2CppBackup/il2cppOutput/mscorlib__20.cpp
[1105/1311  0s] CopyFiles /Users/<USER>/Documents/GitHub/PerfumerProject/Library/Bee/Android/Prj/IL2CPP/Il2CppBackup/il2cppOutput/Generics__20.cpp
[1106/1311  0s] CopyFiles /Users/<USER>/Documents/GitHub/PerfumerProject/Library/Bee/Android/Prj/IL2CPP/Il2CppBackup/il2cppOutput/Generics__72.cpp
[1107/1311  0s] CopyFiles /Users/<USER>/Documents/GitHub/PerfumerProject/Library/Bee/Android/Prj/IL2CPP/Il2CppBackup/il2cppOutput/mscorlib__17.cpp
[1108/1311  0s] CopyFiles /Users/<USER>/Documents/GitHub/PerfumerProject/Library/Bee/Android/Prj/IL2CPP/Il2CppBackup/il2cppOutput/Generics__17.cpp
[1109/1311  0s] CopyFiles /Users/<USER>/Documents/GitHub/PerfumerProject/Library/Bee/Android/Prj/IL2CPP/Il2CppBackup/il2cppOutput/Generics__33.cpp
[1110/1311  0s] CopyFiles /Users/<USER>/Documents/GitHub/PerfumerProject/Library/Bee/Android/Prj/IL2CPP/Il2CppBackup/il2cppOutput/Generics__45.cpp
[1111/1311  0s] CopyFiles /Users/<USER>/Documents/GitHub/PerfumerProject/Library/Bee/Android/Prj/IL2CPP/Il2CppBackup/il2cppOutput/Generics__74.cpp
[1112/1311  0s] CopyFiles /Users/<USER>/Documents/GitHub/PerfumerProject/Library/Bee/Android/Prj/IL2CPP/Il2CppBackup/il2cppOutput/Generics__60.cpp
[1113/1311  0s] CopyFiles /Users/<USER>/Documents/GitHub/PerfumerProject/Library/Bee/Android/Prj/IL2CPP/Il2CppBackup/il2cppOutput/Generics__37.cpp
[1114/1311  0s] CopyFiles /Users/<USER>/Documents/GitHub/PerfumerProject/Library/Bee/Android/Prj/IL2CPP/Il2CppBackup/il2cppOutput/Generics__61.cpp
[1115/1311  0s] CopyFiles /Users/<USER>/Documents/GitHub/PerfumerProject/Library/Bee/Android/Prj/IL2CPP/Il2CppBackup/il2cppOutput/mscorlib__15.cpp
[1116/1311  0s] CopyFiles /Users/<USER>/Documents/GitHub/PerfumerProject/Library/Bee/Android/Prj/IL2CPP/Il2CppBackup/il2cppOutput/Generics__15.cpp
[1117/1311  0s] CopyFiles /Users/<USER>/Documents/GitHub/PerfumerProject/Library/Bee/Android/Prj/IL2CPP/Il2CppBackup/il2cppOutput/mscorlib__16.cpp
[1118/1311  0s] CopyFiles /Users/<USER>/Documents/GitHub/PerfumerProject/Library/Bee/Android/Prj/IL2CPP/Il2CppBackup/il2cppOutput/Generics__16.cpp
[1119/1311  0s] CopyFiles /Users/<USER>/Documents/GitHub/PerfumerProject/Library/Bee/Android/Prj/IL2CPP/Il2CppBackup/il2cppOutput/System.Xml__2.cpp
[1120/1311  0s] CopyFiles /Users/<USER>/Documents/GitHub/PerfumerProject/Library/Bee/Android/Prj/IL2CPP/Il2CppBackup/il2cppOutput/System.Xml__4.cpp
[1121/1311  0s] CopyFiles /Users/<USER>/Documents/GitHub/PerfumerProject/Library/Bee/Android/Prj/IL2CPP/Il2CppBackup/il2cppOutput/DOTween_CodeGen.c
[1122/1311  0s] CopyFiles /Users/<USER>/Documents/GitHub/PerfumerProject/Library/Bee/Android/Prj/IL2CPP/Il2CppBackup/il2cppOutput/System.Xml__9.cpp
[1123/1311  0s] CopyFiles /Users/<USER>/Documents/GitHub/PerfumerProject/Library/Bee/Android/Prj/IL2CPP/Il2CppBackup/il2cppOutput/System.Xml__6.cpp
[1124/1311  0s] CopyFiles /Users/<USER>/Documents/GitHub/PerfumerProject/Library/Bee/Android/Prj/IL2CPP/Il2CppBackup/il2cppOutput/System.Xml__3.cpp
[1125/1311  0s] CopyFiles /Users/<USER>/Documents/GitHub/PerfumerProject/Library/Bee/Android/Prj/IL2CPP/Il2CppBackup/il2cppOutput/System.Xml__7.cpp
[1126/1311  0s] CopyFiles /Users/<USER>/Documents/GitHub/PerfumerProject/Library/Bee/Android/Prj/IL2CPP/Il2CppBackup/il2cppOutput/Mono.Security.cpp
[1127/1311  0s] CopyFiles /Users/<USER>/Documents/GitHub/PerfumerProject/Library/Bee/Android/Prj/IL2CPP/Il2CppBackup/il2cppOutput/System.Xml__1.cpp
[1128/1311  0s] CopyFiles /Users/<USER>/Documents/GitHub/PerfumerProject/Library/Bee/Android/Prj/IL2CPP/Il2CppBackup/il2cppOutput/System.Xml__5.cpp
[1129/1311  0s] CopyFiles /Users/<USER>/Documents/GitHub/PerfumerProject/Library/Bee/Android/Prj/IL2CPP/Il2CppBackup/il2cppOutput/System.Xml__8.cpp
[1130/1311  0s] CopyFiles /Users/<USER>/Documents/GitHub/PerfumerProject/Library/Bee/Android/Prj/IL2CPP/Il2CppBackup/il2cppOutput/Fungus.cpp
[1131/1311  0s] CopyFiles /Users/<USER>/Documents/GitHub/PerfumerProject/Library/Bee/Android/Prj/IL2CPP/Il2CppBackup/il2cppOutput/System.cpp
[1132/1311  0s] CopyFiles /Users/<USER>/Documents/GitHub/PerfumerProject/Library/Bee/Android/Prj/IL2CPP/Il2CppBackup/il2cppOutput/System.Xml__10.cpp
[1133/1311  0s] CopyFiles /Users/<USER>/Documents/GitHub/PerfumerProject/Library/Bee/Android/Prj/IL2CPP/Il2CppBackup/il2cppOutput/GenericMethods.cpp
[1134/1311  0s] CopyFiles /Users/<USER>/Documents/GitHub/PerfumerProject/Library/Bee/Android/Prj/IL2CPP/Il2CppBackup/il2cppOutput/System.Core__2.cpp
[1135/1311  0s] CopyFiles /Users/<USER>/Documents/GitHub/PerfumerProject/Library/Bee/Android/Prj/IL2CPP/Il2CppBackup/il2cppOutput/UnityEngine.UI.cpp
[1136/1311  0s] CopyFiles /Users/<USER>/Documents/GitHub/PerfumerProject/Library/Bee/Android/Prj/IL2CPP/Il2CppBackup/il2cppOutput/System.Core__3.cpp
[1137/1311  0s] CopyFiles /Users/<USER>/Documents/GitHub/PerfumerProject/Library/Bee/Android/Prj/IL2CPP/Il2CppBackup/il2cppOutput/Il2CppRgctxTable.c
[1138/1311  0s] CopyFiles /Users/<USER>/Documents/GitHub/PerfumerProject/Library/Bee/Android/Prj/IL2CPP/Il2CppBackup/il2cppOutput/mscorlib_CodeGen.c
[1139/1311  0s] CopyFiles /Users/<USER>/Documents/GitHub/PerfumerProject/Library/Bee/Android/Prj/IL2CPP/Il2CppBackup/il2cppOutput/System.Core__1.cpp
[1140/1311  0s] CopyFiles /Users/<USER>/Documents/GitHub/PerfumerProject/Library/Bee/Android/Prj/IL2CPP/Il2CppBackup/il2cppOutput/Unity.InputSystem__1.cpp
[1141/1311  0s] CopyFiles /Users/<USER>/Documents/GitHub/PerfumerProject/Library/Bee/Android/Prj/IL2CPP/Il2CppBackup/il2cppOutput/Unity.InputSystem__5.cpp
[1142/1311  0s] CopyFiles /Users/<USER>/Documents/GitHub/PerfumerProject/Library/Bee/Android/Prj/IL2CPP/Il2CppBackup/il2cppOutput/Unity.TextMeshPro__3.cpp
[1143/1311  0s] CopyFiles /Users/<USER>/Documents/GitHub/PerfumerProject/Library/Bee/Android/Prj/IL2CPP/Il2CppBackup/il2cppOutput/Unity.InputSystem__8.cpp
[1144/1311  0s] CopyFiles /Users/<USER>/Documents/GitHub/PerfumerProject/Library/Bee/Android/Prj/IL2CPP/Il2CppBackup/il2cppOutput/UnityEngine.UI_CodeGen.c
[1145/1311  0s] CopyFiles /Users/<USER>/Documents/GitHub/PerfumerProject/Library/Bee/Android/Prj/IL2CPP/Il2CppBackup/il2cppOutput/Unity.TextMeshPro__4.cpp
[1146/1311  0s] CopyFiles /Users/<USER>/Documents/GitHub/PerfumerProject/Library/Bee/Android/Prj/IL2CPP/Il2CppBackup/il2cppOutput/Unity.TextMeshPro__1.cpp
[1147/1311  0s] CopyFiles /Users/<USER>/Documents/GitHub/PerfumerProject/Library/Bee/Android/Prj/IL2CPP/Il2CppBackup/il2cppOutput/UnityEngine.VRModule.cpp
[1148/1311  0s] CopyFiles /Users/<USER>/Documents/GitHub/PerfumerProject/Library/Bee/Android/Prj/IL2CPP/Il2CppBackup/il2cppOutput/Unity.InputSystem__2.cpp
[1149/1311  0s] CopyFiles /Users/<USER>/Documents/GitHub/PerfumerProject/Library/Bee/Android/Prj/IL2CPP/Il2CppBackup/il2cppOutput/Unity.TextMeshPro__5.cpp
[1150/1311  0s] CopyFiles /Users/<USER>/Documents/GitHub/PerfumerProject/Library/Bee/Android/Prj/IL2CPP/Il2CppBackup/il2cppOutput/UnityEngine.AIModule.cpp
[1151/1311  0s] CopyFiles /Users/<USER>/Documents/GitHub/PerfumerProject/Library/Bee/Android/Prj/IL2CPP/Il2CppBackup/il2cppOutput/System.Configuration.cpp
[1152/1311  0s] CopyFiles /Users/<USER>/Documents/GitHub/PerfumerProject/Library/Bee/Android/Prj/IL2CPP/Il2CppBackup/il2cppOutput/UnityEngine.UIModule.cpp
[1153/1311  0s] CopyFiles /Users/<USER>/Documents/GitHub/PerfumerProject/Library/Bee/Android/Prj/IL2CPP/Il2CppBackup/il2cppOutput/Unity.TextMeshPro__6.cpp
[1154/1311  0s] CopyFiles /Users/<USER>/Documents/GitHub/PerfumerProject/Library/Bee/Android/Prj/IL2CPP/Il2CppBackup/il2cppOutput/Unity.InputSystem__3.cpp
[1155/1311  0s] CopyFiles /Users/<USER>/Documents/GitHub/PerfumerProject/Library/Bee/Android/Prj/IL2CPP/Il2CppBackup/il2cppOutput/Unity.InputSystem__4.cpp
[1156/1311  0s] CopyFiles /Users/<USER>/Documents/GitHub/PerfumerProject/Library/Bee/Android/Prj/IL2CPP/Il2CppBackup/il2cppOutput/Unity.InputSystem__7.cpp
[1157/1311  0s] CopyFiles /Users/<USER>/Documents/GitHub/PerfumerProject/Library/Bee/Android/Prj/IL2CPP/Il2CppBackup/il2cppOutput/Unity.InputSystem__6.cpp
[1158/1311  0s] CopyFiles /Users/<USER>/Documents/GitHub/PerfumerProject/Library/Bee/Android/Prj/IL2CPP/Il2CppBackup/il2cppOutput/UnityEngine.XRModule.cpp
[1159/1311  0s] CopyFiles /Users/<USER>/Documents/GitHub/PerfumerProject/Library/Bee/Android/Prj/IL2CPP/Il2CppBackup/il2cppOutput/Unity.TextMeshPro__2.cpp
[1160/1311  0s] CopyFiles /Users/<USER>/Documents/GitHub/PerfumerProject/Library/Bee/Android/Prj/IL2CPP/Il2CppBackup/il2cppOutput/Unity.InputSystem__9.cpp
[1161/1311  0s] CopyFiles /Users/<USER>/Documents/GitHub/PerfumerProject/Library/Bee/Android/Prj/IL2CPP/Il2CppBackup/il2cppOutput/UnityEngine_CodeGen.c
[1162/1311  0s] CopyFiles /Users/<USER>/Documents/GitHub/PerfumerProject/Library/Bee/Android/Prj/IL2CPP/Il2CppBackup/il2cppOutput/System.Core_CodeGen.c
[1163/1311  0s] CopyFiles /Users/<USER>/Documents/GitHub/PerfumerProject/Library/Bee/Android/Prj/IL2CPP/Il2CppBackup/il2cppOutput/__Generated_CodeGen.c
[1164/1311  0s] CopyFiles /Users/<USER>/Documents/GitHub/PerfumerProject/Library/Bee/Android/Prj/IL2CPP/Il2CppBackup/il2cppOutput/UnityEngine.UI__2.cpp
[1165/1311  0s] CopyFiles /Users/<USER>/Documents/GitHub/PerfumerProject/Library/Bee/Android/Prj/IL2CPP/Il2CppBackup/il2cppOutput/GenericMethods__1.cpp
[1166/1311  0s] CopyFiles /Users/<USER>/Documents/GitHub/PerfumerProject/Library/Bee/Android/Prj/IL2CPP/Il2CppBackup/il2cppOutput/UnityEngine.UI__3.cpp
[1167/1311  0s] CopyFiles /Users/<USER>/Documents/GitHub/PerfumerProject/Library/Bee/Android/Prj/IL2CPP/Il2CppBackup/il2cppOutput/GenericMethods__2.cpp
[1168/1311  0s] CopyFiles /Users/<USER>/Documents/GitHub/PerfumerProject/Library/Bee/Android/Prj/IL2CPP/Il2CppBackup/il2cppOutput/Unity.TextMeshPro.cpp
[1169/1311  0s] CopyFiles /Users/<USER>/Documents/GitHub/PerfumerProject/Library/Bee/Android/Prj/IL2CPP/Il2CppBackup/il2cppOutput/GenericMethods__3.cpp
[1170/1311  0s] CopyFiles /Users/<USER>/Documents/GitHub/PerfumerProject/Library/Bee/Android/Prj/IL2CPP/Il2CppBackup/il2cppOutput/Il2CppMetadataUsage.c
[1171/1311  0s] CopyFiles /Users/<USER>/Documents/GitHub/PerfumerProject/Library/Bee/Android/Prj/IL2CPP/Il2CppBackup/il2cppOutput/GenericMethods__4.cpp
[1172/1311  0s] CopyFiles /Users/<USER>/Documents/GitHub/PerfumerProject/Library/Bee/Android/Prj/IL2CPP/Il2CppBackup/il2cppOutput/NavMeshPlus_CodeGen.c
[1173/1311  0s] CopyFiles /Users/<USER>/Documents/GitHub/PerfumerProject/Library/Bee/Android/Prj/IL2CPP/Il2CppBackup/il2cppOutput/GenericMethods__8.cpp
[1174/1311  0s] CopyFiles /Users/<USER>/Documents/GitHub/PerfumerProject/Library/Bee/Android/Prj/IL2CPP/Il2CppBackup/il2cppOutput/GenericMethods__7.cpp
[1175/1311  0s] CopyFiles /Users/<USER>/Documents/GitHub/PerfumerProject/Library/Bee/Android/Prj/IL2CPP/Il2CppBackup/il2cppOutput/Unity.InputSystem.cpp
[1176/1311  0s] CopyFiles /Users/<USER>/Documents/GitHub/PerfumerProject/Library/Bee/Android/Prj/IL2CPP/Il2CppBackup/il2cppOutput/GenericMethods__9.cpp
[1177/1311  0s] CopyFiles /Users/<USER>/Documents/GitHub/PerfumerProject/Library/Bee/Android/Prj/IL2CPP/Il2CppBackup/il2cppOutput/GenericMethods__5.cpp
[1178/1311  0s] CopyFiles /Users/<USER>/Documents/GitHub/PerfumerProject/Library/Bee/Android/Prj/IL2CPP/Il2CppBackup/il2cppOutput/UnityEngine.UI__1.cpp
[1179/1311  0s] CopyFiles /Users/<USER>/Documents/GitHub/PerfumerProject/Library/Bee/Android/Prj/IL2CPP/Il2CppBackup/il2cppOutput/GenericMethods__6.cpp
[1180/1311  0s] CopyFiles /Users/<USER>/Documents/GitHub/PerfumerProject/Library/Bee/Android/Prj/IL2CPP/Il2CppBackup/il2cppOutput/DOTween.cpp
[1181/1311  0s] CopyFiles /Users/<USER>/Documents/GitHub/PerfumerProject/Library/Bee/Android/Prj/IL2CPP/Il2CppBackup/il2cppOutput/mscorlib__1.cpp
[1182/1311  0s] CopyFiles /Users/<USER>/Documents/GitHub/PerfumerProject/Library/Bee/Android/Prj/IL2CPP/Il2CppBackup/il2cppOutput/Generics__1.cpp
[1183/1311  0s] CopyFiles /Users/<USER>/Documents/GitHub/PerfumerProject/Library/Bee/Android/Prj/IL2CPP/Il2CppBackup/il2cppOutput/mscorlib__4.cpp
[1184/1311  0s] CopyFiles /Users/<USER>/Documents/GitHub/PerfumerProject/Library/Bee/Android/Prj/IL2CPP/Il2CppBackup/il2cppOutput/Generics__4.cpp
[1185/1311  0s] CopyFiles /Users/<USER>/Documents/GitHub/PerfumerProject/Library/Bee/Android/Prj/IL2CPP/Il2CppBackup/il2cppOutput/mscorlib__6.cpp
[1186/1311  0s] CopyFiles /Users/<USER>/Documents/GitHub/PerfumerProject/Library/Bee/Android/Prj/IL2CPP/Il2CppBackup/il2cppOutput/Generics__6.cpp
[1187/1311  0s] CopyFiles /Users/<USER>/Documents/GitHub/PerfumerProject/Library/Bee/Android/Prj/IL2CPP/Il2CppBackup/il2cppOutput/mscorlib__8.cpp
[1188/1311  0s] CopyFiles /Users/<USER>/Documents/GitHub/PerfumerProject/Library/Bee/Android/Prj/IL2CPP/Il2CppBackup/il2cppOutput/Generics__8.cpp
[1189/1311  0s] CopyFiles /Users/<USER>/Documents/GitHub/PerfumerProject/Library/Bee/Android/Prj/IL2CPP/Il2CppBackup/il2cppOutput/mscorlib__3.cpp
[1190/1311  0s] CopyFiles /Users/<USER>/Documents/GitHub/PerfumerProject/Library/Bee/Android/Prj/IL2CPP/Il2CppBackup/il2cppOutput/Generics__3.cpp
[1191/1311  0s] CopyFiles /Users/<USER>/Documents/GitHub/PerfumerProject/Library/Bee/Android/Prj/IL2CPP/Il2CppBackup/il2cppOutput/UnityEngine.cpp
[1192/1311  0s] CopyFiles /Users/<USER>/Documents/GitHub/PerfumerProject/Library/Bee/Android/Prj/IL2CPP/Il2CppBackup/il2cppOutput/System.Core.cpp
[1193/1311  0s] CopyFiles /Users/<USER>/Documents/GitHub/PerfumerProject/Library/Bee/Android/Prj/IL2CPP/Il2CppBackup/il2cppOutput/mscorlib__9.cpp
[1194/1311  0s] CopyFiles /Users/<USER>/Documents/GitHub/PerfumerProject/Library/Bee/Android/Prj/IL2CPP/Il2CppBackup/il2cppOutput/Generics__9.cpp
[1195/1311  0s] CopyFiles /Users/<USER>/Documents/GitHub/PerfumerProject/Library/Bee/Android/Prj/IL2CPP/Il2CppBackup/il2cppOutput/mscorlib__5.cpp
[1196/1311  0s] CopyFiles /Users/<USER>/Documents/GitHub/PerfumerProject/Library/Bee/Android/Prj/IL2CPP/Il2CppBackup/il2cppOutput/Generics__5.cpp
[1197/1311  0s] CopyFiles /Users/<USER>/Documents/GitHub/PerfumerProject/Library/Bee/Android/Prj/IL2CPP/Il2CppBackup/il2cppOutput/mscorlib__7.cpp
[1198/1311  0s] CopyFiles /Users/<USER>/Documents/GitHub/PerfumerProject/Library/Bee/Android/Prj/IL2CPP/Il2CppBackup/il2cppOutput/Generics__7.cpp
[1199/1311  0s] CopyFiles /Users/<USER>/Documents/GitHub/PerfumerProject/Library/Bee/Android/Prj/IL2CPP/Il2CppBackup/il2cppOutput/NavMeshPlus.cpp
[1200/1311  0s] CopyFiles /Users/<USER>/Documents/GitHub/PerfumerProject/Library/Bee/Android/Prj/IL2CPP/Il2CppBackup/il2cppOutput/mscorlib__2.cpp
[1201/1311  0s] CopyFiles /Users/<USER>/Documents/GitHub/PerfumerProject/Library/Bee/Android/Prj/IL2CPP/Il2CppBackup/il2cppOutput/Generics__2.cpp
[1202/1311  0s] CopyFiles /Users/<USER>/Documents/GitHub/PerfumerProject/Library/Bee/Android/Prj/IL2CPP/Il2CppBackup/il2cppOutput/__Generated.cpp
[1203/1311  0s] CopyFiles /Users/<USER>/Documents/GitHub/PerfumerProject/Library/Bee/Android/Prj/IL2CPP/Il2CppBackup/il2cppOutput/Fungus__8.cpp
[1204/1311  0s] CopyFiles /Users/<USER>/Documents/GitHub/PerfumerProject/Library/Bee/Android/Prj/IL2CPP/Il2CppBackup/il2cppOutput/Fungus__7.cpp
[1205/1311  0s] CopyFiles /Users/<USER>/Documents/GitHub/PerfumerProject/Library/Bee/Android/Prj/IL2CPP/Il2CppBackup/il2cppOutput/Fungus__1.cpp
[1206/1311  0s] CopyFiles /Users/<USER>/Documents/GitHub/PerfumerProject/Library/Bee/Android/Prj/IL2CPP/Il2CppBackup/il2cppOutput/System__8.cpp
[1207/1311  0s] CopyFiles /Users/<USER>/Documents/GitHub/PerfumerProject/Library/Bee/Android/Prj/IL2CPP/Il2CppBackup/il2cppOutput/Fungus__9.cpp
[1208/1311  0s] CopyFiles /Users/<USER>/Documents/GitHub/PerfumerProject/Library/Bee/Android/Prj/IL2CPP/Il2CppBackup/il2cppOutput/System__3.cpp
[1209/1311  0s] CopyFiles /Users/<USER>/Documents/GitHub/PerfumerProject/Library/Bee/Android/Prj/IL2CPP/Il2CppBackup/il2cppOutput/System__4.cpp
[1210/1311  0s] CopyFiles /Users/<USER>/Documents/GitHub/PerfumerProject/Library/Bee/Android/Prj/IL2CPP/Il2CppBackup/il2cppOutput/System__9.cpp
[1211/1311  0s] CopyFiles /Users/<USER>/Documents/GitHub/PerfumerProject/Library/Bee/Android/Prj/IL2CPP/Il2CppBackup/il2cppOutput/System__6.cpp
[1212/1311  0s] CopyFiles /Users/<USER>/Documents/GitHub/PerfumerProject/Library/Bee/Android/Prj/IL2CPP/Il2CppBackup/il2cppOutput/Fungus__3.cpp
[1213/1311  0s] CopyFiles /Users/<USER>/Documents/GitHub/PerfumerProject/Library/Bee/Android/Prj/IL2CPP/Il2CppBackup/il2cppOutput/Fungus__6.cpp
[1214/1311  0s] CopyFiles /Users/<USER>/Documents/GitHub/PerfumerProject/Library/Bee/Android/Prj/IL2CPP/Il2CppBackup/il2cppOutput/System__2.cpp
[1215/1311  0s] CopyFiles /Users/<USER>/Documents/GitHub/PerfumerProject/Library/Bee/Android/Prj/IL2CPP/Il2CppBackup/il2cppOutput/Fungus__5.cpp
[1216/1311  0s] CopyFiles /Users/<USER>/Documents/GitHub/PerfumerProject/Library/Bee/Android/Prj/IL2CPP/Il2CppBackup/il2cppOutput/Fungus__2.cpp
[1217/1311  0s] CopyFiles /Users/<USER>/Documents/GitHub/PerfumerProject/Library/Bee/Android/Prj/IL2CPP/Il2CppBackup/il2cppOutput/System__5.cpp
[1218/1311  0s] CopyFiles /Users/<USER>/Documents/GitHub/PerfumerProject/Library/Bee/Android/Prj/IL2CPP/Il2CppBackup/il2cppOutput/Fungus__4.cpp
[1219/1311  0s] CopyFiles /Users/<USER>/Documents/GitHub/PerfumerProject/Library/Bee/Android/Prj/IL2CPP/Il2CppBackup/il2cppOutput/System__7.cpp
[1220/1311  0s] CopyFiles /Users/<USER>/Documents/GitHub/PerfumerProject/Library/Bee/Android/Prj/IL2CPP/Il2CppBackup/il2cppOutput/System__1.cpp
[1221/1311  0s] CopyFiles /Users/<USER>/Documents/GitHub/PerfumerProject/Library/Bee/Android/Prj/IL2CPP/Il2CppBackup/il2cppOutput/analytics.json
[1222/1311  0s] CopyFiles /Users/<USER>/Documents/GitHub/PerfumerProject/Library/Bee/Android/Prj/IL2CPP/Il2CppBackup/il2cppOutput/System.Xml.cpp
[1223/1311  0s] CopyFiles /Users/<USER>/Documents/GitHub/PerfumerProject/Library/Bee/Android/Prj/IL2CPP/Il2CppBackup/il2cppOutput/Fungus__10.cpp
[1224/1311  0s] CopyFiles /Users/<USER>/Documents/GitHub/PerfumerProject/Library/Bee/Android/Prj/IL2CPP/Il2CppBackup/il2cppOutput/Fungus__14.cpp
[1225/1311  0s] CopyFiles /Users/<USER>/Documents/GitHub/PerfumerProject/Library/Bee/Android/Prj/IL2CPP/Il2CppBackup/il2cppOutput/DOTweenPro.cpp
[1226/1311  0s] CopyFiles /Users/<USER>/Documents/GitHub/PerfumerProject/Library/Bee/Android/Prj/IL2CPP/Il2CppBackup/il2cppOutput/Fungus__12.cpp
[1227/1311  0s] CopyFiles /Users/<USER>/Documents/GitHub/PerfumerProject/Library/Bee/Android/Prj/IL2CPP/Il2CppBackup/il2cppOutput/DOTween__2.cpp
[1228/1311  0s] CopyFiles /Users/<USER>/Documents/GitHub/PerfumerProject/Library/Bee/Android/Prj/IL2CPP/Il2CppBackup/il2cppOutput/Fungus__11.cpp
[1229/1311  0s] CopyFiles /Users/<USER>/Documents/GitHub/PerfumerProject/Library/Bee/Android/Prj/IL2CPP/Il2CppBackup/il2cppOutput/DOTween__1.cpp
[1230/1311  0s] CopyFiles /Users/<USER>/Documents/GitHub/PerfumerProject/Library/Bee/Android/Prj/IL2CPP/Il2CppBackup/il2cppOutput/Fungus__13.cpp
[1231/1311  0s] CopyFiles /Users/<USER>/Documents/GitHub/PerfumerProject/Library/Bee/Android/Prj/IL2CPP/Il2CppBackup/il2cppOutput/System.Numerics_CodeGen.c
[1232/1311  0s] CopyFiles /Users/<USER>/Documents/GitHub/PerfumerProject/Library/Bee/Android/Prj/IL2CPP/Il2CppBackup/il2cppOutput/Assembly-CSharp_CodeGen.c
[1233/1311  0s] CopyFiles /Users/<USER>/Documents/GitHub/PerfumerProject/Library/Bee/Android/Prj/IL2CPP/Il2CppBackup/il2cppOutput/Il2CppGenericClassTable.c
[1234/1311  0s] CopyFiles /Users/<USER>/Documents/GitHub/PerfumerProject/Library/Bee/Android/Prj/IL2CPP/Il2CppBackup/il2cppOutput/Unity.InputSystem__11.cpp
[1235/1311  0s] CopyFiles /Users/<USER>/Documents/GitHub/PerfumerProject/Library/Bee/Android/Prj/IL2CPP/Il2CppBackup/il2cppOutput/Unity.InputSystem__10.cpp
[1236/1311  0s] CopyFiles /Users/<USER>/Documents/GitHub/PerfumerProject/Library/Bee/Android/Prj/IL2CPP/Il2CppBackup/il2cppOutput/System.Numerics.cpp
[1237/1311  0s] CopyFiles /Users/<USER>/Documents/GitHub/PerfumerProject/Library/Bee/Android/Prj/IL2CPP/Il2CppBackup/il2cppOutput/Assembly-CSharp.cpp
[1238/1311  0s] CopyFiles /Users/<USER>/Documents/GitHub/PerfumerProject/Library/Bee/Android/Prj/IL2CPP/Il2CppBackup/il2cppOutput/GenericMethods__14.cpp
[1239/1311  0s] CopyFiles /Users/<USER>/Documents/GitHub/PerfumerProject/Library/Bee/Android/Prj/IL2CPP/Il2CppBackup/il2cppOutput/GenericMethods__10.cpp
[1240/1311  0s] CopyFiles /Users/<USER>/Documents/GitHub/PerfumerProject/Library/Bee/Android/Prj/IL2CPP/Il2CppBackup/il2cppOutput/GenericMethods__18.cpp
[1241/1311  0s] CopyFiles /Users/<USER>/Documents/GitHub/PerfumerProject/Library/Bee/Android/Prj/IL2CPP/Il2CppBackup/il2cppOutput/GenericMethods__21.cpp
[1242/1311  0s] CopyFiles /Users/<USER>/Documents/GitHub/PerfumerProject/Library/Bee/Android/Prj/IL2CPP/Il2CppBackup/il2cppOutput/GenericMethods__11.cpp
[1243/1311  0s] CopyFiles /Users/<USER>/Documents/GitHub/PerfumerProject/Library/Bee/Android/Prj/IL2CPP/Il2CppBackup/il2cppOutput/GenericMethods__17.cpp
[1244/1311  0s] CopyFiles /Users/<USER>/Documents/GitHub/PerfumerProject/Library/Bee/Android/Prj/IL2CPP/Il2CppBackup/il2cppOutput/GenericMethods__15.cpp
[1245/1311  0s] CopyFiles /Users/<USER>/Documents/GitHub/PerfumerProject/Library/Bee/Android/Prj/IL2CPP/Il2CppBackup/il2cppOutput/GenericMethods__19.cpp
[1246/1311  0s] CopyFiles /Users/<USER>/Documents/GitHub/PerfumerProject/Library/Bee/Android/Prj/IL2CPP/Il2CppBackup/il2cppOutput/GenericMethods__20.cpp
[1247/1311  0s] CopyFiles /Users/<USER>/Documents/GitHub/PerfumerProject/Library/Bee/Android/Prj/IL2CPP/Il2CppBackup/il2cppOutput/Assembly-CSharp__1.cpp
[1248/1311  0s] CopyFiles /Users/<USER>/Documents/GitHub/PerfumerProject/Library/Bee/Android/Prj/IL2CPP/Il2CppBackup/il2cppOutput/Il2CppInvokerTable.cpp
[1249/1311  0s] CopyFiles /Users/<USER>/Documents/GitHub/PerfumerProject/Library/Bee/Android/Prj/IL2CPP/Il2CppBackup/il2cppOutput/GenericMethods__13.cpp
[1250/1311  0s] CopyFiles /Users/<USER>/Documents/GitHub/PerfumerProject/Library/Bee/Android/Prj/IL2CPP/Il2CppBackup/il2cppOutput/GenericMethods__12.cpp
[1251/1311  0s] CopyFiles /Users/<USER>/Documents/GitHub/PerfumerProject/Library/Bee/Android/Prj/IL2CPP/Il2CppBackup/il2cppOutput/GenericMethods__16.cpp
[1252/1311  0s] CopyFiles /Users/<USER>/Documents/GitHub/PerfumerProject/Library/Bee/Android/Prj/IL2CPP/Il2CppBackup/il2cppOutput/Assembly-CSharp__2.cpp
[1253/1311  0s] CopyFiles /Users/<USER>/Documents/GitHub/PerfumerProject/Library/Bee/Android/Prj/IL2CPP/Il2CppBackup/il2cppOutput/Mono.Security__1.cpp
[1254/1311  0s] CopyFiles /Users/<USER>/Documents/GitHub/PerfumerProject/Library/Bee/Android/Prj/IL2CPP/Il2CppBackup/il2cppOutput/System.Xml_CodeGen.c
[1255/1311  0s] CopyFiles /Users/<USER>/Documents/GitHub/PerfumerProject/Library/Bee/Android/Prj/IL2CPP/Il2CppBackup/il2cppOutput/DOTweenPro_CodeGen.c
[1256/1311  0s] CopyFiles /Users/<USER>/Documents/GitHub/PerfumerProject/Library/Bee/Android/Prj/IL2CPP/Il2CppBackup/il2cppOutput/mscorlib.cpp
[1257/1311  0s] CopyFiles /Users/<USER>/Documents/GitHub/PerfumerProject/Library/Bee/Android/Prj/IL2CPP/Il2CppBackup/il2cppOutput/Generics.cpp
[1258/1311  0s] CopyFiles /Users/<USER>/Documents/GitHub/PerfumerProject/Library/Bee/Android/Prj/IL2CPP/Il2CppBackup/il2cppOutput/UnityEngine.SharedInternalsModule_CodeGen.c
[1259/1311  0s] CopyFiles /Users/<USER>/Documents/GitHub/PerfumerProject/Library/Bee/Android/Prj/IL2CPP/Il2CppBackup/il2cppOutput/UnityEngine.UnityWebRequestModule_CodeGen.c
[1260/1311  0s] CopyFiles /Users/<USER>/Documents/GitHub/PerfumerProject/Library/Bee/Android/Prj/IL2CPP/Il2CppBackup/il2cppOutput/Il2CppMetadataRegistration.c
[1261/1311  0s] CopyFiles /Users/<USER>/Documents/GitHub/PerfumerProject/Library/Bee/Android/Prj/IL2CPP/Gradle/unityLibrary/src/main/assets/bin/Data/Managed/Resources/mscorlib.dll-resources.dat
[1262/1311  0s] CopyFiles /Users/<USER>/Documents/GitHub/PerfumerProject/Library/Bee/Android/Prj/IL2CPP/Il2CppBackup/il2cppOutput/UnityEngine.ParticleSystemModule_CodeGen.c
[1263/1311  0s] CopyFiles /Users/<USER>/Documents/GitHub/PerfumerProject/Library/Bee/Android/Prj/IL2CPP/Il2CppBackup/il2cppOutput/UnityEngine.TerrainModule.cpp
[1264/1311  0s] CopyFiles /Users/<USER>/Documents/GitHub/PerfumerProject/Library/Bee/Android/Prj/IL2CPP/Il2CppBackup/il2cppOutput/UnityEngine.CoreModule__3.cpp
[1265/1311  0s] CopyFiles /Users/<USER>/Documents/GitHub/PerfumerProject/Library/Bee/Android/Prj/IL2CPP/Il2CppBackup/il2cppOutput/UnityEngine.CoreModule__1.cpp
[1266/1311  0s] CopyFiles /Users/<USER>/Documents/GitHub/PerfumerProject/Library/Bee/Android/Prj/IL2CPP/Il2CppBackup/il2cppOutput/Assembly-CSharp-firstpass.cpp
[1267/1311  0s] CopyFiles /Users/<USER>/Documents/GitHub/PerfumerProject/Library/Bee/Android/Prj/IL2CPP/Il2CppBackup/il2cppOutput/UnityEngine.PhysicsModule.cpp
[1268/1311  0s] CopyFiles /Users/<USER>/Documents/GitHub/PerfumerProject/Library/Bee/Android/Prj/IL2CPP/Il2CppBackup/il2cppOutput/UnityEngine.CoreModule__2.cpp
[1269/1311  0s] CopyFiles /Users/<USER>/Documents/GitHub/PerfumerProject/Library/Bee/Android/Prj/IL2CPP/Il2CppBackup/il2cppOutput/UnityEngine.TilemapModule.cpp
[1270/1311  0s] CopyFiles /Users/<USER>/Documents/GitHub/PerfumerProject/Library/Bee/Android/Prj/IL2CPP/Il2CppBackup/il2cppOutput/UnityEngine.UIElementsModule__4.cpp
[1271/1311  0s] CopyFiles /Users/<USER>/Documents/GitHub/PerfumerProject/Library/Bee/Android/Prj/IL2CPP/Il2CppBackup/il2cppOutput/UnityEngine.UIElementsModule__6.cpp
[1272/1311  0s] CopyFiles /Users/<USER>/Documents/GitHub/PerfumerProject/Library/Bee/Android/Prj/IL2CPP/Il2CppBackup/il2cppOutput/UnityEngine.UIElementsModule__9.cpp
[1273/1311  0s] CopyFiles /Users/<USER>/Documents/GitHub/PerfumerProject/Library/Bee/Android/Prj/IL2CPP/Il2CppBackup/il2cppOutput/UnityEngine.UIElementsModule__8.cpp
[1274/1311  0s] CopyFiles /Users/<USER>/Documents/GitHub/PerfumerProject/Library/Bee/Android/Prj/IL2CPP/Il2CppBackup/il2cppOutput/UnityEngine.UIElementsModule__5.cpp
[1275/1311  0s] CopyFiles /Users/<USER>/Documents/GitHub/PerfumerProject/Library/Bee/Android/Prj/IL2CPP/Il2CppBackup/il2cppOutput/UnityEngine.UIElementsModule__1.cpp
[1276/1311  0s] CopyFiles /Users/<USER>/Documents/GitHub/PerfumerProject/Library/Bee/Android/Prj/IL2CPP/Il2CppBackup/il2cppOutput/UnityEngine.TilemapModule_CodeGen.c
[1277/1311  0s] CopyFiles /Users/<USER>/Documents/GitHub/PerfumerProject/Library/Bee/Android/Prj/IL2CPP/Il2CppBackup/il2cppOutput/Assembly-CSharp-firstpass_CodeGen.c
[1278/1311  0s] CopyFiles /Users/<USER>/Documents/GitHub/PerfumerProject/Library/Bee/Android/Prj/IL2CPP/Il2CppBackup/il2cppOutput/UnityEngine.TerrainModule_CodeGen.c
[1279/1311  0s] CopyFiles /Users/<USER>/Documents/GitHub/PerfumerProject/Library/Bee/Android/Prj/IL2CPP/Il2CppBackup/il2cppOutput/UnityEngine.PhysicsModule_CodeGen.c
[1280/1311  0s] CopyFiles /Users/<USER>/Documents/GitHub/PerfumerProject/Library/Bee/Android/Prj/IL2CPP/Il2CppBackup/il2cppOutput/UnityEngine.TextRenderingModule.cpp
[1281/1311  0s] CopyFiles /Users/<USER>/Documents/GitHub/PerfumerProject/Library/Bee/Android/Prj/IL2CPP/Il2CppBackup/il2cppOutput/UnityEngine.JSONSerializeModule.cpp
[1282/1311  0s] CopyFiles /Users/<USER>/Documents/GitHub/PerfumerProject/Library/Bee/Android/Prj/IL2CPP/Il2CppBackup/il2cppOutput/UnityEngine.UIElementsModule__2.cpp
[1283/1311  0s] CopyFiles /Users/<USER>/Documents/GitHub/PerfumerProject/Library/Bee/Android/Prj/IL2CPP/Il2CppBackup/il2cppOutput/UnityEngine.UIElementsModule__7.cpp
[1284/1311  0s] CopyFiles /Users/<USER>/Documents/GitHub/PerfumerProject/Library/Bee/Android/Prj/IL2CPP/Il2CppBackup/il2cppOutput/UnityEngine.UIElementsModule__3.cpp
[1285/1311  0s] CopyFiles /Users/<USER>/Documents/GitHub/PerfumerProject/Library/Bee/Android/Prj/IL2CPP/Il2CppBackup/il2cppOutput/UnityEngine.TextCoreFontEngineModule.cpp
[1286/1311  0s] CopyFiles /Users/<USER>/Documents/GitHub/PerfumerProject/Library/Bee/Android/Prj/IL2CPP/Il2CppBackup/il2cppOutput/UnityEngine.TextCoreTextEngineModule.cpp
[1287/1311  0s] CopyFiles /Users/<USER>/Documents/GitHub/PerfumerProject/Library/Bee/Android/Prj/IL2CPP/Il2CppBackup/il2cppOutput/Unity.TextMeshPro_CodeGen.c
[1288/1311  0s] CopyFiles /Users/<USER>/Documents/GitHub/PerfumerProject/Library/Bee/Android/Prj/IL2CPP/Il2CppBackup/il2cppOutput/Unity.InputSystem_CodeGen.c
[1289/1311  0s] CopyFiles /Users/<USER>/Documents/GitHub/PerfumerProject/Library/Bee/Android/Prj/IL2CPP/Il2CppBackup/il2cppOutput/Il2CppCCTypeValuesTable.cpp
[1290/1311  0s] CopyFiles /Users/<USER>/Documents/GitHub/PerfumerProject/Library/Bee/Android/Prj/IL2CPP/Il2CppBackup/il2cppOutput/ICSharpCode.SharpZipLib.cpp
[1291/1311  0s] CopyFiles /Users/<USER>/Documents/GitHub/PerfumerProject/Library/Bee/Android/Prj/IL2CPP/Il2CppBackup/il2cppOutput/Unity.InputSystem.ForUI.cpp
[1292/1311  0s] CopyFiles /Users/<USER>/Documents/GitHub/PerfumerProject/Library/Bee/Android/Prj/IL2CPP/Il2CppBackup/il2cppOutput/UnityEngine.IMGUIModule.cpp
[1293/1311  0s] CopyFiles /Users/<USER>/Documents/GitHub/PerfumerProject/Library/Bee/Android/Prj/IL2CPP/Il2CppBackup/il2cppOutput/UnityEngine.InputModule.cpp
[1294/1311  0s] CopyFiles /Users/<USER>/Documents/GitHub/PerfumerProject/Library/Bee/Android/Prj/IL2CPP/Il2CppBackup/il2cppOutput/UnityEngine.AudioModule.cpp
[1295/1311  0s] C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/2o7dowz6k0qx.o
[1296/1311  0s] GuidGenerator /Users/<USER>/Documents/GitHub/PerfumerProject/Library/Bee/Android/Prj/IL2CPP/Gradle/unityLibrary/src/main/assets/bin/Data/unity_app_guid
[1297/1311  0s] C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/99th98dnnmgu.o
[1298/1311  0s] C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/5ngizqlpjbsi.o
[1299/1311  0s] C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/y4fszp61xkt9.o
[1300/1311  0s] C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/xjwvhrtue926.o
[1301/1311  0s] C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/3xi6to2kg4fl.o
[1302/1311  2s] Link_Android_arm64 Library/Bee/artifacts/Android/d8kzr/libil2cpp.so
[1303/1311  0s] CopyFiles Library/Bee/artifacts/objcopy_5lmi/libil2cpp.dbg.so
[1304/1311  0s] NdkObjCopy Library/Bee/artifacts/objcopy_5lmi/stripped/libil2cpp.so
[1305/1311  0s] NdkObjCopy Library/Bee/artifacts/objcopy_5lmi/libil2cpp.sym.so
[1306/1311  0s] CopyFiles /Users/<USER>/Documents/GitHub/PerfumerProject/Library/Bee/artifacts/Android/il2cppOutput/build/arm64-v8a/libil2cpp.sym.so
[1307/1311  0s] CopyFiles /Users/<USER>/Documents/GitHub/PerfumerProject/Library/Bee/artifacts/Android/il2cppOutput/build/arm64-v8a/libil2cpp.dbg.so
[1308/1311  0s] NdkObjCopy Library/Bee/artifacts/objcopy_5lmi/libil2cpp.so
[1309/1311  0s] CopyFiles /Users/<USER>/Documents/GitHub/PerfumerProject/Library/Bee/Android/Prj/IL2CPP/Gradle/unityLibrary/src/main/jniLibs/arm64-v8a/libil2cpp.so
[1310/1311  0s] CopyFiles /Users/<USER>/Documents/GitHub/PerfumerProject/Library/Bee/artifacts/Android/il2cppOutput/build/arm64-v8a/libil2cpp.so
*** Tundra build success (15.93 seconds), 668 items updated, 1311 evaluated
Total cache size 91898556
Total cache size after purge 91898556
DisplayProgressbar: Calling IPostGenerateGradleAndroidProject callbacks
Calling IPostGenerateGradleAndroidProject callbacks on '/Users/<USER>/Documents/GitHub/PerfumerProject/Library/Bee/Android/Prj/IL2CPP/Gradle/unityLibrary':
Android PostProcess task "Calling IPostGenerateGradleAndroidProject callbacks" took 1.5719 ms
Android PostProcess task "Cloud Diagnostics Symbol Upload" took 0.1959 ms
DisplayProgressbar: Building Gradle project
ExitCode: 0 Duration: 16s
Android PostProcess task "Building Gradle project" took 30876.0312 ms
DisplayProgressbar: Processing Mapping File
Android PostProcess task "Processing Mapping File" took 0.6868 ms
DisplayProgressbar: Moving output package(s)
Android PostProcess task "Moving output package(s)" took 31.8158 ms
Refreshing native plugins compatible for Editor in 5.36 ms, found 2 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.

-------------------------------------------------------------------------------
Build Report
Information on used Assets is not available, since player data was not rebuilt.
Do a clean build to view the Asset build report information.
-------------------------------------------------------------------------------
Unloading 3 Unused Serialized files (Serialized files now loaded: 0)
Unloading 110 unused Assets / (21.4 MB). Loaded Objects now: 5092.
Memory consumption went from 225.7 MB to 204.2 MB.
Total: 22.461083 ms (FindLiveObjects: 11.321000 ms CreateObjectMapping: 0.099084 ms MarkObjects: 7.492916 ms  DeleteObjects: 3.547375 ms)

Build Finished, Result: Success.
Launching external process: /Applications/Unity/Hub/Editor/2022.3.62f1/Unity.app/Contents/il2cpp/build/deploy/Analytics
##utp:{"type":"PlayerBuildInfo","version":2,"phase":"Immediate","time":1750862445176,"processId":72054,"steps":[{"description":"Preprocess Player","duration":468},{"description":"Prepare For Build","duration":5684},{"description":"ProducePlayerScriptAssemblies","duration":2499},{"description":"Verify Build setup","duration":188},{"description":"Prepare assets for target platform","duration":108},{"description":"Prepare splash screen","duration":1},{"description":"Run script only build","duration":25},{"description":"Postprocess built player","duration":48009}],"duration":56982}
[PhysX] Initialized MultithreadedTaskDispatcher with 8 workers.
Unloading 0 Unused Serialized files (Serialized files now loaded: 0)
Loaded scene 'Temp/__Backupscenes/0.backup'
	Deserialize:            0.897 ms
	Integration:            9.885 ms
	Integration of assets:  0.001 ms
	Thread Wait Time:       0.004 ms
	Total Operation Time:   10.786 ms
Unloading 0 unused Assets / (Unknown). Loaded Objects now: 5092.
Memory consumption went from 177.2 MB to 177.2 MB.
Total: 4.319709 ms (FindLiveObjects: 0.145458 ms CreateObjectMapping: 0.056333 ms MarkObjects: 4.112042 ms  DeleteObjects: 0.005542 ms)

Android APK build succeeded!
UnityEngine.StackTraceUtility:ExtractStackTrace () (at /Users/<USER>/build/output/unity/unity/Runtime/Export/Scripting/StackTrace.cs:37)
UnityEngine.DebugLogHandler:LogFormat (UnityEngine.LogType,UnityEngine.Object,string,object[])
UnityEngine.Logger:Log (UnityEngine.LogType,object)
UnityEngine.Debug:Log (object)
BuildTools.CommandLineBuild:BuildAndroidAPK () (at Assets/Editor/CommandLineBuild.cs:174)
BuildTools.CommandLineBuild:BuildAndroid () (at Assets/Editor/CommandLineBuild.cs:101)

(Filename: Assets/Editor/CommandLineBuild.cs Line: 174)

APK location: /Users/<USER>/Documents/GitHub/PerfumerProject/Assets/../Builds/android/香域.apk
UnityEngine.StackTraceUtility:ExtractStackTrace () (at /Users/<USER>/build/output/unity/unity/Runtime/Export/Scripting/StackTrace.cs:37)
UnityEngine.DebugLogHandler:LogFormat (UnityEngine.LogType,UnityEngine.Object,string,object[])
UnityEngine.Logger:Log (UnityEngine.LogType,object)
UnityEngine.Debug:Log (object)
BuildTools.CommandLineBuild:BuildAndroidAPK () (at Assets/Editor/CommandLineBuild.cs:175)
BuildTools.CommandLineBuild:BuildAndroid () (at Assets/Editor/CommandLineBuild.cs:101)

(Filename: Assets/Editor/CommandLineBuild.cs Line: 175)

Build size: 699332447 bytes
UnityEngine.StackTraceUtility:ExtractStackTrace () (at /Users/<USER>/build/output/unity/unity/Runtime/Export/Scripting/StackTrace.cs:37)
UnityEngine.DebugLogHandler:LogFormat (UnityEngine.LogType,UnityEngine.Object,string,object[])
UnityEngine.Logger:Log (UnityEngine.LogType,object)
UnityEngine.Debug:Log (object)
BuildTools.CommandLineBuild:BuildAndroidAPK () (at Assets/Editor/CommandLineBuild.cs:176)
BuildTools.CommandLineBuild:BuildAndroid () (at Assets/Editor/CommandLineBuild.cs:101)

(Filename: Assets/Editor/CommandLineBuild.cs Line: 176)

Build time: 00:00:57.0222908
UnityEngine.StackTraceUtility:ExtractStackTrace () (at /Users/<USER>/build/output/unity/unity/Runtime/Export/Scripting/StackTrace.cs:37)
UnityEngine.DebugLogHandler:LogFormat (UnityEngine.LogType,UnityEngine.Object,string,object[])
UnityEngine.Logger:Log (UnityEngine.LogType,object)
UnityEngine.Debug:Log (object)
BuildTools.CommandLineBuild:BuildAndroidAPK () (at Assets/Editor/CommandLineBuild.cs:177)
BuildTools.CommandLineBuild:BuildAndroid () (at Assets/Editor/CommandLineBuild.cs:101)

(Filename: Assets/Editor/CommandLineBuild.cs Line: 177)

APK file confirmed: 84897599 bytes
UnityEngine.StackTraceUtility:ExtractStackTrace () (at /Users/<USER>/build/output/unity/unity/Runtime/Export/Scripting/StackTrace.cs:37)
UnityEngine.DebugLogHandler:LogFormat (UnityEngine.LogType,UnityEngine.Object,string,object[])
UnityEngine.Logger:Log (UnityEngine.LogType,object)
UnityEngine.Debug:Log (object)
BuildTools.CommandLineBuild:BuildAndroidAPK () (at Assets/Editor/CommandLineBuild.cs:183)
BuildTools.CommandLineBuild:BuildAndroid () (at Assets/Editor/CommandLineBuild.cs:101)

(Filename: Assets/Editor/CommandLineBuild.cs Line: 183)

Batchmode quit successfully invoked - shutting down!
Killing ADB server in 0.027398 seconds.
Thread 0x31c113000 may have been prematurely finalized
[PhysX] Initialized MultithreadedTaskDispatcher with 8 workers.
Input System module state changed to: ShutdownInProgress.
Input System polling thread exited.
Input System module state changed to: Shutdown.
[Licensing::IpcConnector] LicenseClient-cafe-notifications channel disconnected successfully.
[Licensing::IpcConnector] LicenseClient-cafe channel disconnected successfully.
Thread 0x16f19b000 may have been prematurely finalized
[40m[32minfo[39m[22m[49m: Microsoft.Hosting.Lifetime[0]
      Application is shutting down...
Cleanup mono
debugger-agent: Unable to listen on 48
[usbmuxd] Stop listen thread
[usbmuxd] Error: 
[usbmuxd] Listen thread exiting
[usbmuxd] Stop listen thread
[usbmuxd] Error: 
[usbmuxd] Listen thread exiting
[Performance] Application.InitializeProject                                                                                  :        1 samples, Peak.  6.20 s (1.0x), Avg.  6.20 s, Total. 6.203 s (8.6%)
[Performance] Application.PackageManager.StartServer                                                                         :        1 samples, Peak.  2.16 ms (1.0x), Avg.  2.16 ms, Total. 2.157 ms (0.0%)
[Performance] Application.AcquireProjectLock                                                                                 :        1 samples, Peak.  1.34 ms (1.0x), Avg.  1.34 ms, Total. 1.341 ms (0.0%)
[Performance] Application.InitializeEngineNoGraphics                                                                         :        1 samples, Peak.  30.5 ms (1.0x), Avg.  30.5 ms, Total. 30.47 ms (0.0%)
[Performance] Application.PackageManager.Initialize                                                                          :        1 samples, Peak.   480 ms (1.0x), Avg.   480 ms, Total. 480.1 ms (0.7%)
[Performance] Connecting to Package Manager                                                                                  :       49 samples, Peak.  27.4 us (40.1x), Avg.   684 ns, Total. 33.50 us (0.0%)
[Performance] Application.EngineGraphics.Initialize                                                                          :        1 samples, Peak.  48.5 ms (1.0x), Avg.  48.5 ms, Total. 48.55 ms (0.1%)
[Performance] Application.GI.Initialize                                                                                      :        1 samples, Peak.  2.23 ms (1.0x), Avg.  2.23 ms, Total. 2.227 ms (0.0%)
[Performance] Application.LoadAllDefaultResourcesFromEditor                                                                  :        1 samples, Peak.  1.33 ms (1.0x), Avg.  1.33 ms, Total. 1.328 ms (0.0%)
[Performance] Application.LoadMonoAssemblies                                                                                 :        1 samples, Peak.   700 ms (1.0x), Avg.   700 ms, Total. 699.7 ms (1.0%)
[Performance] RestoreManagedReferences                                                                                       :        2 samples, Peak.  36.8 ms (2.0x), Avg.  18.4 ms, Total. 36.85 ms (0.1%)
[Performance] InitializeOnLoad DrivenRectTransformUndo                                                                       :        2 samples, Peak.   409 us (1.3x), Avg.   315 us, Total. 629.6 us (0.0%)
[Performance] InitializeOnLoad SceneSearch                                                                                   :        2 samples, Peak.  89.5 us (1.1x), Avg.  81.2 us, Total. 162.4 us (0.0%)
[Performance] InitializeOnLoad CloudBuild                                                                                    :        2 samples, Peak.  3.96 us (1.1x), Avg.  3.65 us, Total. 7.292 us (0.0%)
[Performance] InitializeOnLoad NativeFormatImporterUtility                                                                   :        2 samples, Peak.  1.88 ms (1.3x), Avg.  1.42 ms, Total. 2.847 ms (0.0%)
[Performance] InitializeOnLoad SearchService                                                                                 :        2 samples, Peak.  90.2 us (1.1x), Avg.  79.4 us, Total. 158.8 us (0.0%)
[Performance] InitializeOnLoad SettingsService                                                                               :        2 samples, Peak.  7.04 us (1.0x), Avg.  6.96 us, Total. 13.92 us (0.0%)
[Performance] InitializeOnLoad WindowLayout                                                                                  :        2 samples, Peak.   182 us (1.2x), Avg.   156 us, Total. 312.3 us (0.0%)
[Performance] InitializeOnLoad AssetStoreContext                                                                             :        2 samples, Peak.  12.0 ms (1.6x), Avg.  7.27 ms, Total. 14.54 ms (0.0%)
[Performance] InitializeOnLoad PoolManager                                                                                   :        2 samples, Peak.   454 us (1.2x), Avg.   382 us, Total. 763.7 us (0.0%)
[Performance] InitializeOnLoad MixerEffectDefinitionReloader                                                                 :        2 samples, Peak.  1.93 ms (1.0x), Avg.  1.85 ms, Total. 3.710 ms (0.0%)
[Performance] InitializeOnLoad ProjectSearch                                                                                 :        2 samples, Peak.  39.5 us (1.1x), Avg.  35.9 us, Total. 71.83 us (0.0%)
[Performance] InitializeOnLoad UISystemProfilerRenderService                                                                 :        2 samples, Peak.  7.62 us (1.1x), Avg.  6.67 us, Total. 13.33 us (0.0%)
[Performance] InitializeOnLoad EditMode                                                                                      :        2 samples, Peak.   318 us (1.0x), Avg.   305 us, Total. 611.0 us (0.0%)
[Performance] InitializeOnLoad UnityConnect                                                                                  :        2 samples, Peak.  40.3 us (1.1x), Avg.  35.8 us, Total. 71.54 us (0.0%)
[Performance] InitializeOnLoad ManagedDebugger                                                                               :        2 samples, Peak.   458 us (1.1x), Avg.   400 us, Total. 800.3 us (0.0%)
[Performance] InitializeOnLoad ShortcutIntegration                                                                           :        2 samples, Peak.  49.3 us (1.2x), Avg.  40.4 us, Total. 80.71 us (0.0%)
[Performance] InitializeOnLoad AddComponentWindow                                                                            :        2 samples, Peak.  87.0 us (1.1x), Avg.  78.0 us, Total. 156.0 us (0.0%)
[Performance] InitializeOnLoad ManagedDebuggerWindow                                                                         :        2 samples, Peak.  93.8 us (1.2x), Avg.  79.8 us, Total. 159.5 us (0.0%)
[Performance] InitializeOnLoad CacheServerWindow                                                                             :        2 samples, Peak.  7.63 us (1.0x), Avg.  7.44 us, Total. 14.88 us (0.0%)
[Performance] InitializeOnLoad PlayModeDownload                                                                              :        2 samples, Peak.  2.21 ms (1.9x), Avg.  1.14 ms, Total. 2.276 ms (0.0%)
[Performance] InitializeOnLoad MenuItems                                                                                     :        2 samples, Peak.  68.5 us (1.2x), Avg.  57.9 us, Total. 115.8 us (0.0%)
[Performance] InitializeOnLoad PrefabInstanceChangedListener                                                                 :        2 samples, Peak.  96.2 us (1.1x), Avg.  84.9 us, Total. 169.8 us (0.0%)
[Performance] InitializeOnLoad ObjectSelectorSearch                                                                          :        2 samples, Peak.  24.5 us (1.1x), Avg.  22.9 us, Total. 45.88 us (0.0%)
[Performance] InitializeOnLoad EditorMonitor                                                                                 :        2 samples, Peak.   453 us (1.1x), Avg.   400 us, Total. 800.0 us (0.0%)
[Performance] InitializeOnLoad RetainedMode                                                                                  :        2 samples, Peak.  1.74 ms (1.1x), Avg.  1.54 ms, Total. 3.082 ms (0.0%)
[Performance] InitializeOnLoad EditorShaderLoader                                                                            :        2 samples, Peak.   494 us (1.2x), Avg.   411 us, Total. 821.2 us (0.0%)
[Performance] InitializeOnLoad EditorDelegateRegistration                                                                    :        2 samples, Peak.   365 us (1.0x), Avg.   349 us, Total. 697.7 us (0.0%)
[Performance] InitializeOnLoad UIDocumentHierarchyWatcher                                                                    :        2 samples, Peak.  50.3 us (1.1x), Avg.  45.1 us, Total. 90.25 us (0.0%)
[Performance] InitializeOnLoad LiveReloadTrackerCreator                                                                      :        2 samples, Peak.   148 us (1.1x), Avg.   130 us, Total. 261.0 us (0.0%)
[Performance] InitializeOnLoad UxmlObjectEditorFactories                                                                     :        2 samples, Peak.   353 us (1.1x), Avg.   336 us, Total. 671.6 us (0.0%)
[Performance] InitializeOnLoad UXMLEditorFactories                                                                           :        2 samples, Peak.  33.0 ms (1.0x), Avg.  32.4 ms, Total. 64.84 ms (0.1%)
[Performance] InitializeOnLoad PurchasingService                                                                             :        2 samples, Peak.  3.90 ms (1.0x), Avg.  3.72 ms, Total. 7.433 ms (0.0%)
[Performance] InitializeOnLoad CloudBuildPoller                                                                              :        2 samples, Peak.  60.8 us (1.0x), Avg.  59.5 us, Total. 118.9 us (0.0%)
[Performance] InitializeOnLoad EditorGameServicesAnalytics                                                                   :        2 samples, Peak.   639 us (1.2x), Avg.   521 us, Total. 1.043 ms (0.0%)
[Performance] InitializeOnLoad AnalyticsService                                                                              :        2 samples, Peak.   101 us (1.0x), Avg.  97.4 us, Total. 194.8 us (0.0%)
[Performance] InitializeOnLoad BuildService                                                                                  :        2 samples, Peak.  86.8 us (1.0x), Avg.  84.5 us, Total. 169.0 us (0.0%)
[Performance] InitializeOnLoad AdsService                                                                                    :        2 samples, Peak.   211 us (1.0x), Avg.   210 us, Total. 420.2 us (0.0%)
[Performance] InitializeOnLoad UDPService                                                                                    :        2 samples, Peak.  92.7 us (1.1x), Avg.  86.1 us, Total. 172.2 us (0.0%)
[Performance] InitializeOnLoad CrashService                                                                                  :        2 samples, Peak.  81.4 us (1.0x), Avg.  79.8 us, Total. 159.6 us (0.0%)
[Performance] InitializeOnLoad ServicesRepository                                                                            :        2 samples, Peak.   833 ns (1.6x), Avg.   520 ns, Total. 1.041 us (0.0%)
[Performance] InitializeOnLoad SearchWindow                                                                                  :        2 samples, Peak.  73.6 us (1.1x), Avg.  66.1 us, Total. 132.2 us (0.0%)
[Performance] InitializeOnLoad CustomIndexers                                                                                :        2 samples, Peak.  9.33 ms (1.1x), Avg.  8.51 ms, Total. 17.03 ms (0.0%)
[Performance] InitializeOnLoad SearchMonitor                                                                                 :        2 samples, Peak.   716 us (1.2x), Avg.   578 us, Total. 1.157 ms (0.0%)
[Performance] InitializeOnLoad ParameterControllerEditor                                                                     :        2 samples, Peak.  17.0 us (1.1x), Avg.  15.0 us, Total. 30.08 us (0.0%)
[Performance] InitializeOnLoad LayerSettingsWindow                                                                           :        2 samples, Peak.  15.8 us (1.7x), Avg.  9.15 us, Total. 18.29 us (0.0%)
[Performance] InitializeOnLoad PackageManagerHookGUIDConverter.RegisterPackagesEventHandler                                  :        2 samples, Peak.   432 us (1.7x), Avg.   248 us, Total. 495.5 us (0.0%)
[Performance] InitializeOnLoad EditorDragAndDrop.RegisterEditorClient                                                        :        2 samples, Peak.   135 us (1.1x), Avg.   121 us, Total. 241.8 us (0.0%)
[Performance] InitializeOnLoad EditorEventCallbacks.InitializeFontAssetResourceChangeCallBacks                               :        2 samples, Peak.   675 us (1.0x), Avg.   653 us, Total. 1.306 ms (0.0%)
[Performance] InitializeOnLoad SceneTemplateService.Init                                                                     :        2 samples, Peak.   687 us (1.0x), Avg.   661 us, Total. 1.323 ms (0.0%)
[Performance] InitializeOnLoad DiagnosticSwitchesConsoleMessage.Init                                                         :        2 samples, Peak.  1.83 ms (1.6x), Avg.  1.11 ms, Total. 2.222 ms (0.0%)
[Performance] InitializeOnLoad PackageManagerWindow.EditorInitializedInSafeMode                                              :        2 samples, Peak.  14.3 ms (2.0x), Avg.  7.18 ms, Total. 14.36 ms (0.0%)
[Performance] InitializeOnLoad ToolShortcutContext.Init                                                                      :        2 samples, Peak.  79.6 us (1.1x), Avg.  75.5 us, Total. 151.1 us (0.0%)
[Performance] InitializeOnLoad MemoryProfilerCompilationGuard.InjectCompileGuard                                             :        2 samples, Peak.  84.1 us (1.0x), Avg.  83.4 us, Total. 166.7 us (0.0%)
[Performance] InitializeOnLoad AssetPostprocessingInternal.RefreshCustomDependencies                                         :        2 samples, Peak.  1.98 ms (1.6x), Avg.  1.25 ms, Total. 2.505 ms (0.0%)
[Performance] InitializeOnLoad SysrootManager.Initialize                                                                     :        2 samples, Peak.  4.83 us (1.0x), Avg.  4.60 us, Total. 9.208 us (0.0%)
[Performance] InitializeOnLoad ScopedRegistryAddedPopup.SubscribeToRegistriesAdded                                           :        2 samples, Peak.  4.58 ms (2.0x), Avg.  2.31 ms, Total. 4.626 ms (0.0%)
[Performance] InitializeOnLoad SceneVisibilityManager.Initialize                                                             :        2 samples, Peak.  1.64 ms (1.6x), Avg.  1.03 ms, Total. 2.063 ms (0.0%)
[Performance] InitializeOnLoad EditorWindow.Initialize                                                                       :        2 samples, Peak.   119 us (1.0x), Avg.   115 us, Total. 229.4 us (0.0%)
[Performance] AssemblyReloadEvents.afterAssemblyReload: UnityEditor.SceneTemplate.SceneTemplateService.AfterAssemblyReload   :        2 samples, Peak.  1.97 ms (1.9x), Avg.  1.06 ms, Total. 2.119 ms (0.0%)
[Performance] ProcessService.EditorAfterLoadAllAssemblies                                                                    :        2 samples, Peak.   188 us (1.2x), Avg.   160 us, Total. 320.2 us (0.0%)
[Performance] Application.ReadLicenseInfo                                                                                    :        1 samples, Peak.  26.0 ms (1.0x), Avg.  26.0 ms, Total. 25.97 ms (0.0%)
[Performance] Application.InitialRefresh                                                                                     :        1 samples, Peak.  4.10 s (1.0x), Avg.  4.10 s, Total. 4.104 s (5.7%)
[Performance] ShaderImporter.SourceAssetModified                                                                             :        2 samples, Peak.   542 ns (1.4x), Avg.   396 ns, Total. 792.0 ns (0.0%)
[Performance] ShaderIncludeImporter.SourceAssetModified                                                                      :        2 samples, Peak.   667 ns (1.1x), Avg.   605 ns, Total. 1.209 us (0.0%)
[Performance] Compiling Scripts                                                                                              :        1 samples, Peak.  1.75 s (1.0x), Avg.  1.75 s, Total. 1.747 s (2.4%)
[Performance] AssemblyReloadEvents.beforeAssemblyReload: UnityEditor.SceneTemplate.SceneTemplateService.BeforeAssemblyReload :        1 samples, Peak.   299 us (1.0x), Avg.   299 us, Total. 299.3 us (0.0%)
[Performance] PresetManagerPostProcessor.OnPreprocessAsset                                                                   :        1 samples, Peak.   606 us (1.0x), Avg.   606 us, Total. 606.1 us (0.0%)
[Performance] CancelSplashScreenOnAssetChange.OnPreprocessAsset                                                              :        1 samples, Peak.   355 us (1.0x), Avg.   355 us, Total. 355.3 us (0.0%)
[Performance] InitializeOnLoad TouchSimulation                                                                               :        1 samples, Peak.   162 us (1.0x), Avg.   162 us, Total. 162.5 us (0.0%)
[Performance] InitializeOnLoad EnableUITKEditor                                                                              :        1 samples, Peak.  13.0 us (1.0x), Avg.  13.0 us, Total. 13.04 us (0.0%)
[Performance] InitializeOnLoad InputSystem                                                                                   :        1 samples, Peak.  27.4 ms (1.0x), Avg.  27.4 ms, Total. 27.35 ms (0.0%)
[Performance] InitializeOnLoad InputSystemUIInputModuleEditor                                                                :        1 samples, Peak.  80.7 us (1.0x), Avg.  80.7 us, Total. 80.71 us (0.0%)
[Performance] InitializeOnLoad DeEditorNotification                                                                          :        1 samples, Peak.   308 us (1.0x), Avg.   308 us, Total. 308.2 us (0.0%)
[Performance] InitializeOnLoad DeScriptExecutionOrderManager                                                                 :        1 samples, Peak.   136 us (1.0x), Avg.   136 us, Total. 135.9 us (0.0%)
[Performance] InitializeOnLoad DOTweenProEditorManager                                                                       :        1 samples, Peak.   263 us (1.0x), Avg.   263 us, Total. 262.9 us (0.0%)
[Performance] InitializeOnLoad Initializer                                                                                   :        2 samples, Peak.   108 us (1.2x), Avg.  91.7 us, Total. 183.3 us (0.0%)
[Performance] InitializeOnLoad ReadmeEditor                                                                                  :        1 samples, Peak.  81.6 us (1.0x), Avg.  81.6 us, Total. 81.58 us (0.0%)
[Performance] InitializeOnLoad VSUsageUtility                                                                                :        1 samples, Peak.  19.9 ms (1.0x), Avg.  19.9 ms, Total. 19.88 ms (0.0%)
[Performance] InitializeOnLoad BackgroundWatcher                                                                             :        1 samples, Peak.  80.3 us (1.0x), Avg.  80.3 us, Total. 80.29 us (0.0%)
[Performance] InitializeOnLoad UnityTestProtocolStarter                                                                      :        1 samples, Peak.   134 us (1.0x), Avg.   134 us, Total. 133.6 us (0.0%)
[Performance] InitializeOnLoad TestStarter                                                                                   :        1 samples, Peak.   341 us (1.0x), Avg.   341 us, Total. 340.5 us (0.0%)
[Performance] InitializeOnLoad RerunCallbackInitializer                                                                      :        1 samples, Peak.  1.82 ms (1.0x), Avg.  1.82 ms, Total. 1.816 ms (0.0%)
[Performance] InitializeOnLoad VisualStudioEditor                                                                            :        1 samples, Peak.  9.18 ms (1.0x), Avg.  9.18 ms, Total. 9.184 ms (0.0%)
[Performance] InitializeOnLoad TestRunnerApiListener                                                                         :        1 samples, Peak.   359 us (1.0x), Avg.   359 us, Total. 358.7 us (0.0%)
[Performance] InitializeOnLoad VisualStudioIntegration                                                                       :        1 samples, Peak.  2.96 ms (1.0x), Avg.  2.96 ms, Total. 2.960 ms (0.0%)
[Performance] InitializeOnLoad PrefabLayoutRebuilder                                                                         :        1 samples, Peak.   237 us (1.0x), Avg.   237 us, Total. 236.8 us (0.0%)
[Performance] InitializeOnLoad RiderScriptEditor                                                                             :        1 samples, Peak.  4.54 ms (1.0x), Avg.  4.54 ms, Total. 4.543 ms (0.0%)
[Performance] InitializeOnLoad CallbackInitializer                                                                           :        1 samples, Peak.   654 us (1.0x), Avg.   654 us, Total. 654.5 us (0.0%)
[Performance] InitializeOnLoad SpriteEditorWindow                                                                            :        1 samples, Peak.   240 us (1.0x), Avg.   240 us, Total. 239.5 us (0.0%)
[Performance] InitializeOnLoad CoverageReporterStarter                                                                       :        1 samples, Peak.  2.98 ms (1.0x), Avg.  2.98 ms, Total. 2.983 ms (0.0%)
[Performance] InitializeOnLoad PlasticPlugin                                                                                 :        1 samples, Peak.  34.0 ms (1.0x), Avg.  34.0 ms, Total. 34.03 ms (0.0%)
[Performance] InitializeOnLoad FungusEditorPreferences                                                                       :        1 samples, Peak.   131 us (1.0x), Avg.   131 us, Total. 131.1 us (0.0%)
[Performance] InitializeOnLoad HierarchyIcons                                                                                :        1 samples, Peak.  82.5 us (1.0x), Avg.  82.5 us, Total. 82.54 us (0.0%)
[Performance] InitializeOnLoad ToolbarBootstrap                                                                              :        1 samples, Peak.  27.6 ms (1.0x), Avg.  27.6 ms, Total. 27.60 ms (0.0%)
[Performance] InitializeOnLoad VSCodeScriptEditor                                                                            :        1 samples, Peak.  1.38 ms (1.0x), Avg.  1.38 ms, Total. 1.376 ms (0.0%)
[Performance] InitializeOnLoad SysrootPackage.IssueWarningIfLinuxIL2CPPNotPresent                                            :        1 samples, Peak.   103 us (1.0x), Avg.   103 us, Total. 103.0 us (0.0%)
[Performance] InitializeOnLoad InputSystemPackageControl.SubscribePackageManagerEvent                                        :        1 samples, Peak.  41.2 us (1.0x), Avg.  41.2 us, Total. 41.17 us (0.0%)
[Performance] InitializeOnLoad InputSystemPluginControl.CheckForExtension                                                    :        1 samples, Peak.   853 us (1.0x), Avg.   853 us, Total. 852.8 us (0.0%)
[Performance] InitializeOnLoad TestJobDataHolder.ResumeRunningJobs                                                           :        1 samples, Peak.   562 us (1.0x), Avg.   562 us, Total. 562.5 us (0.0%)
[Performance] InitializeOnLoad AnalyticsReporter.RegisterCallbacks                                                           :        1 samples, Peak.   148 us (1.0x), Avg.   148 us, Total. 148.0 us (0.0%)
[Performance] InitializeOnLoad VisualStudioEditor.LegacyVisualStudioCodePackageDisabler                                      :        1 samples, Peak.  1.66 ms (1.0x), Avg.  1.66 ms, Total. 1.659 ms (0.0%)
[Performance] DidReloadScriptsNavMeshExtension.OnScriptReload                                                                :        1 samples, Peak.   109 us (1.0x), Avg.   109 us, Total. 108.6 us (0.0%)
[Performance] DidReloadScriptsFungusEditorResources.OnDidReloadScripts                                                       :        1 samples, Peak.  44.9 ms (1.0x), Avg.  44.9 ms, Total. 44.92 ms (0.1%)
[Performance] DidReloadScriptsEventSelectorPopupWindowContent.OnScriptsReloaded                                              :        1 samples, Peak.   522 us (1.0x), Avg.   522 us, Total. 522.0 us (0.0%)
[Performance] DidReloadScriptsCommandSelectorPopupWindowContent.OnScriptsReloaded                                            :        1 samples, Peak.   237 us (1.0x), Avg.   237 us, Total. 237.2 us (0.0%)
[Performance] DidReloadScriptsVariableSelectPopupWindowContent.OnScriptsReloaded                                             :        1 samples, Peak.   113 us (1.0x), Avg.   113 us, Total. 113.4 us (0.0%)
[Performance] DidReloadScriptsAdvancedDropdownWindow.OnScriptReload                                                          :        2 samples, Peak.   157 us (1.2x), Avg.   135 us, Total. 269.8 us (0.0%)
[Performance] DidReloadScriptsTestRunnerWindow.CompilationCallback                                                           :        1 samples, Peak.   275 us (1.0x), Avg.   275 us, Total. 274.6 us (0.0%)
[Performance] DidReloadScriptsTestListCache.ScriptReloaded                                                                   :        1 samples, Peak.   762 us (1.0x), Avg.   762 us, Total. 762.0 us (0.0%)
[Performance] DidReloadScriptsLuaBindingsEditor.DidReloadScripts                                                             :        1 samples, Peak.   247 us (1.0x), Avg.   247 us, Total. 246.6 us (0.0%)
[Performance] AssetDatabase.ImportAssets                                                                                     :        5 samples, Peak.   333 ns (2.3x), Avg.   142 ns, Total. 709.0 ns (0.0%)
[Performance] VersionControl.Task.Wait                                                                                       :        1 samples, Peak.  10.4 us (1.0x), Avg.  10.4 us, Total. 10.38 us (0.0%)
[Performance] UtilityWindowPostProcessor.OnPostprocessAllAssets                                                              :        2 samples, Peak.   362 us (1.1x), Avg.   325 us, Total. 650.5 us (0.0%)
[Performance] EditorResourcesPostProcessor.OnPostprocessAllAssets                                                            :        2 samples, Peak.   105 us (1.0x), Avg.   104 us, Total. 208.7 us (0.0%)
[Performance] TMPro_TexturePostProcessor.OnPostprocessAllAssets                                                              :        2 samples, Peak.   202 us (1.0x), Avg.   201 us, Total. 401.6 us (0.0%)
[Performance] AssetPostprocessor.OnPostprocessAllAssets                                                                      :        2 samples, Peak.   578 us (1.0x), Avg.   577 us, Total. 1.155 ms (0.0%)
[Performance] BuilderAssetPostprocessor.OnPostprocessAllAssets                                                               :        2 samples, Peak.  1.76 ms (1.0x), Avg.  1.76 ms, Total. 3.527 ms (0.0%)
[Performance] ArtifactBrowserPostProcessor.OnPostprocessAllAssets                                                            :        2 samples, Peak.   291 us (1.0x), Avg.   291 us, Total. 581.1 us (0.0%)
[Performance] AssetEvents.OnPostprocessAllAssets                                                                             :        4 samples, Peak.   106 us (1.6x), Avg.  67.5 us, Total. 269.8 us (0.0%)
[Performance] AudioMixerPostprocessor.OnPostprocessAllAssets                                                                 :        2 samples, Peak.   170 us (1.0x), Avg.   169 us, Total. 337.4 us (0.0%)
[Performance] StyleCatalogPostProcessor.OnPostprocessAllAssets                                                               :        2 samples, Peak.  13.4 ms (1.0x), Avg.  13.4 ms, Total. 26.77 ms (0.0%)
[Performance] BuildCatalog                                                                                                   :        1 samples, Peak.  12.8 ms (1.0x), Avg.  12.8 ms, Total. 12.76 ms (0.0%)
[Performance] ModelImporterPostProcessor.OnPostprocessAllAssets                                                              :        2 samples, Peak.   564 us (1.0x), Avg.   563 us, Total. 1.126 ms (0.0%)
[Performance] RetainedModeAssetPostprocessor.OnPostprocessAllAssets                                                          :        2 samples, Peak.  1.20 ms (1.0x), Avg.  1.20 ms, Total. 2.392 ms (0.0%)
[Performance] AssetChangedListener.OnPostprocessAllAssets                                                                    :        2 samples, Peak.   728 us (1.0x), Avg.   727 us, Total. 1.454 ms (0.0%)
[Performance] SpeedTreePostProcessor.OnPostprocessAllAssets                                                                  :        2 samples, Peak.   102 us (1.0x), Avg.   101 us, Total. 202.4 us (0.0%)
[Performance] TextAssetPostProcessor.OnPostprocessAllAssets                                                                  :        2 samples, Peak.   212 us (1.0x), Avg.   211 us, Total. 422.5 us (0.0%)
[Performance] SpriteEditorTexturePostprocessor.OnPostprocessAllAssets                                                        :        2 samples, Peak.   137 us (1.0x), Avg.   136 us, Total. 272.9 us (0.0%)
[Performance] InputActionAssetPostprocessor.OnPostprocessAllAssets                                                           :        2 samples, Peak.   177 us (1.0x), Avg.   175 us, Total. 350.7 us (0.0%)
[Performance] InputActionJsonNameModifierAssetProcessor.OnPostprocessAllAssets                                               :        2 samples, Peak.  31.0 us (1.0x), Avg.  30.3 us, Total. 60.67 us (0.0%)
[Performance] ProjectSettingsPostprocessor.OnPostprocessAllAssets                                                            :        2 samples, Peak.   111 us (1.0x), Avg.   111 us, Total. 221.3 us (0.0%)
[Performance] SyncVS.PostprocessSyncProject                                                                                  :        1 samples, Peak.  1.78 ms (1.0x), Avg.  1.78 ms, Total. 1.784 ms (0.0%)
[Performance] Application.ImportPackagesAndSetTemplateWhenCreatingProject                                                    :        1 samples, Peak.  12.2 ms (1.0x), Avg.  12.2 ms, Total. 12.22 ms (0.0%)
[Performance] Application.SyncCurrentColorSpace                                                                              :        1 samples, Peak.  84.5 ms (1.0x), Avg.  84.5 ms, Total. 84.50 ms (0.1%)
[Performance] Application.OnUsbDevicesChanged                                                                                :        1 samples, Peak.  50.0 us (1.0x), Avg.  50.0 us, Total. 50.00 us (0.0%)
[Performance] Application.AssetInstanceCacheUpdate                                                                           :        1 samples, Peak.   166 ns (1.0x), Avg.   166 ns, Total. 166.0 ns (0.0%)
[Performance] Application.UnityExtensions.Initialize                                                                         :        1 samples, Peak.  4.15 ms (1.0x), Avg.  4.15 ms, Total. 4.151 ms (0.0%)
[Performance] CodeEditorProjectSync.SyncEditorProject                                                                        :        1 samples, Peak.   129 ms (1.0x), Avg.   129 ms, Total. 129.3 ms (0.2%)
[Performance] Application.ExecuteStartups                                                                                    :        1 samples, Peak.  44.2 ms (1.0x), Avg.  44.2 ms, Total. 44.19 ms (0.1%)
[Performance] Menu.RegisterMenuInterface                                                                                     :       26 samples, Peak.  1.42 us (8.0x), Avg.   178 ns, Total. 4.625 us (0.0%)
[Performance] Gizmo.RebuildRenderers                                                                                         :        2 samples, Peak.  43.3 ms (1.5x), Avg.  29.2 ms, Total. 58.31 ms (0.1%)
[Performance] Gizmo.AddGizmoRenderers                                                                                        :      192 samples, Peak.   958 us (69.8x), Avg.  13.7 us, Total. 2.636 ms (0.0%)
[Performance] Application.editorInitializingProject                                                                          :        1 samples, Peak.  15.7 ms (1.0x), Avg.  15.7 ms, Total. 15.72 ms (0.0%)
[Performance] Application.InitializeMenu                                                                                     :        1 samples, Peak.   776 ms (1.0x), Avg.   776 ms, Total. 775.9 ms (1.1%)
[Performance] Menu.RebuildAll                                                                                                :        1 samples, Peak.   776 ms (1.0x), Avg.   776 ms, Total. 775.8 ms (1.1%)
[Performance] Menu.BuildRegisteredMenuInterfaces                                                                             :        1 samples, Peak.   772 ms (1.0x), Avg.   772 ms, Total. 771.9 ms (1.1%)
[Performance] Menu.FilterMenuItem                                                                                            :      799 samples, Peak.  24.0 ms (792.5x), Avg.  30.3 us, Total. 24.20 ms (0.0%)
[Performance] UpdateAllMenus                                                                                                 :        1 samples, Peak.   336 us (1.0x), Avg.   336 us, Total. 335.5 us (0.0%)
[Performance] EditorSceneManager.sceneClosing: UnityEditor.SceneVisibilityManager.EditorSceneManagerOnSceneClosing           :        2 samples, Peak.   165 us (1.9x), Avg.  87.1 us, Total. 174.1 us (0.0%)
[Performance] GUIView.RepaintAll.PlayerLoopController                                                                        :        2 samples, Peak.  3.75 us (1.8x), Avg.  2.12 us, Total. 4.250 us (0.0%)
[Performance] EditorSceneManager.newSceneCreated: UnityEditor.SceneTemplate.SceneTemplateService.OnNewSceneCreated           :        1 samples, Peak.   148 us (1.0x), Avg.   148 us, Total. 148.0 us (0.0%)
[Performance] EditorSceneManager.newSceneCreated: UnityEditor.SceneVisibilityManager.EditorSceneManagerOnNewSceneCreated     :        1 samples, Peak.  94.0 us (1.0x), Avg.  94.0 us, Total. 94.00 us (0.0%)
[Performance] EditorSceneManager.newSceneCreated: UnityEditor.SceneManagement.StageNavigationManager.OnNewSceneCreated       :        1 samples, Peak.  38.9 us (1.0x), Avg.  38.9 us, Total. 38.92 us (0.0%)
[Performance] Application.InvokeFinishedLoadingProject                                                                       :        1 samples, Peak.  8.64 ms (1.0x), Avg.  8.64 ms, Total. 8.642 ms (0.0%)
[Performance] ProcessService.OnProjectLoaded                                                                                 :        1 samples, Peak.  31.8 us (1.0x), Avg.  31.8 us, Total. 31.83 us (0.0%)
[Performance] Inspector.InitOrRebuild                                                                                        :        2 samples, Peak.   351 us (1.2x), Avg.   305 us, Total. 609.8 us (0.0%)
[Performance] StateMacroSavedEvent.OnWillSaveAssets                                                                          :        2 samples, Peak.  90.6 us (1.8x), Avg.  50.8 us, Total. 101.7 us (0.0%)
[Performance] AssetModProcessor.OnWillSaveAssets                                                                             :        2 samples, Peak.   128 us (2.0x), Avg.  64.8 us, Total. 129.5 us (0.0%)
[Performance] AssetModificationProcessor.OnWillSaveAssets                                                                    :        2 samples, Peak.   148 us (1.9x), Avg.  76.7 us, Total. 153.3 us (0.0%)
[Performance] UnityCloudProjectLinkMonitor.OnWillSaveAssets                                                                  :        2 samples, Peak.   159 us (2.0x), Avg.  79.9 us, Total. 159.8 us (0.0%)
[Performance] FlowMacroSavedEvent.OnWillSaveAssets                                                                           :        2 samples, Peak.  75.0 us (2.0x), Avg.  37.9 us, Total. 75.87 us (0.0%)
[Performance] BuilderAssetModificationProcessor.OnWillSaveAssets                                                             :        2 samples, Peak.   192 us (2.0x), Avg.  97.2 us, Total. 194.5 us (0.0%)
[Performance] TerrainModificationProcessor.OnWillSaveAssets                                                                  :        2 samples, Peak.   364 us (2.0x), Avg.   186 us, Total. 372.8 us (0.0%)
[Performance] Application.Message                                                                                            :       77 samples, Peak.  2.96 us (18.3x), Avg.   162 ns, Total. 12.46 us (0.0%)
[Performance] Application.Idle                                                                                               :       76 samples, Peak.  3.41 ms (1.0x), Avg.  3.32 ms, Total. 252.0 ms (0.4%)
[Performance] GenerateAssemblyTypeInfos                                                                                      :        2 samples, Peak.  24.3 ms (1.2x), Avg.  20.9 ms, Total. 41.71 ms (0.1%)
[Performance] AssetDatabase.Refresh                                                                                          :        1 samples, Peak.  73.2 ms (1.0x), Avg.  73.2 ms, Total. 73.20 ms (0.1%)
[Performance] EditorApplication.quitting: UnityEditor.SettingsManagement.FileSettingsRepository.Save                         :        1 samples, Peak.  2.01 ms (1.0x), Avg.  2.01 ms, Total. 2.005 ms (0.0%)
[Performance] EditorApplication.quitting: callback in UnityEditor.TextCore.Text.EditorEventCallbacks                         :        1 samples, Peak.  45.8 ms (1.0x), Avg.  45.8 ms, Total. 45.78 ms (0.1%)
[Performance] Killing ADB server                                                                                             :        1 samples, Peak.  27.5 ms (1.0x), Avg.  27.5 ms, Total. 27.55 ms (0.0%)
[Performance] Application.Shutdown.PauseProfilerSession                                                                      :        1 samples, Peak.  35.0 us (1.0x), Avg.  35.0 us, Total. 34.96 us (0.0%)
[Performance] Application.Shutdown.PauseAssetImportWorkers                                                                   :        1 samples, Peak.   289 us (1.0x), Avg.   289 us, Total. 288.6 us (0.0%)
[Performance] Application.Shutdown.SaveAssets                                                                                :        1 samples, Peak.  4.56 ms (1.0x), Avg.  4.56 ms, Total. 4.557 ms (0.0%)
[Performance] Application.Shutdown.CleanupRenderPipeline                                                                     :        1 samples, Peak.  9.33 us (1.0x), Avg.  9.33 us, Total. 9.333 us (0.0%)
[Performance] Application.Shutdown.StopPreloadManager                                                                        :        1 samples, Peak.  11.1 ms (1.0x), Avg.  11.1 ms, Total. 11.06 ms (0.0%)
[Performance] Application.Shutdown.DestroyWorld                                                                              :        1 samples, Peak.   708 us (1.0x), Avg.   708 us, Total. 708.2 us (0.0%)
[Performance] Application.Shutdown.CleanupAfterLoad                                                                          :        1 samples, Peak.  6.83 ms (1.0x), Avg.  6.83 ms, Total. 6.825 ms (0.0%)
[Performance] Application.Shutdown.Progress                                                                                  :        1 samples, Peak.  34.1 us (1.0x), Avg.  34.1 us, Total. 34.12 us (0.0%)
[Performance] Application.Shutdown.GICleanupManagers                                                                         :        1 samples, Peak.  4.09 ms (1.0x), Avg.  4.09 ms, Total. 4.088 ms (0.0%)
[Performance] Application.Shutdown.MenuCleanupClass                                                                          :        1 samples, Peak.   177 us (1.0x), Avg.   177 us, Total. 177.0 us (0.0%)
[Performance] Application.Shutdown.ADBSaveStateBeforeShutdown                                                                :        1 samples, Peak.  8.50 us (1.0x), Avg.  8.50 us, Total. 8.500 us (0.0%)
[Performance] Application.Shutdown.RemoteShutdown                                                                            :        1 samples, Peak.   542 ns (1.0x), Avg.   542 ns, Total. 542.0 ns (0.0%)
[Performance] Application.Shutdown.CleanupVCProvider                                                                         :        1 samples, Peak.   195 us (1.0x), Avg.   195 us, Total. 194.8 us (0.0%)
[Performance] Application.Shutdown.InputShutdown                                                                             :        1 samples, Peak.   182 us (1.0x), Avg.   182 us, Total. 181.7 us (0.0%)
[Performance] Application.Shutdown.GizmoManagerDestroy                                                                       :        1 samples, Peak.   435 us (1.0x), Avg.   435 us, Total. 434.9 us (0.0%)
[Performance] Application.Shutdown.ProfilerSession                                                                           :        1 samples, Peak.   537 us (1.0x), Avg.   537 us, Total. 536.7 us (0.0%)
[Performance] Application.Shutdown.ReleaseGfxWindowOnAllGUIViews                                                             :        1 samples, Peak.  1.54 us (1.0x), Avg.  1.54 us, Total. 1.541 us (0.0%)
[Performance] Application.Shutdown.CleanupEngine                                                                             :        1 samples, Peak.   141 ms (1.0x), Avg.   141 ms, Total. 140.9 ms (0.2%)
[Performance] Application.Shutdown.CleanupAssetDatabase                                                                      :        1 samples, Peak.  4.81 ms (1.0x), Avg.  4.81 ms, Total. 4.807 ms (0.0%)
[Performance] Application.Shutdown.ScriptCompilationCleanUp                                                                  :        1 samples, Peak.  16.1 us (1.0x), Avg.  16.1 us, Total. 16.08 us (0.0%)
[Performance] Application.Shutdown.DestroyJobSystem                                                                          :        1 samples, Peak.   387 us (1.0x), Avg.   387 us, Total. 387.2 us (0.0%)
[Performance] Application.Shutdown.CleanupPersistentManager                                                                  :        1 samples, Peak.  3.16 ms (1.0x), Avg.  3.16 ms, Total. 3.160 ms (0.0%)
[Performance] Application.Shutdown.CleanupAsyncReadManager                                                                   :        1 samples, Peak.   213 us (1.0x), Avg.   213 us, Total. 212.5 us (0.0%)
[Performance] Application.Shutdown.CleanupMono                                                                               :        1 samples, Peak.  79.4 ms (1.0x), Avg.  79.4 ms, Total. 79.38 ms (0.1%)
[Performance] Application.Shutdown.CleanupStdConverters                                                                      :        1 samples, Peak.  7.13 us (1.0x), Avg.  7.13 us, Total. 7.125 us (0.0%)
[Performance] Application.Shutdown.UnloadAllPlatformSupportModuleNativeDlls                                                  :        1 samples, Peak.  1.29 us (1.0x), Avg.  1.29 us, Total. 1.292 us (0.0%)
[Performance] Application.Shutdown.UnloadAllPlatformSupportNativeLibraries                                                   :        1 samples, Peak.  97.4 us (1.0x), Avg.  97.4 us, Total. 97.37 us (0.0%)
[Performance] Application.Shutdown.CleanupAutoDocumentation                                                                  :        1 samples, Peak.   224 us (1.0x), Avg.   224 us, Total. 223.9 us (0.0%)
[Performance] Application.Shutdown.ShaderNameManagerDestroy                                                                  :        1 samples, Peak.  31.7 us (1.0x), Avg.  31.7 us, Total. 31.71 us (0.0%)
[Performance] Application.Shutdown.CleanupCacheServer                                                                        :        1 samples, Peak.   250 ns (1.0x), Avg.   250 ns, Total. 250.0 ns (0.0%)
[Performance] Application.Shutdown.Virtualization_Shutdown                                                                   :        1 samples, Peak.  1.46 us (1.0x), Avg.  1.46 us, Total. 1.458 us (0.0%)
[Performance] Application.Shutdown.DevConnections                                                                            :        1 samples, Peak.  61.6 us (1.0x), Avg.  61.6 us, Total. 61.58 us (0.0%)
Exiting batchmode successfully now!
[Package Manager] Server::Kill -- Server was shutdown
