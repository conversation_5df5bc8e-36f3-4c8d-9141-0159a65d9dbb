# Unity开发环境 - curl安装命令参考

本文档提供了使用curl安装Unity开发环境所需工具的命令参考。

## 🚀 快速开始

### macOS
```bash
# 使用我们的curl安装脚本
chmod +x curl-install-environment.sh
./curl-install-environment.sh
```

### Windows (PowerShell)
```powershell
# 使用我们的curl安装脚本
.\curl-install-environment.ps1
```

## 📦 单独安装命令

### 1. Homebrew (macOS)
```bash
# 安装Homebrew包管理器
/bin/bash -c "$(curl -fsSL https://raw.githubusercontent.com/Homebrew/install/HEAD/install.sh)"
```

### 2. Node.js

#### macOS - 通过nvm
```bash
# 安装nvm (Node Version Manager)
curl -o- https://raw.githubusercontent.com/nvm-sh/nvm/v0.39.0/install.sh | bash

# 重新加载shell配置
source ~/.bashrc  # 或 source ~/.zshrc

# 安装最新LTS版本的Node.js
nvm install --lts
nvm use --lts
nvm alias default lts/*
```

#### Windows - 直接下载
```powershell
# 下载Node.js安装程序
curl.exe -L -o nodejs-installer.msi https://nodejs.org/dist/v18.17.0/node-v18.17.0-x64.msi

# 静默安装
msiexec /i nodejs-installer.msi /quiet
```

### 3. Unity Hub

#### macOS
```bash
# 下载Unity Hub DMG文件
curl -L -o /tmp/UnityHub.dmg https://public-cdn.cloud.unity3d.com/hub/prod/UnityHub.dmg

# 挂载并安装
hdiutil attach /tmp/UnityHub.dmg -quiet
cp -R "/Volumes/Unity Hub/Unity Hub.app" "/Applications/"
hdiutil detach "/Volumes/Unity Hub" -quiet
rm /tmp/UnityHub.dmg
```

#### Windows
```powershell
# 下载Unity Hub安装程序
curl.exe -L -o UnityHubSetup.exe https://public-cdn.cloud.unity3d.com/hub/prod/UnityHubSetup.exe

# 静默安装
Start-Process -FilePath UnityHubSetup.exe -ArgumentList "/S" -Wait
```

### 4. Git

#### macOS
```bash
# Git通常通过Xcode Command Line Tools安装
xcode-select --install

# 或通过Homebrew
brew install git
```

#### Windows
```powershell
# 下载Git安装程序
curl.exe -L -o Git-installer.exe https://github.com/git-for-windows/git/releases/latest/download/Git-2.42.0-64-bit.exe

# 静默安装
Start-Process -FilePath Git-installer.exe -ArgumentList "/VERYSILENT", "/NORESTART" -Wait
```

### 5. Python

#### macOS - 通过pyenv
```bash
# 安装pyenv
curl https://pyenv.run | bash

# 添加到shell配置
echo 'export PYENV_ROOT="$HOME/.pyenv"' >> ~/.zshrc
echo 'command -v pyenv >/dev/null || export PATH="$PYENV_ROOT/bin:$PATH"' >> ~/.zshrc
echo 'eval "$(pyenv init -)"' >> ~/.zshrc

# 重新加载配置
source ~/.zshrc

# 安装Python
pyenv install 3.11.5
pyenv global 3.11.5
```

#### Windows
```powershell
# 下载Python安装程序
curl.exe -L -o python-installer.exe https://www.python.org/ftp/python/3.11.5/python-3.11.5-amd64.exe

# 静默安装
Start-Process -FilePath python-installer.exe -ArgumentList "/quiet", "InstallAllUsers=1", "PrependPath=1" -Wait
```

### 6. Rust

#### macOS/Linux
```bash
# 安装Rust
curl --proto '=https' --tlsv1.2 -sSf https://sh.rustup.rs | sh -s -- -y

# 加载Cargo环境
source ~/.cargo/env
```

#### Windows
```powershell
# 下载rustup安装程序
curl.exe -L -o rustup-init.exe https://win.rustup.rs/x86_64

# 安装Rust
Start-Process -FilePath rustup-init.exe -ArgumentList "-y" -Wait
```

### 7. Visual Studio Code

#### macOS
```bash
# 下载VS Code (根据架构选择)
if [[ $(uname -m) == "arm64" ]]; then
    curl -L -o /tmp/VSCode.zip "https://code.visualstudio.com/sha/download?build=stable&os=darwin-arm64"
else
    curl -L -o /tmp/VSCode.zip "https://code.visualstudio.com/sha/download?build=stable&os=darwin"
fi

# 解压并安装
unzip -q /tmp/VSCode.zip -d /Applications/
rm /tmp/VSCode.zip
```

#### Windows
```powershell
# 下载VS Code安装程序
curl.exe -L -o VSCodeSetup.exe "https://code.visualstudio.com/sha/download?build=stable&os=win32-x64-user"

# 静默安装
Start-Process -FilePath VSCodeSetup.exe -ArgumentList "/VERYSILENT", "/NORESTART", "/MERGETASKS=!runcode" -Wait
```

### 8. Docker Desktop

#### macOS
```bash
# 根据架构下载Docker Desktop
if [[ $(uname -m) == "arm64" ]]; then
    curl -L -o /tmp/Docker.dmg https://desktop.docker.com/mac/main/arm64/Docker.dmg
else
    curl -L -o /tmp/Docker.dmg https://desktop.docker.com/mac/main/amd64/Docker.dmg
fi

# 挂载并安装
hdiutil attach /tmp/Docker.dmg -quiet
cp -R "/Volumes/Docker/Docker.app" "/Applications/"
hdiutil detach "/Volumes/Docker" -quiet
rm /tmp/Docker.dmg
```

#### Windows
```powershell
# 下载Docker Desktop安装程序
curl.exe -L -o DockerDesktopInstaller.exe "https://desktop.docker.com/win/main/amd64/Docker%20Desktop%20Installer.exe"

# 安装Docker Desktop
Start-Process -FilePath DockerDesktopInstaller.exe -ArgumentList "install", "--quiet" -Wait
```

### 9. Oh My Zsh (macOS/Linux)
```bash
# 安装Oh My Zsh
sh -c "$(curl -fsSL https://raw.github.com/ohmyzsh/ohmyzsh/master/tools/install.sh)" "" --unattended
```

## 🔧 curl常用参数说明

- `-L` 或 `--location`: 跟随重定向
- `-o <file>`: 将输出写入文件
- `-O`: 使用远程文件名保存
- `-s` 或 `--silent`: 静默模式，不显示进度
- `-S` 或 `--show-error`: 在静默模式下显示错误
- `-f` 或 `--fail`: 在HTTP错误时失败
- `--proto '=https'`: 只允许HTTPS协议
- `--tlsv1.2`: 使用TLS 1.2或更高版本

## 📝 使用提示

1. **权限**: 某些安装可能需要管理员权限
2. **网络**: 确保网络连接稳定
3. **防火墙**: 某些企业防火墙可能阻止下载
4. **验证**: 安装后验证工具是否正确安装
5. **环境变量**: 重启终端或重新加载配置文件

## 🔍 验证安装

```bash
# 验证各工具是否安装成功
node --version
npm --version
git --version
python --version
rustc --version
code --version
docker --version
```

## 🚨 故障排除

如果curl下载失败，可以尝试：
1. 检查网络连接
2. 使用代理: `curl --proxy http://proxy:port`
3. 忽略SSL证书: `curl -k` (不推荐)
4. 增加超时时间: `curl --connect-timeout 30`
5. 重试: `curl --retry 3`

## 📚 相关文档

- [Unity Hub CLI文档](https://docs.unity3d.com/hub/manual/HubCLI.html)
- [Node.js下载页面](https://nodejs.org/en/download/)
- [Git官方文档](https://git-scm.com/doc)
- [curl官方文档](https://curl.se/docs/)
