# Unity Development Environment Setup using curl for Windows
# Run this script in PowerShell

param(
    [string]$UnityVersion = "2022.3.8f1",
    [string]$NodeVersion = "18.17.0"
)

# Colors for output
$Red = [System.ConsoleColor]::Red
$Green = [System.ConsoleColor]::Green
$Yellow = [System.ConsoleColor]::Yellow
$Blue = [System.ConsoleColor]::Blue

function Write-ColorOutput($ForegroundColor) {
    $fc = $host.UI.RawUI.ForegroundColor
    $host.UI.RawUI.ForegroundColor = $ForegroundColor
    if ($args) {
        Write-Output $args
    } else {
        $input | Write-Output
    }
    $host.UI.RawUI.ForegroundColor = $fc
}

function Test-CommandExists($Command) {
    return Get-Command $Command -ErrorAction SilentlyContinue
}

function Install-NodeJS-Curl {
    Write-ColorOutput $Blue "Installing Node.js via curl..."
    
    if (Test-CommandExists node) {
        Write-ColorOutput $Green "Node.js is already installed."
        return
    }
    
    # Download Node.js installer
    $nodeUrl = "https://nodejs.org/dist/v$NodeVersion/node-v$NodeVersion-x64.msi"
    $nodeInstaller = "$env:TEMP\nodejs-installer.msi"
    
    Write-ColorOutput $Blue "Downloading Node.js via curl..."
    curl.exe -L -o $nodeInstaller $nodeUrl
    
    # Install Node.js
    Write-ColorOutput $Blue "Installing Node.js..."
    Start-Process -FilePath "msiexec.exe" -ArgumentList "/i", $nodeInstaller, "/quiet" -Wait
    
    # Clean up
    Remove-Item $nodeInstaller -Force
    
    # Refresh environment variables
    $env:Path = [System.Environment]::GetEnvironmentVariable("Path","Machine") + ";" + [System.Environment]::GetEnvironmentVariable("Path","User")
    
    Write-ColorOutput $Green "Node.js installed successfully via curl!"
}

function Install-UnityHub-Curl {
    Write-ColorOutput $Blue "Installing Unity Hub via curl..."
    
    $hubPath = "${env:ProgramFiles}\Unity Hub\Unity Hub.exe"
    if (Test-Path $hubPath) {
        Write-ColorOutput $Green "Unity Hub is already installed."
        return
    }
    
    # Download Unity Hub
    $hubUrl = "https://public-cdn.cloud.unity3d.com/hub/prod/UnityHubSetup.exe"
    $hubInstaller = "$env:TEMP\UnityHubSetup.exe"
    
    Write-ColorOutput $Blue "Downloading Unity Hub via curl..."
    curl.exe -L -o $hubInstaller $hubUrl
    
    # Install Unity Hub
    Write-ColorOutput $Blue "Installing Unity Hub..."
    Start-Process -FilePath $hubInstaller -ArgumentList "/S" -Wait
    
    # Clean up
    Remove-Item $hubInstaller -Force
    
    Write-ColorOutput $Green "Unity Hub installed successfully via curl!"
}

function Install-Git-Curl {
    Write-ColorOutput $Blue "Installing Git via curl..."
    
    if (Test-CommandExists git) {
        Write-ColorOutput $Green "Git is already installed."
        return
    }
    
    # Download Git installer
    $gitUrl = "https://github.com/git-for-windows/git/releases/latest/download/Git-2.42.0-64-bit.exe"
    $gitInstaller = "$env:TEMP\Git-installer.exe"
    
    Write-ColorOutput $Blue "Downloading Git via curl..."
    curl.exe -L -o $gitInstaller $gitUrl
    
    # Install Git silently
    Write-ColorOutput $Blue "Installing Git..."
    Start-Process -FilePath $gitInstaller -ArgumentList "/VERYSILENT", "/NORESTART" -Wait
    
    # Clean up
    Remove-Item $gitInstaller -Force
    
    # Refresh environment variables
    $env:Path = [System.Environment]::GetEnvironmentVariable("Path","Machine") + ";" + [System.Environment]::GetEnvironmentVariable("Path","User")
    
    Write-ColorOutput $Green "Git installed successfully via curl!"
}

function Install-Python-Curl {
    Write-ColorOutput $Blue "Installing Python via curl..."
    
    if (Test-CommandExists python) {
        Write-ColorOutput $Green "Python is already installed."
        return
    }
    
    # Download Python installer
    $pythonUrl = "https://www.python.org/ftp/python/3.11.5/python-3.11.5-amd64.exe"
    $pythonInstaller = "$env:TEMP\python-installer.exe"
    
    Write-ColorOutput $Blue "Downloading Python via curl..."
    curl.exe -L -o $pythonInstaller $pythonUrl
    
    # Install Python
    Write-ColorOutput $Blue "Installing Python..."
    Start-Process -FilePath $pythonInstaller -ArgumentList "/quiet", "InstallAllUsers=1", "PrependPath=1" -Wait
    
    # Clean up
    Remove-Item $pythonInstaller -Force
    
    # Refresh environment variables
    $env:Path = [System.Environment]::GetEnvironmentVariable("Path","Machine") + ";" + [System.Environment]::GetEnvironmentVariable("Path","User")
    
    Write-ColorOutput $Green "Python installed successfully via curl!"
}

function Install-VSCode-Curl {
    Write-ColorOutput $Blue "Installing VS Code via curl..."
    
    $vscodePath = "${env:ProgramFiles}\Microsoft VS Code\Code.exe"
    if (Test-Path $vscodePath) {
        Write-ColorOutput $Green "VS Code is already installed."
        return
    }
    
    # Download VS Code installer
    $vscodeUrl = "https://code.visualstudio.com/sha/download?build=stable&os=win32-x64-user"
    $vscodeInstaller = "$env:TEMP\VSCodeSetup.exe"
    
    Write-ColorOutput $Blue "Downloading VS Code via curl..."
    curl.exe -L -o $vscodeInstaller $vscodeUrl
    
    # Install VS Code
    Write-ColorOutput $Blue "Installing VS Code..."
    Start-Process -FilePath $vscodeInstaller -ArgumentList "/VERYSILENT", "/NORESTART", "/MERGETASKS=!runcode" -Wait
    
    # Clean up
    Remove-Item $vscodeInstaller -Force
    
    Write-ColorOutput $Green "VS Code installed successfully via curl!"
}

function Install-Docker-Curl {
    Write-ColorOutput $Blue "Installing Docker Desktop via curl..."
    
    if (Test-CommandExists docker) {
        Write-ColorOutput $Green "Docker is already installed."
        return
    }
    
    # Download Docker Desktop installer
    $dockerUrl = "https://desktop.docker.com/win/main/amd64/Docker%20Desktop%20Installer.exe"
    $dockerInstaller = "$env:TEMP\DockerDesktopInstaller.exe"
    
    Write-ColorOutput $Blue "Downloading Docker Desktop via curl..."
    curl.exe -L -o $dockerInstaller $dockerUrl
    
    # Install Docker Desktop
    Write-ColorOutput $Blue "Installing Docker Desktop..."
    Start-Process -FilePath $dockerInstaller -ArgumentList "install", "--quiet" -Wait
    
    # Clean up
    Remove-Item $dockerInstaller -Force
    
    Write-ColorOutput $Green "Docker Desktop installed successfully via curl!"
    Write-ColorOutput $Yellow "Please restart your computer to complete Docker installation."
}

function Install-Rust-Curl {
    Write-ColorOutput $Blue "Installing Rust via curl..."
    
    if (Test-CommandExists rustc) {
        Write-ColorOutput $Green "Rust is already installed."
        return
    }
    
    # Download rustup installer
    $rustupUrl = "https://win.rustup.rs/x86_64"
    $rustupInstaller = "$env:TEMP\rustup-init.exe"
    
    Write-ColorOutput $Blue "Downloading Rust installer via curl..."
    curl.exe -L -o $rustupInstaller $rustupUrl
    
    # Install Rust
    Write-ColorOutput $Blue "Installing Rust..."
    Start-Process -FilePath $rustupInstaller -ArgumentList "-y" -Wait
    
    # Clean up
    Remove-Item $rustupInstaller -Force
    
    # Add Cargo to PATH
    $cargoPath = "$env:USERPROFILE\.cargo\bin"
    $currentPath = [Environment]::GetEnvironmentVariable("Path", "User")
    if ($currentPath -notlike "*$cargoPath*") {
        [Environment]::SetEnvironmentVariable("Path", "$currentPath;$cargoPath", "User")
    }
    
    Write-ColorOutput $Green "Rust installed successfully via curl!"
}

function Show-InstallationSummary {
    Write-ColorOutput $Blue "`n========================================="
    Write-ColorOutput $Blue "    CURL INSTALLATION SUMMARY"
    Write-ColorOutput $Blue "========================================="
    
    Write-ColorOutput $Green "`nInstalled via curl:"
    
    if (Test-CommandExists node) {
        Write-ColorOutput $Green "✓ Node.js"
    }
    
    if (Test-Path "${env:ProgramFiles}\Unity Hub\Unity Hub.exe") {
        Write-ColorOutput $Green "✓ Unity Hub"
    }
    
    if (Test-CommandExists git) {
        Write-ColorOutput $Green "✓ Git"
    }
    
    if (Test-CommandExists python) {
        Write-ColorOutput $Green "✓ Python"
    }
    
    if (Test-Path "${env:ProgramFiles}\Microsoft VS Code\Code.exe") {
        Write-ColorOutput $Green "✓ Visual Studio Code"
    }
    
    if (Test-CommandExists docker) {
        Write-ColorOutput $Green "✓ Docker Desktop"
    }
    
    if (Test-CommandExists rustc) {
        Write-ColorOutput $Green "✓ Rust"
    }
    
    Write-ColorOutput $Yellow "`nNext Steps:"
    Write-ColorOutput $Yellow "1. Restart your computer to load all environment variables"
    Write-ColorOutput $Yellow "2. Open Unity Hub and sign in"
    Write-ColorOutput $Yellow "3. Install Unity Editor version $UnityVersion"
    Write-ColorOutput $Yellow "4. Start Docker Desktop if installed"
    
    Write-ColorOutput $Blue "`n========================================="
}

# Main installation process
function Main {
    Write-ColorOutput $Blue "========================================="
    Write-ColorOutput $Blue "  Unity Environment Setup via curl"
    Write-ColorOutput $Blue "========================================="
    
    Write-ColorOutput $Green "Starting curl-based environment setup for Windows..."
    Write-ColorOutput $Yellow "This will install development tools using curl."
    
    $confirmation = Read-Host "`nDo you want to continue? (y/n)"
    if ($confirmation -ne 'y' -and $confirmation -ne 'Y') {
        Write-ColorOutput $Yellow "Installation cancelled."
        exit 0
    }
    
    try {
        # Install components using curl
        Install-NodeJS-Curl
        Install-UnityHub-Curl
        Install-Git-Curl
        Install-Python-Curl
        Install-VSCode-Curl
        Install-Docker-Curl
        Install-Rust-Curl
        
        Show-InstallationSummary
        
        Write-ColorOutput $Green "`nCurl-based installation completed successfully!"
        Write-ColorOutput $Yellow "Please restart your computer to ensure all environment variables are loaded."
        
    } catch {
        Write-ColorOutput $Red "An error occurred during installation: $($_.Exception.Message)"
        Write-ColorOutput $Yellow "Please check the error and try again."
        exit 1
    }
}

# Run main function
Main
