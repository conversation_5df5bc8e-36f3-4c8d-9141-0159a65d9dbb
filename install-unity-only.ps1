# Unity开发环境快速安装脚本 (仅Unity) - Windows版本
# 使用curl下载和安装Unity Hub和Unity Editor

param(
    [string]$UnityVersion = "2022.3.8f1"
)

# 颜色定义
$Red = [System.ConsoleColor]::Red
$Green = [System.ConsoleColor]::Green
$Yellow = [System.ConsoleColor]::Yellow
$Blue = [System.ConsoleColor]::Blue

function Write-ColorOutput($ForegroundColor) {
    $fc = $host.UI.RawUI.ForegroundColor
    $host.UI.RawUI.ForegroundColor = $ForegroundColor
    if ($args) {
        Write-Output $args
    } else {
        $input | Write-Output
    }
    $host.UI.RawUI.ForegroundColor = $fc
}

function Test-Administrator {
    $currentUser = [Security.Principal.WindowsIdentity]::GetCurrent()
    $principal = New-Object Security.Principal.WindowsPrincipal($currentUser)
    return $principal.IsInRole([Security.Principal.WindowsBuiltInRole]::Administrator)
}

function Install-UnityHub {
    Write-ColorOutput $Blue "正在安装Unity Hub..."
    
    $hubPath = "${env:ProgramFiles}\Unity Hub\Unity Hub.exe"
    if (Test-Path $hubPath) {
        Write-ColorOutput $Green "Unity Hub已经安装。"
        return $hubPath
    }
    
    # 下载Unity Hub
    $hubUrl = "https://public-cdn.cloud.unity3d.com/hub/prod/UnityHubSetup.exe"
    $hubInstaller = "$env:TEMP\UnityHubSetup.exe"
    
    Write-ColorOutput $Blue "正在通过curl下载Unity Hub..."
    try {
        curl.exe -L -o $hubInstaller $hubUrl
        Write-ColorOutput $Green "Unity Hub下载完成"
    }
    catch {
        Write-ColorOutput $Red "Unity Hub下载失败: $($_.Exception.Message)"
        return $null
    }
    
    # 安装Unity Hub
    Write-ColorOutput $Blue "正在安装Unity Hub..."
    try {
        Start-Process -FilePath $hubInstaller -ArgumentList "/S" -Wait
        Write-ColorOutput $Green "Unity Hub安装成功！"
    }
    catch {
        Write-ColorOutput $Red "Unity Hub安装失败: $($_.Exception.Message)"
        return $null
    }
    finally {
        # 清理安装文件
        if (Test-Path $hubInstaller) {
            Remove-Item $hubInstaller -Force
        }
    }
    
    return $hubPath
}

function Install-UnityEditor {
    param($HubPath, $Version)
    
    Write-ColorOutput $Blue "正在安装Unity Editor $Version..."
    
    # 检查Unity版本是否已安装
    $unityPath = "${env:ProgramFiles}\Unity\Hub\Editor\$Version\Editor\Unity.exe"
    if (Test-Path $unityPath) {
        Write-ColorOutput $Green "Unity $Version 已经安装。"
        return $unityPath
    }
    
    # 使用Unity Hub CLI安装Unity Editor
    $hubCliPath = "${env:ProgramFiles}\Unity Hub\Unity Hub.exe"
    
    if (Test-Path $hubCliPath) {
        Write-ColorOutput $Blue "正在通过Unity Hub CLI安装Unity $Version..."
        Write-ColorOutput $Yellow "这可能需要几分钟时间，请耐心等待..."
        
        # 安装Unity及常用模块
        $modules = @(
            "android",           # Android Build Support
            "ios",              # iOS Build Support  
            "windows-il2cpp",   # Windows Build Support (IL2CPP)
            "mac-mono",         # Mac Build Support (Mono)
            "linux-il2cpp",     # Linux Build Support (IL2CPP)
            "webgl"             # WebGL Build Support
        )
        
        $moduleArgs = $modules -join ","
        
        try {
            # 使用Unity Hub CLI安装
            Start-Process -FilePath $hubCliPath -ArgumentList "-- --headless install --version $Version --module $moduleArgs" -Wait
            Write-ColorOutput $Green "Unity $Version 安装成功！"
            return $unityPath
        }
        catch {
            Write-ColorOutput $Yellow "Unity CLI安装失败，请手动通过Unity Hub安装"
            Write-ColorOutput $Yellow "1. 打开Unity Hub"
            Write-ColorOutput $Yellow "2. 转到'安装'标签页"
            Write-ColorOutput $Yellow "3. 点击'安装编辑器'"
            Write-ColorOutput $Yellow "4. 选择版本 $Version"
            Write-ColorOutput $Yellow "5. 添加模块：Android, iOS, Windows, Mac, Linux, WebGL"
            return $null
        }
    } else {
        Write-ColorOutput $Red "Unity Hub CLI未找到，请确保Unity Hub已正确安装"
        return $null
    }
}

function Test-Installation {
    Write-ColorOutput $Blue "正在验证安装..."
    
    $success = $true
    
    # 检查Unity Hub
    $hubPath = "${env:ProgramFiles}\Unity Hub\Unity Hub.exe"
    if (Test-Path $hubPath) {
        Write-ColorOutput $Green "✓ Unity Hub"
    } else {
        Write-ColorOutput $Red "✗ Unity Hub"
        $success = $false
    }
    
    # 检查Unity Editor
    $unityPath = "${env:ProgramFiles}\Unity\Hub\Editor\$UnityVersion\Editor\Unity.exe"
    if (Test-Path $unityPath) {
        Write-ColorOutput $Green "✓ Unity Editor $UnityVersion"
    } else {
        Write-ColorOutput $Yellow "⚠ Unity Editor $UnityVersion (可能需要手动安装)"
    }
    
    if ($success) {
        Write-ColorOutput $Green "Unity开发环境安装完成！"
    } else {
        Write-ColorOutput $Yellow "安装过程中遇到一些问题，请检查上述输出"
    }
    
    return $success
}

function Show-UsageInfo {
    Write-ColorOutput $Blue "`n========================================="
    Write-ColorOutput $Blue "    使用说明"
    Write-ColorOutput $Blue "========================================="
    
    Write-ColorOutput $Green "下一步操作："
    Write-ColorOutput $Yellow "1. 打开Unity Hub"
    Write-ColorOutput $Yellow "2. 登录您的Unity账户"
    Write-ColorOutput $Yellow "3. 如果Unity Editor未自动安装，请手动安装版本 $UnityVersion"
    Write-ColorOutput $Yellow "4. 打开您的Unity项目"
    
    Write-ColorOutput $Green "`n项目相关："
    Write-ColorOutput $Yellow "• 项目路径：$(Get-Location)"
    Write-ColorOutput $Yellow "• Unity版本：$UnityVersion"
    Write-ColorOutput $Yellow "• 支持平台：iOS, Android, Windows, Mac, Linux, WebGL"
    
    Write-ColorOutput $Blue "`n========================================="
}

# 主函数
function Main {
    Write-ColorOutput $Blue "========================================="
    Write-ColorOutput $Blue "  Unity开发环境快速安装 (仅Unity)"
    Write-ColorOutput $Blue "========================================="
    
    # 检查管理员权限
    if (!(Test-Administrator)) {
        Write-ColorOutput $Red "此脚本需要管理员权限运行！"
        Write-ColorOutput $Yellow "请右键点击PowerShell并选择'以管理员身份运行'"
        exit 1
    }
    
    Write-ColorOutput $Green "开始安装Unity开发环境..."
    Write-ColorOutput $Yellow "此脚本将安装Unity Hub和Unity Editor $UnityVersion"
    
    # 确认安装
    $confirmation = Read-Host "`n是否继续安装？(y/n)"
    if ($confirmation -ne 'y' -and $confirmation -ne 'Y') {
        Write-ColorOutput $Yellow "安装已取消。"
        exit 0
    }
    
    try {
        # 执行安装步骤
        $hubPath = Install-UnityHub
        if ($hubPath) {
            $unityPath = Install-UnityEditor -HubPath $hubPath -Version $UnityVersion
            $installSuccess = Test-Installation
            Show-UsageInfo
            
            if ($installSuccess) {
                Write-ColorOutput $Green "`n安装脚本执行完成！"
            } else {
                Write-ColorOutput $Yellow "`n安装过程中遇到一些问题，请查看上述信息"
            }
        } else {
            Write-ColorOutput $Red "Unity Hub安装失败，无法继续"
            exit 1
        }
        
    } catch {
        Write-ColorOutput $Red "安装过程中发生错误: $($_.Exception.Message)"
        Write-ColorOutput $Yellow "请检查错误信息并重试"
        exit 1
    }
}

# 运行主函数
Main
