using System.Collections;
using System.Collections.Generic;
using System.Runtime.InteropServices;
using UnityEngine;
// ����eID = 1520��ʾ���𶯣�Ϊ1519Ϊ����
public class MobileVibration
{
#if UNITY_IOS
    [DllImport("__Internal")]
    private static extern void EX_C_CallVibrateE(int eID);
#endif

    public static void CallVibrate(int eID)
    {
#if UNITY_EDITOR

#elif UNITY_ANDROID
        long miSec = 30;
        if(eID == 1520)
        {
            miSec = 60;
        }
        //ͨ��������ȡjava class
        AndroidJavaClass jc = new AndroidJavaClass("com.unity3d.player.UnityPlayer");
        //��ȡ��ǰ��activity
        var activity = jc.GetStatic<AndroidJavaObject>("currentActivity");
        var service = new AndroidJavaClass("android.app.Service");
        var s = service.GetStatic<string>("VIBRATOR_SERVICE");
        var vib = activity.Call<AndroidJavaObject>("getSystemService", s);
        vib.Call("vibrate", miSec);

#elif UNITY_IOS
        EX_C_CallVibrateE(eID);
#endif
    }

}
