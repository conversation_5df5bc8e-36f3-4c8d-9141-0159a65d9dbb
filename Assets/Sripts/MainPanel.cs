using Fungus;
using System;
using System.Collections;
using System.Collections.Generic;
using TMPro;
using UnityEngine;
using UnityEngine.SceneManagement;
using UnityEngine.UI;

public class MainPanel : MonoSingleton<MainPanel>
{


    private Button task_Button;
    private Button illustratedHandbook_Button;
    private Button accompany_Button;
    private Button tarotCard_Button;
    private Button personalHomepage_Button;
    private Button worldView_Button;
    private Button battle_Button;
    private Button gold_Button;
    private Button pantheon_Button;
    private Button gringotts_Button;
    private Button sache_Button;
    private Button level_Button;
    private Button drawcards_Button;
    private Button new_pantheon_Button;





    private TextMeshProUGUI gold_Text;
    private TextMeshProUGUI diamond_Text;

    private GameObject storyPanel;
    private GameObject back_Button;
    private GameObject personalHomepagePanel;
    private GameObject goldPanel;
    private GameObject worldViewPanel;
    private GameObject illustratedHandbookPanel;
    private GameObject accompanyPanel;
    private GameObject pantheonPanel;
    private GameObject gringottsPanel;
    private GameObject battlePanel;
    private GameObject sachetPanel;
    private GameObject UIChild;
    private GameObject dailyfortunePanel;

    private GameObject newpantheonPanel;
    private GameObject drawcardsPanel;




    // Start is called before the first frame update
    void Start()
    {

        gold_Text = transform.Find("UIChild/gold/gold_Text").GetComponent<TextMeshProUGUI>();
        diamond_Text = transform.Find("UIChild/diamond/diamond_Text").GetComponent<TextMeshProUGUI>();

        task_Button = transform.Find("UIChild/task_Button").GetComponent<Button>();
        task_Button.onClick.AddListener(TaskOpen);

        illustratedHandbook_Button = transform.Find("UIChild/illustratedHandbook_Button").GetComponent<Button>();
        illustratedHandbook_Button.onClick.AddListener(IllustratedHandbookOpen);

        accompany_Button = transform.Find("UIChild/accompany_Button").GetComponent<Button>();
        accompany_Button.onClick.AddListener(AccompanyOpen);

        tarotCard_Button = transform.Find("UIChild/tarotCard_Button").GetComponent<Button>();
        tarotCard_Button.onClick.AddListener(TarotCardOpen);

        personalHomepage_Button = transform.Find("UIChild/personalHomepage_Button").GetComponent<Button>();
        personalHomepage_Button.onClick.AddListener(PersonalHomepageOpen);

        worldView_Button = transform.Find("UIChild/worldView_Button").GetComponent<Button>();
        worldView_Button.onClick.AddListener(WorldViewOpen);

        battle_Button = transform.Find("UIChild/battle_Button").GetComponent<Button>();
        battle_Button.onClick.AddListener(BattleOpen);

        gold_Button = transform.Find("UIChild/diamond/diamond_Button").GetComponent<Button>();
        gold_Button.onClick.AddListener(GoldOpen);

        pantheon_Button = transform.Find("UIChild/pantheon_Button").GetComponent<Button>();
        pantheon_Button.onClick.AddListener(PantheonOpen);

        gringotts_Button = transform.Find("UIChild/gringotts_Button").GetComponent<Button>();
        gringotts_Button.onClick.AddListener(GringottsOpen);

        sache_Button = transform.Find("UIChild/sache_Button").GetComponent<Button>();
        sache_Button.onClick.AddListener(SacheOpen);

        level_Button = transform.Find("UIChild/level_Button").GetComponent<Button>();
        level_Button.onClick.AddListener(LevelOpen);



        drawcards_Button = transform.Find("UIChild/drawcards_Button").GetComponent<Button>();
        drawcards_Button.onClick.AddListener(DrawcardsOprn);



        new_pantheon_Button = transform.Find("UIChild/new_pantheon_Button").GetComponent<Button>();
        new_pantheon_Button.onClick.AddListener(NewPersonalHomepageOpen);





        storyPanel = transform.Find("UIChild/storyPanel").gameObject;
        personalHomepagePanel = transform.Find("UIChild/personalHomepagePanel").gameObject;
        goldPanel = transform.Find("diamondPanel").gameObject;
        worldViewPanel = transform.Find("UIChild/worldViewPanel").gameObject;
        illustratedHandbookPanel = transform.Find("UIChild/illustratedHandbookPanel").gameObject;
        accompanyPanel = transform.Find("UIChild/accompanyPanel").gameObject;
        pantheonPanel = transform.Find("UIChild/pantheonPanel").gameObject;
        gringottsPanel = transform.Find("UIChild/gringottsPanel").gameObject;
        battlePanel = transform.Find("UIChild/battlePanel").gameObject;
        sachetPanel = transform.Find("UIChild/sachetPanel").gameObject;
        drawcardsPanel = transform.Find("UIChild/drawcardsPanel").gameObject;
        newpantheonPanel = transform.Find("UIChild/newpantheonPanel").gameObject;
        back_Button = transform.Find("back_Button").gameObject;
        UIChild = transform.Find("UIChild").gameObject;
        dailyfortunePanel = transform.Find("dailyfortunePanel").gameObject;
        back_Button.GetComponent<Button>().onClick.AddListener(Back);
        back_Button.SetActive(false);
        Init();



    }


    public void DrawcardsOprn()
    {
        UIBackManage.Instance.PushOnBack(() =>
        {
            drawcardsPanel.SetActive(false);
            back_Button.SetActive(false);
        });
        drawcardsPanel.SetActive(true);
        back_Button.SetActive(true);

    }
    public void NewPersonalHomepageOpen() {

        UIBackManage.Instance.PushOnBack(() =>
        {
            newpantheonPanel.SetActive(false);
            back_Button.SetActive(false);
        });
        newpantheonPanel.SetActive(true);
        back_Button.SetActive(true);
    }



    public void OpenDailyfortunePanel(){
          dailyfortunePanel.gameObject.SetActive(true);
        }


    private void LevelOpen()
    {

        SceneManager.LoadScene("Level");
    }

    private void SacheOpen()
    {

        UIBackManage.Instance.PushOnBack(() =>
        {
            sachetPanel.SetActive(false);
            back_Button.SetActive(false);
        });
        sachetPanel.SetActive(true);
        back_Button.SetActive(true);
    }

    private void GringottsOpen()
    {
        
         UIBackManage.Instance.PushOnBack(() =>
         {
             gringottsPanel.SetActive(false);
             back_Button.SetActive(false);
         });
             gringottsPanel.SetActive(true);
                back_Button.SetActive(true);
    }

    private void PantheonOpen()
    {
        UIBackManage.Instance.PushOnBack(() =>
        {
            pantheonPanel.SetActive(false);
            back_Button.SetActive(false);
        });
        pantheonPanel.SetActive(true);
        back_Button.SetActive(true);
    }


    public  void Init() {

       
        personalHomepagePanel.SetActive(false);
        storyPanel.SetActive(false);
        gold_Text.text = GameDataManager.Instance.GetGoldNumber().ToString();
        diamond_Text.text = GameDataManager.Instance.GetDiamondNumber().ToString();

    }

    public void Back() {

        UIBackManage.Instance.OnBack();

    }
    public void GoldOpen()
    {
        UIBackManage.Instance.PushOnBack(() =>
        {
            goldPanel.SetActive(false);
            back_Button.SetActive(false);
        });
        goldPanel.SetActive(true);
        back_Button.SetActive(true);
    }

    public void BattleOpen()
    {
        UIBackManage.Instance.PushOnBack(() =>
        {
            battlePanel.SetActive(false);
            back_Button.SetActive(false);
        });
        battlePanel.SetActive(true);
        battlePanel.GetComponent<BattlePanel>().Init("相宜", "推荐属性");
        back_Button.SetActive(true);
    }

    public void WorldViewOpen()
    {
        UIBackManage.Instance.PushOnBack(() =>
        {
            worldViewPanel.SetActive(false);
            back_Button.SetActive(false);
        });
        worldViewPanel.SetActive(true);
        back_Button.SetActive(true);
    }

    public void PersonalHomepageOpen()
    {
        UIBackManage.Instance.PushOnBack(() =>
        {
            personalHomepagePanel.SetActive(false);
            back_Button.SetActive(false);
        });
        personalHomepagePanel.SetActive(true);
        back_Button.SetActive(true);
    }

    public void TarotCardOpen()
    {
        SceneManager.LoadScene("Tarot");
    }

    public void AccompanyOpen()
    {
        UIBackManage.Instance.PushOnBack(() =>
        {
            accompanyPanel.SetActive(false);
            back_Button.SetActive(false);
        });
        accompanyPanel.SetActive(true);
        back_Button.SetActive(true);
    }

    public void TaskOpen()
    {
        UIBackManage.Instance.PushOnBack(() =>
        {
            storyPanel.SetActive(false);
            back_Button.SetActive(false);
        });
        storyPanel.SetActive(true);
        back_Button.SetActive(true);
    }
    public void IllustratedHandbookOpen()
    {
        UIBackManage.Instance.PushOnBack(() =>
        {
            illustratedHandbookPanel.SetActive(false);
            back_Button.SetActive(false);
        });
        illustratedHandbookPanel.SetActive(true);
        back_Button.SetActive(true);

    }

    // Update is called once per frame
    void Update()
    {
        
    }

    

}
