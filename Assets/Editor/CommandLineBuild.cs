using System;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using UnityEditor;
using UnityEditor.Build.Reporting;
using UnityEngine;

namespace BuildTools
{
    public static class CommandLineBuild
    {
        private static readonly Dictionary<string, BuildTarget> PlatformMap = new Dictionary<string, BuildTarget>
        {
            { "windows", BuildTarget.StandaloneWindows64 },
            { "win", BuildTarget.StandaloneWindows64 },
            { "mac", BuildTarget.StandaloneOSX },
            { "osx", BuildTarget.StandaloneOSX },
            { "linux", BuildTarget.StandaloneLinux64 },
            { "android", BuildTarget.Android },
            { "ios", BuildTarget.iOS }
        };

        /// <summary>
        /// Command line build method
        /// Usage: Unity -batchmode -quit -projectPath "path/to/project" -executeMethod BuildTools.CommandLineBuild.Build -platform windows -version 1.0.1
        /// </summary>
        public static void Build()
        {
            try
            {
                var args = ParseCommandLineArgs();
                
                string platform = GetArg(args, "-platform", "all");
                string version = GetArg(args, "-version", "");
                string buildPath = GetArg(args, "-buildPath", "");
                bool developmentBuild = HasArg(args, "-development");
                bool autoConnectProfiler = HasArg(args, "-autoConnectProfiler");
                
                Debug.Log($"Command Line Build Started - Platform: {platform}, Version: {version}");
                
                var builder = new MultiPlatformBuilder();
                
                // Update version if provided
                if (!string.IsNullOrEmpty(version))
                {
                    UpdateVersionInfo(version);
                }
                
                // Build specific platform or all platforms
                if (platform.ToLower() == "all")
                {
                    BuildAllPlatforms(developmentBuild, autoConnectProfiler, buildPath);
                }
                else
                {
                    BuildSinglePlatform(platform, developmentBuild, autoConnectProfiler, buildPath);
                }
                
                Debug.Log("Command Line Build Completed Successfully!");
            }
            catch (Exception e)
            {
                Debug.LogError($"Command Line Build Failed: {e.Message}");
                EditorApplication.Exit(1);
            }
        }

        /// <summary>
        /// Build all platforms from command line
        /// </summary>
        public static void BuildAll()
        {
            try
            {
                var args = ParseCommandLineArgs();
                string version = GetArg(args, "-version", "");
                bool developmentBuild = HasArg(args, "-development");
                
                if (!string.IsNullOrEmpty(version))
                {
                    UpdateVersionInfo(version);
                }
                
                BuildAllPlatforms(developmentBuild, false, "");
                Debug.Log("All Platforms Build Completed Successfully!");
            }
            catch (Exception e)
            {
                Debug.LogError($"Build All Failed: {e.Message}");
                EditorApplication.Exit(1);
            }
        }

        /// <summary>
        /// Build specific platform from command line
        /// </summary>
        public static void BuildWindows() => BuildPlatformFromCommandLine("windows");
        public static void BuildMac() => BuildPlatformFromCommandLine("mac");
        public static void BuildLinux() => BuildPlatformFromCommandLine("linux");
        public static void BuildAndroid() => BuildAndroidAPK();
        public static void BuildAndroidProject() => BuildAndroidGradleProject();
        public static void BuildiOS() => BuildPlatformFromCommandLine("ios");
        public static void BuildiOSIPA() => BuildiOSIPAFile();

        /// <summary>
        /// Build Android APK specifically
        /// </summary>
        public static void BuildAndroidAPK()
        {
            try
            {
                Debug.Log("Starting Android APK build...");

                var args = ParseCommandLineArgs();
                string version = GetArg(args, "-version", "1.0.0");
                bool developmentBuild = HasArg(args, "-development");

                // Force APK build settings
                EditorUserBuildSettings.exportAsGoogleAndroidProject = false;
                EditorUserBuildSettings.buildAppBundle = false;

                // Switch to Android platform if not already
                if (EditorUserBuildSettings.activeBuildTarget != BuildTarget.Android)
                {
                    Debug.Log("Switching to Android platform...");
                    EditorUserBuildSettings.SwitchActiveBuildTarget(BuildTargetGroup.Android, BuildTarget.Android);
                }

                // Update version
                if (!string.IsNullOrEmpty(version))
                {
                    UpdateVersionInfo(version);
                }

                // Build path
                string buildPath = Path.Combine(Application.dataPath, "../Builds/android");
                Directory.CreateDirectory(buildPath);
                string apkPath = Path.Combine(buildPath, "香域.apk");

                // Get scenes
                string[] scenes = EditorBuildSettings.scenes
                    .Where(scene => scene.enabled)
                    .Select(scene => scene.path)
                    .ToArray();

                if (scenes.Length == 0)
                {
                    throw new Exception("No scenes found in build settings!");
                }

                Debug.Log($"Building APK to: {apkPath}");
                Debug.Log($"Scenes: {string.Join(", ", scenes)}");

                // Build options
                BuildOptions options = BuildOptions.None;
                if (developmentBuild)
                {
                    options |= BuildOptions.Development;
                }

                BuildPlayerOptions buildPlayerOptions = new BuildPlayerOptions
                {
                    scenes = scenes,
                    locationPathName = apkPath,
                    target = BuildTarget.Android,
                    options = options
                };

                // Perform build
                BuildReport report = BuildPipeline.BuildPlayer(buildPlayerOptions);

                if (report.summary.result == UnityEditor.Build.Reporting.BuildResult.Succeeded)
                {
                    Debug.Log($"Android APK build succeeded!");
                    Debug.Log($"APK location: {apkPath}");
                    Debug.Log($"Build size: {report.summary.totalSize} bytes");
                    Debug.Log($"Build time: {report.summary.totalTime}");

                    // Verify file exists
                    if (File.Exists(apkPath))
                    {
                        FileInfo apkInfo = new FileInfo(apkPath);
                        Debug.Log($"APK file confirmed: {apkInfo.Length} bytes");
                    }
                    else
                    {
                        Debug.LogWarning($"APK file not found at: {apkPath}");
                    }
                }
                else
                {
                    Debug.LogError($"Android APK build failed: {report.summary.result}");
                    foreach (var step in report.steps)
                    {
                        foreach (var message in step.messages)
                        {
                            if (message.type == LogType.Error || message.type == LogType.Exception)
                            {
                                Debug.LogError($"Build error: {message.content}");
                            }
                        }
                    }
                    EditorApplication.Exit(1);
                }
            }
            catch (Exception e)
            {
                Debug.LogError($"Android APK Build Failed: {e.Message}");
                Debug.LogError($"Stack trace: {e.StackTrace}");
                EditorApplication.Exit(1);
            }
        }

        /// <summary>
        /// Build Android Gradle Project for Android Studio
        /// </summary>
        public static void BuildAndroidGradleProject()
        {
            try
            {
                var args = ParseCommandLineArgs();
                string version = GetArg(args, "-version", "");
                bool developmentBuild = HasArg(args, "-development");

                // Force Gradle project export
                EditorUserBuildSettings.exportAsGoogleAndroidProject = true;
                EditorUserBuildSettings.buildAppBundle = false;

                // Set custom build location for Android project
                string buildPath = GetBuildLocationPath("android", BuildTarget.Android);
                string androidProjectPath = Path.Combine(buildPath, "AndroidProject");

                // Get scenes
                string[] scenes = EditorBuildSettings.scenes
                    .Where(scene => scene.enabled)
                    .Select(scene => scene.path)
                    .ToArray();

                var buildPlayerOptions = new BuildPlayerOptions
                {
                    scenes = scenes,
                    locationPathName = androidProjectPath,
                    target = BuildTarget.Android,
                    options = developmentBuild ? BuildOptions.Development : BuildOptions.None
                };

                var report = BuildPipeline.BuildPlayer(buildPlayerOptions);

                if (report.summary.result == BuildResult.Succeeded)
                {
                    Debug.Log($"Android Gradle Project exported to: {androidProjectPath}");
                    Debug.Log("You can now open this project in Android Studio to build APK");
                }
                else
                {
                    Debug.LogError("Android Gradle Project export failed!");
                    EditorApplication.Exit(1);
                }
            }
            catch (Exception e)
            {
                Debug.LogError($"Android Gradle Project Build Failed: {e.Message}");
                EditorApplication.Exit(1);
            }
        }

        /// <summary>
        /// Build iOS IPA file from existing Xcode project
        /// </summary>
        public static void BuildiOSIPAFile()
        {
            try
            {
                Debug.Log("Starting iOS IPA build...");

                var args = ParseCommandLineArgs();
                string version = GetArg(args, "-version", "1.0.0");
                bool developmentBuild = HasArg(args, "-development");

                // Check if iOS Xcode project exists
                string iosProjectPath = Path.Combine(Application.dataPath, "../Builds/ios");
                string xcodeProjectPath = Path.Combine(iosProjectPath, "Unity-iPhone.xcodeproj");

                if (!Directory.Exists(iosProjectPath) || !Directory.Exists(xcodeProjectPath))
                {
                    Debug.LogError("iOS Xcode project not found. Please build iOS project first.");
                    EditorApplication.Exit(1);
                    return;
                }

                Debug.Log($"Found iOS project at: {xcodeProjectPath}");

                // Create IPA output directory
                string ipaOutputPath = Path.Combine(Application.dataPath, "../Builds/ios-ipa");
                Directory.CreateDirectory(ipaOutputPath);
                string ipaFilePath = Path.Combine(ipaOutputPath, "香域.ipa");

                Debug.Log($"Building IPA to: {ipaFilePath}");

                // Use xcodebuild to create archive and export IPA
                string archivePath = Path.Combine(ipaOutputPath, "香域.xcarchive");

                // Build command for creating archive
                string archiveCommand = $"xcodebuild -project \"{xcodeProjectPath}\" -scheme Unity-iPhone -configuration Release -archivePath \"{archivePath}\" archive";

                Debug.Log($"Creating archive with command: {archiveCommand}");

                // Execute archive command
                var archiveProcess = new System.Diagnostics.Process();
                archiveProcess.StartInfo.FileName = "/bin/bash";
                archiveProcess.StartInfo.Arguments = $"-c \"{archiveCommand}\"";
                archiveProcess.StartInfo.UseShellExecute = false;
                archiveProcess.StartInfo.RedirectStandardOutput = true;
                archiveProcess.StartInfo.RedirectStandardError = true;
                archiveProcess.StartInfo.WorkingDirectory = iosProjectPath;

                archiveProcess.Start();
                string archiveOutput = archiveProcess.StandardOutput.ReadToEnd();
                string archiveError = archiveProcess.StandardError.ReadToEnd();
                archiveProcess.WaitForExit();

                Debug.Log($"Archive output: {archiveOutput}");
                if (!string.IsNullOrEmpty(archiveError))
                {
                    Debug.LogWarning($"Archive warnings/errors: {archiveError}");
                }

                if (archiveProcess.ExitCode != 0)
                {
                    Debug.LogError($"Archive creation failed with exit code: {archiveProcess.ExitCode}");
                    EditorApplication.Exit(1);
                    return;
                }

                // Create export options plist for development distribution
                string exportOptionsPlist = Path.Combine(ipaOutputPath, "ExportOptions.plist");
                string plistContent = @"<?xml version=""1.0"" encoding=""UTF-8""?>
<!DOCTYPE plist PUBLIC ""-//Apple//DTD PLIST 1.0//EN"" ""http://www.apple.com/DTDs/PropertyList-1.0.dtd"">
<plist version=""1.0"">
<dict>
    <key>method</key>
    <string>development</string>
    <key>teamID</key>
    <string></string>
    <key>compileBitcode</key>
    <false/>
    <key>stripSwiftSymbols</key>
    <true/>
    <key>thinning</key>
    <string>&lt;none&gt;</string>
</dict>
</plist>";

                File.WriteAllText(exportOptionsPlist, plistContent);
                Debug.Log($"Created export options plist: {exportOptionsPlist}");

                // Export IPA from archive
                string exportCommand = $"xcodebuild -exportArchive -archivePath \"{archivePath}\" -exportPath \"{ipaOutputPath}\" -exportOptionsPlist \"{exportOptionsPlist}\"";

                Debug.Log($"Exporting IPA with command: {exportCommand}");

                var exportProcess = new System.Diagnostics.Process();
                exportProcess.StartInfo.FileName = "/bin/bash";
                exportProcess.StartInfo.Arguments = $"-c \"{exportCommand}\"";
                exportProcess.StartInfo.UseShellExecute = false;
                exportProcess.StartInfo.RedirectStandardOutput = true;
                exportProcess.StartInfo.RedirectStandardError = true;
                exportProcess.StartInfo.WorkingDirectory = iosProjectPath;

                exportProcess.Start();
                string exportOutput = exportProcess.StandardOutput.ReadToEnd();
                string exportError = exportProcess.StandardError.ReadToEnd();
                exportProcess.WaitForExit();

                Debug.Log($"Export output: {exportOutput}");
                if (!string.IsNullOrEmpty(exportError))
                {
                    Debug.LogWarning($"Export warnings/errors: {exportError}");
                }

                if (exportProcess.ExitCode == 0)
                {
                    // Find the generated IPA file
                    string[] ipaFiles = Directory.GetFiles(ipaOutputPath, "*.ipa", SearchOption.AllDirectories);
                    if (ipaFiles.Length > 0)
                    {
                        string generatedIPA = ipaFiles[0];
                        FileInfo ipaInfo = new FileInfo(generatedIPA);
                        Debug.Log($"iOS IPA build succeeded!");
                        Debug.Log($"IPA location: {generatedIPA}");
                        Debug.Log($"IPA size: {ipaInfo.Length} bytes ({ipaInfo.Length / (1024 * 1024)} MB)");
                    }
                    else
                    {
                        Debug.LogWarning("IPA export completed but no IPA file found.");
                    }
                }
                else
                {
                    Debug.LogError($"IPA export failed with exit code: {exportProcess.ExitCode}");
                    EditorApplication.Exit(1);
                }
            }
            catch (Exception e)
            {
                Debug.LogError($"iOS IPA Build Failed: {e.Message}");
                Debug.LogError($"Stack trace: {e.StackTrace}");
                EditorApplication.Exit(1);
            }
        }

        private static void BuildPlatformFromCommandLine(string platform)
        {
            try
            {
                var args = ParseCommandLineArgs();
                string version = GetArg(args, "-version", "");
                bool developmentBuild = HasArg(args, "-development");
                
                if (!string.IsNullOrEmpty(version))
                {
                    UpdateVersionInfo(version);
                }
                
                BuildSinglePlatform(platform, developmentBuild, false, "");
                Debug.Log($"{platform} Build Completed Successfully!");
            }
            catch (Exception e)
            {
                Debug.LogError($"{platform} Build Failed: {e.Message}");
                EditorApplication.Exit(1);
            }
        }

        private static Dictionary<string, string> ParseCommandLineArgs()
        {
            var args = new Dictionary<string, string>();
            var commandLineArgs = Environment.GetCommandLineArgs();
            
            for (int i = 0; i < commandLineArgs.Length; i++)
            {
                if (commandLineArgs[i].StartsWith("-") && i + 1 < commandLineArgs.Length)
                {
                    args[commandLineArgs[i]] = commandLineArgs[i + 1];
                }
                else if (commandLineArgs[i].StartsWith("-"))
                {
                    args[commandLineArgs[i]] = "true";
                }
            }
            
            return args;
        }

        private static string GetArg(Dictionary<string, string> args, string key, string defaultValue)
        {
            return args.ContainsKey(key) ? args[key] : defaultValue;
        }

        private static bool HasArg(Dictionary<string, string> args, string key)
        {
            return args.ContainsKey(key);
        }

        private static void UpdateVersionInfo(string version)
        {
            try
            {
                string versionFilePath = Path.Combine(Application.dataPath, "../version.json");
                VersionInfo versionInfo;
                
                if (File.Exists(versionFilePath))
                {
                    string json = File.ReadAllText(versionFilePath);
                    versionInfo = JsonUtility.FromJson<VersionInfo>(json);
                }
                else
                {
                    versionInfo = new VersionInfo();
                }
                
                versionInfo.version = version;
                versionInfo.buildTime = DateTime.Now.ToString("yyyy-MM-dd HH:mm:ss");
                versionInfo.buildNumber++;
                
                string updatedJson = JsonUtility.ToJson(versionInfo, true);
                File.WriteAllText(versionFilePath, updatedJson);
                
                Debug.Log($"Version updated to: {version}");
            }
            catch (Exception e)
            {
                Debug.LogError($"Failed to update version info: {e.Message}");
            }
        }

        private static void BuildAllPlatforms(bool developmentBuild, bool autoConnectProfiler, string customBuildPath)
        {
            foreach (var kvp in PlatformMap)
            {
                BuildForTarget(kvp.Value, kvp.Key, developmentBuild, autoConnectProfiler, customBuildPath);
            }
        }

        private static void BuildSinglePlatform(string platform, bool developmentBuild, bool autoConnectProfiler, string customBuildPath)
        {
            if (PlatformMap.TryGetValue(platform.ToLower(), out BuildTarget target))
            {
                BuildForTarget(target, platform, developmentBuild, autoConnectProfiler, customBuildPath);
            }
            else
            {
                throw new ArgumentException($"Unknown platform: {platform}");
            }
        }

        private static void BuildForTarget(BuildTarget target, string platformName, bool developmentBuild, bool autoConnectProfiler, string customBuildPath)
        {
            try
            {
                Debug.Log($"Building for {platformName}...");
                
                // Determine build path
                string buildPath = string.IsNullOrEmpty(customBuildPath) 
                    ? Path.Combine(Application.dataPath, "../Builds", platformName)
                    : Path.Combine(customBuildPath, platformName);
                
                Directory.CreateDirectory(buildPath);
                
                // Get scenes
                string[] scenes = EditorBuildSettings.scenes
                    .Where(scene => scene.enabled)
                    .Select(scene => scene.path)
                    .ToArray();
                
                if (scenes.Length == 0)
                {
                    throw new Exception("No scenes found in build settings!");
                }
                
                // Configure platform-specific settings
                if (target == BuildTarget.Android)
                {
                    // Ensure we build APK, not Android project
                    EditorUserBuildSettings.exportAsGoogleAndroidProject = false;
                    EditorUserBuildSettings.buildAppBundle = false; // Build APK instead of AAB
                }

                // Set build options
                BuildOptions options = BuildOptions.None;
                if (developmentBuild)
                {
                    options |= BuildOptions.Development;
                }
                if (autoConnectProfiler)
                {
                    options |= BuildOptions.ConnectWithProfiler;
                }
                
                // Create build player options
                BuildPlayerOptions buildOptions = new BuildPlayerOptions
                {
                    scenes = scenes,
                    locationPathName = GetBuildLocationPath(buildPath, target),
                    target = target,
                    options = options
                };
                
                // Update player settings
                UpdatePlayerSettingsForBuild();
                
                // Perform build
                var report = BuildPipeline.BuildPlayer(buildOptions);
                
                if (report.summary.result == UnityEditor.Build.Reporting.BuildResult.Succeeded)
                {
                    Debug.Log($"{platformName} build succeeded: {buildOptions.locationPathName}");
                    Debug.Log($"Build size: {report.summary.totalSize} bytes");
                    Debug.Log($"Build time: {report.summary.totalTime}");
                }
                else
                {
                    throw new Exception($"{platformName} build failed with result: {report.summary.result}");
                }
            }
            catch (Exception e)
            {
                Debug.LogError($"Build failed for {platformName}: {e.Message}");
                throw;
            }
        }

        private static string GetBuildLocationPath(string buildPath, BuildTarget target)
        {
            string productName = PlayerSettings.productName;
            
            switch (target)
            {
                case BuildTarget.StandaloneWindows64:
                    return Path.Combine(buildPath, $"{productName}.exe");
                case BuildTarget.StandaloneOSX:
                    return Path.Combine(buildPath, $"{productName}.app");
                case BuildTarget.StandaloneLinux64:
                    return Path.Combine(buildPath, productName);
                case BuildTarget.Android:
                    return Path.Combine(buildPath, $"{productName}.apk");
                case BuildTarget.iOS:
                    return buildPath; // iOS builds to directory
                default:
                    return Path.Combine(buildPath, productName);
            }
        }

        private static void UpdatePlayerSettingsForBuild()
        {
            // Configure Android build settings to export APK instead of project
            EditorUserBuildSettings.exportAsGoogleAndroidProject = false;

            // Load version info
            string versionFilePath = Path.Combine(Application.dataPath, "../version.json");
            if (File.Exists(versionFilePath))
            {
                try
                {
                    string json = File.ReadAllText(versionFilePath);
                    var versionInfo = JsonUtility.FromJson<VersionInfo>(json);

                    PlayerSettings.bundleVersion = versionInfo.version;
                    PlayerSettings.Android.bundleVersionCode = versionInfo.buildNumber;
                    PlayerSettings.iOS.buildNumber = versionInfo.buildNumber.ToString();
                    PlayerSettings.macOS.buildNumber = versionInfo.buildNumber.ToString();
                    
                    Debug.Log($"Updated player settings - Version: {versionInfo.version}, Build: {versionInfo.buildNumber}");
                }
                catch (Exception e)
                {
                    Debug.LogWarning($"Failed to update player settings from version file: {e.Message}");
                }
            }
        }
    }
}
