#!/bin/bash

# iOS Simulator App Creation Script
# This script creates an app that can run on iOS Simulator

set -e

echo "📱 Starting iOS Simulator app creation..."

# Paths
IOS_BUILD_PATH="Builds/ios"
SIMULATOR_OUTPUT_PATH="Builds/ios-simulator"
APP_NAME="香域"
XCODE_PROJECT_PATH="$IOS_BUILD_PATH/Unity-iPhone.xcodeproj"

# Check if iOS build exists
if [ ! -d "$IOS_BUILD_PATH" ] || [ ! -d "$XCODE_PROJECT_PATH" ]; then
    echo "❌ Error: iOS Xcode project not found at $XCODE_PROJECT_PATH"
    echo "Please build iOS project first using Unity."
    exit 1
fi

echo "✅ Found iOS project at: $XCODE_PROJECT_PATH"

# Create output directory
mkdir -p "$SIMULATOR_OUTPUT_PATH"
echo "📁 Created output directory: $SIMULATOR_OUTPUT_PATH"

# Check available simulators
echo "🔍 Checking available iOS simulators..."
xcrun simctl list devices available | grep "iPhone"

# Get the latest iOS simulator
SIMULATOR_ID=$(xcrun simctl list devices available | grep "iPhone" | tail -1 | grep -o '[A-F0-9-]\{36\}')
if [ -z "$SIMULATOR_ID" ]; then
    echo "❌ No iPhone simulators found. Please install iOS Simulator from Xcode."
    exit 1
fi

echo "📱 Using simulator: $SIMULATOR_ID"

# Build for iOS Simulator
echo "🔨 Building for iOS Simulator..."

# Try different simulator destinations
DESTINATIONS=(
    "platform=iOS Simulator,name=iPhone 15"
    "platform=iOS Simulator,name=iPhone 14"
    "platform=iOS Simulator,name=iPhone 13"
    "generic/platform=iOS Simulator"
)

BUILD_SUCCESS=false

for dest in "${DESTINATIONS[@]}"; do
    echo "🔄 Trying destination: $dest"
    
    if xcodebuild \
        -project "$XCODE_PROJECT_PATH" \
        -scheme Unity-iPhone \
        -configuration Debug \
        -destination "$dest" \
        -derivedDataPath "$SIMULATOR_OUTPUT_PATH/DerivedData" \
        build 2>/dev/null; then
        
        echo "✅ Build successful with destination: $dest"
        BUILD_SUCCESS=true
        break
    else
        echo "❌ Build failed with destination: $dest"
    fi
done

if [ "$BUILD_SUCCESS" = false ]; then
    echo "❌ All build attempts failed. Trying manual approach..."
    
    # Manual approach: Create simulator-compatible app bundle
    echo "📦 Creating simulator app bundle manually..."
    
    APP_BUNDLE_PATH="$SIMULATOR_OUTPUT_PATH/${APP_NAME}.app"
    mkdir -p "$APP_BUNDLE_PATH"
    
    # Copy essential files for simulator
    if [ -f "$IOS_BUILD_PATH/Info.plist" ]; then
        cp "$IOS_BUILD_PATH/Info.plist" "$APP_BUNDLE_PATH/"
        echo "✅ Copied Info.plist"
    fi
    
    # Copy Data directory
    if [ -d "$IOS_BUILD_PATH/Data" ]; then
        cp -R "$IOS_BUILD_PATH/Data" "$APP_BUNDLE_PATH/"
        echo "✅ Copied Data directory"
    fi
    
    # Copy launch screens
    for file in "$IOS_BUILD_PATH"/LaunchScreen*; do
        if [ -f "$file" ]; then
            cp "$file" "$APP_BUNDLE_PATH/"
        fi
    done
    echo "✅ Copied launch screens"
    
    # Create a simple executable for simulator (this won't actually run the Unity game)
    cat > "$APP_BUNDLE_PATH/${APP_NAME}" << 'EOF'
#!/bin/bash
echo "香域 - Unity Game"
echo "This is a placeholder executable for iOS Simulator"
echo "The actual Unity game requires proper iOS build for simulator architecture"
sleep 5
EOF
    chmod +x "$APP_BUNDLE_PATH/${APP_NAME}"
    
    # Update Info.plist for simulator
    if [ -f "$APP_BUNDLE_PATH/Info.plist" ]; then
        # Add CFBundleExecutable
        /usr/libexec/PlistBuddy -c "Set :CFBundleExecutable ${APP_NAME}" "$APP_BUNDLE_PATH/Info.plist" 2>/dev/null || \
        /usr/libexec/PlistBuddy -c "Add :CFBundleExecutable string ${APP_NAME}" "$APP_BUNDLE_PATH/Info.plist"
        
        # Set bundle identifier
        /usr/libexec/PlistBuddy -c "Set :CFBundleIdentifier com.unity.${APP_NAME}" "$APP_BUNDLE_PATH/Info.plist" 2>/dev/null || \
        /usr/libexec/PlistBuddy -c "Add :CFBundleIdentifier string com.unity.${APP_NAME}" "$APP_BUNDLE_PATH/Info.plist"
        
        echo "✅ Updated Info.plist for simulator"
    fi
    
    echo "📱 Manual simulator app created at: $APP_BUNDLE_PATH"
    
else
    # Find the built app from successful build
    APP_PATH=$(find "$SIMULATOR_OUTPUT_PATH/DerivedData" -name "*.app" -type d | head -1)
    
    if [ -n "$APP_PATH" ] && [ -d "$APP_PATH" ]; then
        echo "📱 Found built app at: $APP_PATH"
        
        # Copy to output directory
        cp -R "$APP_PATH" "$SIMULATOR_OUTPUT_PATH/"
        APP_BUNDLE_PATH="$SIMULATOR_OUTPUT_PATH/$(basename "$APP_PATH")"
        
        echo "✅ Copied app to: $APP_BUNDLE_PATH"
    else
        echo "❌ Could not find built app"
        exit 1
    fi
fi

# Get app size
if [ -d "$APP_BUNDLE_PATH" ]; then
    APP_SIZE=$(du -sh "$APP_BUNDLE_PATH" | cut -f1)
    echo "📏 App size: $APP_SIZE"
fi

echo ""
echo "🎉 iOS Simulator app creation completed!"
echo "📍 Location: $APP_BUNDLE_PATH"
echo ""
echo "📱 To install and run on iOS Simulator:"
echo "1. Open iOS Simulator"
echo "2. Drag and drop the .app file to the simulator"
echo "3. Or use: xcrun simctl install booted \"$APP_BUNDLE_PATH\""
echo ""
echo "⚠️  Note: This app is built for iOS Simulator only."
echo "   It cannot be installed on physical iOS devices."

# Optionally install on booted simulator
read -p "🤔 Do you want to install the app on the currently booted simulator? (y/n): " -n 1 -r
echo
if [[ $REPLY =~ ^[Yy]$ ]]; then
    echo "📲 Installing app on simulator..."
    if xcrun simctl install booted "$APP_BUNDLE_PATH"; then
        echo "✅ App installed successfully!"
        echo "🚀 You can now launch it from the simulator home screen"
    else
        echo "❌ Failed to install app on simulator"
        echo "💡 Try manually dragging the .app file to the simulator"
    fi
fi

echo ""
echo "✨ Process completed!"
