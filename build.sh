#!/bin/bash

# Configuration
UNITY_PATH="/Applications/Unity/Hub/Editor/2022.3.62f1/Unity.app/Contents/MacOS/Unity"
PROJECT_PATH="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
LOG_FILE="$PROJECT_PATH/build.log"

# Colors
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Default values
PLATFORM="all"
VERSION=""
DEVELOPMENT=false

# Function to show help
show_help() {
    echo -e "${BLUE}Unity Multi-Platform Builder Help${NC}"
    echo ""
    echo "Usage: ./build.sh [options]"
    echo ""
    echo "Options:"
    echo "  -p, --platform <platform>   Platform to build (all, windows, mac, linux, android, ios)"
    echo "  -v, --version <version>      Version number (e.g., 1.0.1)"
    echo "  -d, --development            Enable development build"
    echo "  -h, --help                   Show this help message"
    echo ""
    echo "Examples:"
    echo "  ./build.sh                                      # Build all platforms"
    echo "  ./build.sh -p windows                           # Build Windows only"
    echo "  ./build.sh -p android -v 1.0.1                 # Build Android with version 1.0.1"
    echo "  ./build.sh --platform all --development         # Build all platforms in development mode"
    echo ""
}

# Parse command line arguments
while [[ $# -gt 0 ]]; do
    case $1 in
        -p|--platform)
            PLATFORM="$2"
            shift 2
            ;;
        -v|--version)
            VERSION="$2"
            shift 2
            ;;
        -d|--development)
            DEVELOPMENT=true
            shift
            ;;
        -h|--help)
            show_help
            exit 0
            ;;
        *)
            echo -e "${RED}Unknown option: $1${NC}"
            show_help
            exit 1
            ;;
    esac
done

echo -e "${BLUE}========================================${NC}"
echo -e "${BLUE}    Unity Multi-Platform Builder       ${NC}"
echo -e "${BLUE}========================================${NC}"
echo ""

# Check if Unity exists
if [ ! -f "$UNITY_PATH" ]; then
    echo -e "${RED}Error: Unity not found at $UNITY_PATH${NC}"
    echo "Please update UNITY_PATH in this script"
    
    # Try to find Unity automatically
    echo "Searching for Unity installations..."
    if [ -d "/Applications/Unity/Hub/Editor" ]; then
        echo "Found Unity Hub installations:"
        ls -la "/Applications/Unity/Hub/Editor/"
    fi
    
    exit 1
fi

# Make script executable if it isn't
if [ ! -x "$0" ]; then
    chmod +x "$0"
fi

# Show current settings
echo -e "${YELLOW}Build Configuration:${NC}"
echo "  Platform: $PLATFORM"
if [ -n "$VERSION" ]; then
    echo "  Version: $VERSION"
fi
echo "  Development Build: $DEVELOPMENT"
echo "  Project Path: $PROJECT_PATH"
echo "  Log File: $LOG_FILE"
echo ""

# Confirm build
read -p "Continue with build? (y/n): " -n 1 -r
echo ""
if [[ ! $REPLY =~ ^[Yy]$ ]]; then
    echo "Build cancelled."
    exit 0
fi

echo -e "${BLUE}Starting Unity build...${NC}"
echo ""

# Prepare Unity command
UNITY_CMD="$UNITY_PATH -batchmode -quit -projectPath \"$PROJECT_PATH\" -logFile \"$LOG_FILE\""

# Add method based on platform
case $PLATFORM in
    all)
        UNITY_CMD="$UNITY_CMD -executeMethod BuildTools.CommandLineBuild.BuildAll"
        ;;
    windows)
        UNITY_CMD="$UNITY_CMD -executeMethod BuildTools.CommandLineBuild.BuildWindows"
        ;;
    mac)
        UNITY_CMD="$UNITY_CMD -executeMethod BuildTools.CommandLineBuild.BuildMac"
        ;;
    linux)
        UNITY_CMD="$UNITY_CMD -executeMethod BuildTools.CommandLineBuild.BuildLinux"
        ;;
    android)
        UNITY_CMD="$UNITY_CMD -executeMethod BuildTools.CommandLineBuild.BuildAndroid"
        ;;
    ios)
        UNITY_CMD="$UNITY_CMD -executeMethod BuildTools.CommandLineBuild.BuildiOS"
        ;;
    *)
        UNITY_CMD="$UNITY_CMD -executeMethod BuildTools.CommandLineBuild.Build -platform $PLATFORM"
        ;;
esac

# Add version if specified
if [ -n "$VERSION" ]; then
    UNITY_CMD="$UNITY_CMD -version \"$VERSION\""
fi

# Add development flag if specified
if [ "$DEVELOPMENT" = true ]; then
    UNITY_CMD="$UNITY_CMD -development"
fi

# Execute Unity build
echo -e "${YELLOW}Executing: $UNITY_CMD${NC}"
echo ""

# Create log file directory if it doesn't exist
mkdir -p "$(dirname "$LOG_FILE")"

# Execute the command
eval $UNITY_CMD

# Check result
EXIT_CODE=$?
if [ $EXIT_CODE -eq 0 ]; then
    echo ""
    echo -e "${GREEN}========================================${NC}"
    echo -e "${GREEN}         BUILD SUCCESSFUL!             ${NC}"
    echo -e "${GREEN}========================================${NC}"
    echo ""
    echo "Build completed successfully for platform: $PLATFORM"
    
    if [ -d "$PROJECT_PATH/Builds" ]; then
        echo "Output directory: $PROJECT_PATH/Builds"
        echo ""
        echo -e "${YELLOW}Build contents:${NC}"
        ls -la "$PROJECT_PATH/Builds"
        
        # Show build sizes
        echo ""
        echo -e "${YELLOW}Build sizes:${NC}"
        du -sh "$PROJECT_PATH/Builds"/*
    fi
    
    # Open build folder if on macOS
    if [[ "$OSTYPE" == "darwin"* ]] && [ -d "$PROJECT_PATH/Builds" ]; then
        read -p "Open build folder? (y/n): " -n 1 -r
        echo ""
        if [[ $REPLY =~ ^[Yy]$ ]]; then
            open "$PROJECT_PATH/Builds"
        fi
    fi
else
    echo ""
    echo -e "${RED}========================================${NC}"
    echo -e "${RED}           BUILD FAILED!               ${NC}"
    echo -e "${RED}========================================${NC}"
    echo ""
    echo "Build failed with error code: $EXIT_CODE"
    echo "Check the log file for details: $LOG_FILE"
    echo ""
    
    if [ -f "$LOG_FILE" ]; then
        echo -e "${YELLOW}Last few lines of log:${NC}"
        tail -20 "$LOG_FILE"
    fi
fi

echo ""
exit $EXIT_CODE
